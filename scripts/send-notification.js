/**
 * Nova Release Notification Script
 *
 * Usage: pnpm send:release-notification <package-name> <tag> <send-grid-api-key> <webhook-uri>
 * Example: pnpm send:release-notification nova-themes prerelease XXXX XXXX
 *
 * This script sends release notifications via:
 * 1. <PERSON><PERSON> (using SendGrid)
 * 2. Teams webhook (using Adaptive Cards)
 */

const client = require('@sendgrid/mail');
const fs = require('fs');

const CONFIG = {
  STORYBOOK_URL: 'https://zealous-bay-06c2c6503.5.azurestaticapps.net/',

  EMAIL_RECIPIENTS: {
    MYSTIQUE_DEVELOPERS: {
      email: '<EMAIL>',
      name: 'Mystique Developers',
    },
    NOVA_ANNOUNCEMENTS: {
      email: '<EMAIL>',
      name: 'Nova Announcements',
    },
    NO_REPLY: {
      email: '<EMAIL>',
      name: 'Nova Team',
    },
    JOE: {
      email: '<EMAIL>',
      name: '<PERSON>',
    },
    ZOU: {
      email: '<EMAIL>',
      name: 'ZOU',
    },
  },

  PACKAGE_NAMES: {
    'nova-code-mod': '@hxnova/nova-code-mod',
    'nova-icons': '@hxnova/icons',
    'nova-themes': '@hxnova/themes',
    'nova-react-components': '@hxnova/react-components',
    'nova-templates': '@hxnova/templates',
  },

  RELEASE_PATTERNS: {
    MAJOR: /\d+\.0\.0/,
    MINOR: /\d+\.\d+\.0/,
  },
};

/**
 * Parse and validate command line arguments
 */
function parseArguments() {
  const args = process.argv.slice(2);
  const [packageName, tag = 'latest', sendGridAPI, webhookUri] = args;

  if (!packageName) {
    console.error('❌ Error: Package name is required');
    process.exit(1);
  }

  if (!sendGridAPI) {
    console.error('❌ Error: SendGrid API key is required');
    process.exit(1);
  }

  if (!webhookUri) {
    console.error('❌ Error: Webhook URI is required');
    process.exit(1);
  }

  return {
    packageName,
    tag,
    sendGridAPI,
    webhookUri,
    isLatest: tag === 'latest',
  };
}

/**
 * Get full package display name from package key
 */
function getPackageName(key) {
  const packageName = CONFIG.PACKAGE_NAMES[key];
  if (!packageName) {
    console.warn(`⚠️ Unknown package key: ${key}, using as-is`);
    return key;
  }
  return packageName;
}

/**
 * Determine the release size based on version pattern
 */
function getReleaseSize(version, isLatest) {
  if (!isLatest) return 'pre-release';
  if (CONFIG.RELEASE_PATTERNS.MAJOR.test(version)) return 'major';
  if (CONFIG.RELEASE_PATTERNS.MINOR.test(version)) return 'minor';
  return 'patch';
}

/**
 * Convert markdown ### headings to **bold** format for adaptive cards
 */
function adaptMarkdownForTeams(notes) {
  return notes.replace(/### (.+)/g, '**$1**');
}

/**
 * Convert markdown notes to HTML format for email
 */
function convertNotesToHTML(notes) {
  let html = '';
  const categoryList = notes.split('### ');

  categoryList.forEach((category) => {
    if (!category.trim()) return;

    const lineItems = category.split('* ');
    const title = lineItems[0].trim();

    if (title) {
      html += `<p><b>${title}</b></p><ul>`;
      for (let i = 1; i < lineItems.length; i++) {
        const item = lineItems[i].trim();
        if (item) {
          html += `<li>${item}</li>`;
        }
      }
      html += '</ul>';
    }
  });

  return html;
}

/**
 * Read and parse changelog data for the latest release
 */
function getLatestChangelogNotes(packageKey, tag) {
  try {
    const isLatest = tag === 'latest';
    const fileVariations = isLatest
      ? ['Changelog.md', 'CHANGELOG.md']
      : ['Changelog.prerelease.md', 'CHANGELOG.prerelease.md'];

    let data = null;
    for (const fileName of fileVariations) {
      try {
        data = fs.readFileSync(`./packages/${packageKey}/${fileName}`, { encoding: 'utf8' });
        break;
      } catch (readErr) {
        if (readErr.code === 'ENOENT' && fileName !== fileVariations[fileVariations.length - 1]) {
          continue;
        }
        throw readErr;
      }
    }

    if (!data) {
      throw new Error(`Could not find changelog file for ${packageKey} with tag ${tag}`);
    }

    const versionRegex = isLatest ? /## ([0-9\.]+?) \([0-9-]+?\)/ : /## ([0-9\.]+?-(alpha|beta).[0-9]+) \([0-9-]+?\)/;

    const versionSplitRegex = isLatest
      ? /## [0-9\.]+? \([0-9-]+?\)/
      : /## [0-9\.]+?-(?:alpha|beta).[0-9]+ \([0-9-]+?\)/;

    const versionData = data.split(versionSplitRegex)[1];
    const latestVersion = versionRegex.exec(data)[1];
    const trimLinks = versionData.replaceAll(/\[.+?\]\(.+?\)/g, '');

    return {
      package: getPackageName(packageKey),
      version: latestVersion,
      notes: trimLinks,
      size: getReleaseSize(latestVersion, isLatest),
    };
  } catch (err) {
    console.error(`❌ Error reading changelog for ${packageKey} with tag ${tag}:`);
    console.error(err);
    return null;
  }
}

/**
 * Generate email subject line for release notification
 */
function generateSubject(releaseData) {
  const prefix = releaseData.size === 'pre-release' ? '[PRE-RELEASE] ' : '';
  return `${prefix}New release of ${releaseData.package} (v${releaseData.version})`;
}

/**
 * Create email message object for SendGrid
 */
function createEmailMessage(releaseData) {
  const subject = generateSubject(releaseData);
  const contentValue = `<p>Hello Nova Community!</p>
<p>The Nova Team is pleased to announce the latest release of <b>${releaseData.package}</b> (v${releaseData.version}). This is a <b>${releaseData.size}</b> version that includes the following changes:</p>
<div style="margin-top: 16px; margin-bottom: 16px;">${convertNotesToHTML(releaseData.notes)}</div>
<p>For more information, please refer to our <a href="${CONFIG.STORYBOOK_URL}">Storybook</a> site.</p>`;

  return {
    personalizations: [
      {
        // to: [CONFIG.EMAIL_RECIPIENTS.ZOU],
        to: [CONFIG.EMAIL_RECIPIENTS.NOVA_ANNOUNCEMENTS], // Uncomment for production
        cc: [],
        // bcc: [CONFIG.EMAIL_RECIPIENTS.JOE], // Uncomment if needed
      },
    ],
    from: CONFIG.EMAIL_RECIPIENTS.NO_REPLY,
    // replyToList: [CONFIG.EMAIL_RECIPIENTS.MYSTIQUE_DEVELOPERS], // Uncomment if needed
    subject,
    content: [
      {
        type: 'text/html',
        value: contentValue,
      },
    ],
  };
}

/**
 * Create Teams adaptive card message for webhook
 */
function createTeamsMessage(releaseData) {
  const subject = generateSubject(releaseData);

  return {
    type: 'message',
    attachments: [
      {
        contentType: 'application/vnd.microsoft.card.adaptive',
        content: {
          $schema: 'http://adaptivecards.io/schemas/adaptive-card.json',
          type: 'AdaptiveCard',
          version: '1.5',
          msteams: {
            width: 'Full',
          },
          body: [
            {
              type: 'TextBlock',
              text: subject,
              size: 'large',
              weight: 'bolder',
            },
            {
              type: 'TextBlock',
              text: 'Hello Nova Community!',
              spacing: 'Medium',
            },
            {
              type: 'TextBlock',
              text: `The Nova Team is pleased to announce the latest release of **${releaseData.package}** (v${releaseData.version}). This is a **${releaseData.size}** version that includes the following changes:`,
              wrap: true,
              spacing: 'Small',
            },
            {
              type: 'TextBlock',
              text: adaptMarkdownForTeams(releaseData.notes),
              wrap: true,
              spacing: 'Medium',
            },
            {
              type: 'TextBlock',
              text: `For more information, please refer to our [Storybook](${CONFIG.STORYBOOK_URL}) site.`,
              spacing: 'Medium',
            },
          ],
        },
      },
    ],
  };
}

/**
 * Send email notification using SendGrid
 */
async function sendEmailNotification(emailMessage, sendGridAPIKey) {
  try {
    client.setApiKey(sendGridAPIKey);
    await client.send(emailMessage);
    console.log('✅ Email sent successfully');
  } catch (error) {
    console.error('❌ Failed to send email:');
    console.error(error);
    if (error.response?.body?.errors) {
      console.error(error.response.body.errors);
    }
    throw error;
  }
}

/**
 * Send Teams webhook notification
 */
async function sendTeamsNotification(teamsMessage, webhookUri) {
  try {
    const response = await fetch(webhookUri, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(teamsMessage),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    console.log('✅ Teams webhook sent successfully');
  } catch (error) {
    console.error('❌ Failed to send Teams webhook:');
    console.error(error);
    throw error;
  }
}

async function main() {
  try {
    console.log('🚀 Starting Nova release notification...');

    // Parse command line arguments
    const { packageName, tag, sendGridAPI, webhookUri } = parseArguments();
    console.log(`📦 Package: ${packageName}, Tag: ${tag}`);

    // Get release data from changelog
    const releaseData = getLatestChangelogNotes(packageName, tag);
    if (!releaseData) {
      console.error(`❌ Failed to get changelog data for ${packageName} with tag ${tag}`);
      console.error(
        `Make sure the file ./packages/${packageName}/${tag === 'latest' ? 'Changelog.md or CHANGELOG.md' : 'Changelog.prerelease.md'} exists and has the correct format.`,
      );
      process.exit(1);
    }

    console.log(`📋 Release info: ${releaseData.package} v${releaseData.version} (${releaseData.size})`);

    // Create notification messages
    const emailMessage = createEmailMessage(releaseData);
    const teamsMessage = createTeamsMessage(releaseData);

    // Send notifications in parallel
    console.log('📧 Sending notifications...');
    await Promise.all([
      sendEmailNotification(emailMessage, sendGridAPI),
      sendTeamsNotification(teamsMessage, webhookUri),
    ]);

    console.log('✅ All notifications sent successfully!');
  } catch (error) {
    console.error('❌ Script execution failed:');
    console.error(error);
    process.exit(1);
  }
}

main();
