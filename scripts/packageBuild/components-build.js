/* eslint-disable @typescript-eslint/no-var-requires */
/* eslint-disable no-console */
const { resolve, join, basename } = require('path');
const { readFile, writeFile, copy } = require('fs-extra');
const packagePath = process.cwd();
const buildFolder = './dist';
const distPath = join(packagePath, buildFolder);

const writeJson = (targetPath, obj) => writeFile(targetPath, JSON.stringify(obj, null, 2), 'utf8');

async function createPackageFile() {
  const packageData = await readFile(resolve(packagePath, './package.json'), 'utf8');
  const { ...packageOthers } = JSON.parse(packageData);
  const newPackageData = {
    ...packageOthers,
    sideEffects: false,
    module: './index.js',
    main: './cjs/index.js',
    types: './index.d.ts',
    exports: {
      '.': {
        require: './cjs/index.js',
        import: {
          types: './index.d.ts',
          default: './index.js',
        },
      },
      './TypeAugmentation': {
        require: './cjs/TypeAugmentation.js',
        import: {
          types: './TypeAugmentation.d.ts',
          default: './TypeAugmentation.js',
        },
      },
      './setup-vitest': {
        require: './cjs/consumer-vitest.setup.js',
        import: {
          types: './consumer-vitest.setup.d.ts',
          default: './consumer-vitest.setup.js',
        },
      },
      './types/*': {
        require: './cjs/types/*.js',
        import: {
          types: './types/*.d.ts',
          default: './types/*.js',
        },
      },
      './Locale/*': {
        require: './cjs/Locale/*.js',
        import: {
          types: './Locale/*.d.ts',
          default: './Locale/*.js',
        },
      },
      './*': {
        require: './cjs/*/index.js',
        import: {
          types: './*/index.d.ts',
          default: './*/index.js',
        },
      },
    },
  };

  const targetPath = resolve(distPath, './package.json');
  await writeJson(targetPath, newPackageData);
  console.log(`Created package.json in ${targetPath}`);
}

async function includeFileInBuild(file) {
  const sourcePath = resolve(packagePath, file);
  const targetPath = resolve(distPath, basename(file));
  await copy(sourcePath, targetPath);
  console.log(`Copied ${sourcePath} to ${targetPath}`);
}

async function run() {
  try {
    await createPackageFile();
    await includeFileInBuild('./README.md');
    await includeFileInBuild('./LICENSE');
    await includeFileInBuild('./CHANGELOG.md');
  } catch (err) {
    console.error(err);
    process.exit(1);
  }
}

run();
