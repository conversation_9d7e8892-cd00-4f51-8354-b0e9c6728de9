/* eslint-disable @typescript-eslint/no-var-requires */
/* eslint-disable no-console */
const { resolve, join, basename } = require('path');
const { readFile, writeFile, copy } = require('fs-extra');
const packagePath = process.cwd();
const buildFolder = './dist';
const distPath = join(packagePath, buildFolder);

const writeJson = (targetPath, obj) => writeFile(targetPath, JSON.stringify(obj, null, 2), 'utf8');

async function createPackageFile() {
  const packageData = await readFile(resolve(packagePath, './package.json'), 'utf8');
  const { ...packageOthers } = JSON.parse(packageData);
  const newPackageData = {
    ...packageOthers,
    sideEffects: false,
    main: './cjs/index.js',
    types: './index.d.ts',
    module: './index.js',
    exports: {
      '.': {
        require: './cjs/index.js',
        import: {
          types: './index.d.ts',
          default: './index.js',
        },
      },
      './colors': {
        require: './cjs/colors.js',
        import: {
          types: './colors.d.ts',
          default: './colors.js',
        },
      },
      './typography': {
        require: './cjs/typography.js',
        import: {
          types: './typography.d.ts',
          default: './typography.js',
        },
      },
      './types': {
        require: './cjs/types.js',
        import: {
          types: './types.d.ts',
          default: './types.js',
        },
      },
      './viewportTokens': {
        require: './cjs/viewportTokens.js',
        import: {
          types: './viewportTokens.d.ts',
          default: './viewportTokens.js',
        },
      },
      './sizeTokens': {
        require: './cjs/sizeTokens.js',
        import: {
          types: './sizeTokens.d.ts',
          default: './sizeTokens.js',
        },
      },
      './styles.css': './styles.css',
    },
  };

  const targetPath = resolve(distPath, './package.json');
  await writeJson(targetPath, newPackageData);
  console.log(`Created package.json in ${targetPath}`);
}

async function includeFileInBuild(file) {
  const sourcePath = resolve(packagePath, file);
  const targetPath = resolve(distPath, basename(file));
  await copy(sourcePath, targetPath);
  console.log(`Copied ${sourcePath} to ${targetPath}`);
}

async function run() {
  try {
    await createPackageFile();
    await includeFileInBuild('./styles.css');
    await includeFileInBuild('./fonts');
    await includeFileInBuild('./README.md');
    await includeFileInBuild('./LICENSE');
    await includeFileInBuild('./CHANGELOG.md');
  } catch (err) {
    console.error(err);
    process.exit(1);
  }
}

run();
