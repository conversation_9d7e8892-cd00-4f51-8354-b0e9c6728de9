import { describe, it, expect } from 'vitest';
import { zIndexValues } from './zIndex';
import prettier from 'prettier';
import transform from './transform';
import jscodeshift from 'jscodeshift';

import { colorMapping } from './colorPalette';

async function format(code) {
  // Remove leading/trailing whitespace and collapse multiple spaces/newlines
  const cleaned = code
    .replace(/\s+/g, ' ') // collapse all whitespace to single spaces
    .replace(/>\s+</g, '><') // remove whitespace between tags
    .trim();
  return await prettier.format(cleaned, { parser: 'babel-ts' });
}

function runTransform(input) {
  return transform({ source: input }, { jscodeshift }, { parser: 'tsx' });
}

describe('NexusUI -> Nova Code Mod', () => {
  describe('Borders', () => {
    it('correctly transforms borders as plain object', async () => {
      const input = '<div sx={{border: 1, borderTop: 2, borderRadius: 1, borderRadius: 5, borderRadius: 0}} />';
      const expected =
        '<div sx={nt => ({border: "1px solid", borderTop: "2px solid", borderRadius: nt.vars.sys.viewport.radius["2xs"], borderRadius: "1.25rem", borderRadius: 0})} />';
      const output = runTransform(input);
      expect(await format(output)).toBe(await format(expected));
    });
  });
  describe('Colors', () => {
    // Dynamically test all colorMapping values
    Object.entries(colorMapping).forEach(([muiKey, novaValue]) => {
      if (typeof novaValue === 'undefined') return; // skip unmapped
      it(`correctly transforms named color ("${muiKey}" to "${novaValue}")`, async () => {
        const input = `<div sx={{color: "${muiKey}", backgroundColor: "${muiKey}", borderColor: "${muiKey}"}} />`;
        let expected;
        if (
          novaValue.startsWith('var(') ||
          ['inherit', 'transparent', 'currentColor', 'none', 'white', 'black'].includes(novaValue)
        ) {
          expected = `<div sx={nt => ({color: "${novaValue}", backgroundColor: "${novaValue}", borderColor: "${novaValue}"})} />`;
        } else {
          // e.g. info, error, onPrimary, etc.
          expected = `<div sx={nt => ({color: nt.vars.palette.${novaValue}, backgroundColor: nt.vars.palette.${novaValue}, borderColor: nt.vars.palette.${novaValue}})} />`;
        }
        const output = runTransform(input);
        expect(await format(output)).toBe(await format(expected));
      });
      it(`correctly transforms members expression colors ("${muiKey}" to "${novaValue}")`, async () => {
        // Use bracket notation if the key contains a dot and the part after the dot is numeric
        let accessor;
        const parts = muiKey.split('.');
        if (parts.length === 2 && !isNaN(parts[1])) {
          accessor = `t.vars.palette.${parts[0]}[${parts[1]}]`;
        } else {
          accessor = `t.vars.palette.${muiKey}`;
        }
        const input = `<div sx={(t) => ({color: ${accessor}, backgroundColor: ${accessor}, borderColor: ${accessor}})} />`;
        let expected;
        if (
          novaValue.startsWith('var(') ||
          ['inherit', 'transparent', 'currentColor', 'none', 'white', 'black'].includes(novaValue)
        ) {
          expected = `<div sx={t => ({color: "${novaValue}", backgroundColor: "${novaValue}", borderColor: "${novaValue}"})} />`;
        } else {
          expected = `<div sx={t => ({color: t.vars.palette.${novaValue}, backgroundColor: t.vars.palette.${novaValue}, borderColor: t.vars.palette.${novaValue}})} />`;
        }
        const output = runTransform(input);
        expect(await format(output)).toBe(await format(expected));
      });
      it(`correctly transforms local function colors ("${muiKey}" to "${novaValue}")`, async () => {
        // Use bracket notation if the key contains a dot and the part after the dot is numeric
        let accessor;
        const parts = muiKey.split('.');
        if (parts.length === 2 && !isNaN(parts[1])) {
          accessor = `t.vars.palette.${parts[0]}[${parts[1]}]`;
        } else {
          accessor = `t.vars.palette.${muiKey}`;
        }
        const input = `<div sx={{color: (t) => ${accessor}, backgroundColor: (t) => ${accessor}, borderColor: (t) => ${accessor}}} />`;
        let expected;
        if (
          novaValue.startsWith('var(') ||
          ['inherit', 'transparent', 'currentColor', 'none', 'white', 'black'].includes(novaValue)
        ) {
          expected = `<div sx={nt => ({color: "${novaValue}", backgroundColor: "${novaValue}", borderColor: "${novaValue}"})} />`;
        } else {
          expected = `<div sx={nt => ({color: nt.vars.palette.${novaValue}, backgroundColor: nt.vars.palette.${novaValue}, borderColor: nt.vars.palette.${novaValue}})} />`;
        }
        const output = runTransform(input);
        expect(await format(output)).toBe(await format(expected));
      });
    });
  });
  describe('Box Shadows', () => {
    Object.entries({ 0: 0, 1: 1, 24: 5, 11: 3 }).forEach(([muiKey, novaValue]) => {
      it(`correctly transforms and interpolates boxShadows`, async () => {
        const input = `<div sx={{boxShadow: ${muiKey}}} />`;
        const expected = `<div sx={nt => ({boxShadow: nt.vars.shadow[${novaValue}]})} />`;
        const output = runTransform(input);
        expect(await format(output)).toBe(await format(expected));
      });
    });
  });
  describe('Sizes', () => {
    it('correctly transforms sizes as plain object', async () => {
      const input =
        '<div sx={{width: 100, minWidth: 0, maxWidth: 1/2, height: 0.5, minHeight: 500, maxHeight: 999/1000}} />';
      const expected =
        '<div sx={nt => ({width: 100, minWidth: 0, maxWidth: "50%", height: "50%", minHeight: 500, maxHeight: "99.9%"})} />';
      const output = runTransform(input);
      expect(await format(output)).toBe(await format(expected));
    });
    it('correctly transforms sizes in a function', async () => {
      const input =
        '<div sx={(theme) => ({width: 100, minWidth: 0, maxWidth: 1/2, height: 0.5, minHeight: 500, maxHeight: 999/1000})} />';
      const expected =
        '<div sx={theme => ({width: 100, minWidth: 0, maxWidth: "50%", height: "50%", minHeight: 500, maxHeight: "99.9%"})} />';
      const output = runTransform(input);
      expect(await format(output)).toBe(await format(expected));
    });
  });
  describe('Spaces', () => {
    it('correctly transforms spaces as plain object', async () => {
      const input =
        '<div sx={{m: 1, ml: -2, mr: 3, mb: 0, mt: 0.5, mx: 10, my: 9, p: 1, pl: 2, pr: 3, pb: 0, pt: 0.5, px: 10, py: 9}} />';
      const expected =
        '<div sx={nt => ({margin: "0.25rem", marginLeft: "-0.5rem", marginRight: "0.75rem", marginBottom: 0, marginTop: "0.125rem", marginInline: "2.5rem", marginBlock: "2.25rem", padding: "0.25rem", paddingLeft: "0.5rem", paddingRight: "0.75rem", paddingBottom: 0, paddingTop: "0.125rem", paddingInline: "2.5rem", paddingBlock: "2.25rem" })} />';
      const output = runTransform(input);
      expect(await format(output)).toBe(await format(expected));
    });
    it('correctly transforms spaces in a global function', async () => {
      const input =
        '<div sx={(t) => ({m: t.spacing(1), ml: t.spacing(-2), mr: t.spacing(3), mb: t.spacing(0), mt: t.spacing(0.5), mx: t.spacing(10), my: t.spacing(9), p: t.spacing(1), pl: t.spacing(2), pr: t.spacing(3), pb: t.spacing(0), pt: t.spacing(0.5), px: t.spacing(10), py: t.spacing(9)})} />';
      const expected =
        '<div sx={t => ({margin: "0.25rem", marginLeft: "-0.5rem", marginRight: "0.75rem", marginBottom: 0, marginTop: "0.125rem", marginInline: "2.5rem", marginBlock: "2.25rem", padding: "0.25rem", paddingLeft: "0.5rem", paddingRight: "0.75rem", paddingBottom: 0, paddingTop: "0.125rem", paddingInline: "2.5rem", paddingBlock: "2.25rem" })} />';
      const output = runTransform(input);
      expect(await format(output)).toBe(await format(expected));
    });
    it('correctly transforms spaces in a local function', async () => {
      const input =
        '<div sx={{m: (t) => t.spacing(1), ml: (t) => t.spacing(-2), mr: (t) => t.spacing(3), mb: (t) => t.spacing(0), mt: (t) => t.spacing(0.5), mx: (t) => t.spacing(10), my: (t) => t.spacing(9), p: (t) => t.spacing(1), pl: (t) => t.spacing(2), pr: (t) => t.spacing(3), pb: (t) => t.spacing(0), pt: (t) => t.spacing(0.5), px: (t) => t.spacing(10), py: (t) => t.spacing(9)}} />';
      const expected =
        '<div sx={nt => ({margin: "0.25rem", marginLeft: "-0.5rem", marginRight: "0.75rem", marginBottom: 0, marginTop: "0.125rem", marginInline: "2.5rem", marginBlock: "2.25rem", padding: "0.25rem", paddingLeft: "0.5rem", paddingRight: "0.75rem", paddingBottom: 0, paddingTop: "0.125rem", paddingInline: "2.5rem", paddingBlock: "2.25rem" })} />';
      const output = runTransform(input);
      expect(await format(output)).toBe(await format(expected));
    });
  });
  describe('Positions', () => {
    Object.entries(zIndexValues).forEach(([muiKey, novaValue]) => {
      it(`correctly transforms named zIndex values`, async () => {
        const input = `<div sx={{zIndex: '${muiKey}'}} />`;
        const expected = `<div sx={nt => ({zIndex: ${novaValue}})} />`;
        const output = runTransform(input);
        expect(await format(output)).toBe(await format(expected));
      });

      it(`correctly transforms zIndex values in expression`, async () => {
        const input = `<div sx={{zIndex: (nt) => nt.zIndex.${muiKey} + 1}} />`;
        const expected = `<div sx={nt => ({zIndex: ${novaValue + 1}})} />`;
        const output = runTransform(input);
        expect(await format(output)).toBe(await format(expected));
      });
    });
  });
  describe('Responsive Styles', () => {
    it('correctly transforms responsive styles as plain object', async () => {
      const input = `<div
    sx={{
      m: { xs: 0, sm: 1 },
      mt: 2,
      zIndex: (th) => th.zIndex.drawer + 1,
      color: 'text.primary',

      '& .otherClass': {
        m: 1,
        mt: { xs: 0, sm: 2 },
        backgroundColor: (th) => th.palette.primary.main,
      },
    }}
  />`;
      const expected = `  <div
    sx={nt => ({
      margin: 0,
      marginTop: "0.5rem",
      zIndex: 1201,
      color: nt.vars.palette.onSurface,

      ['& .otherClass']: {
        margin: "0.25rem",
        marginTop: 0,
        backgroundColor: nt.vars.palette.primary,

        [nt.breakpoints.up("sm")]: {
          marginTop: "0.5rem"
        }
      },

      [nt.breakpoints.up("sm")]: {
        margin: "0.25rem"
      }
    })}
  />`;
      const output = runTransform(input);
      expect(await format(output)).toBe(await format(expected));
    });
  });
});
