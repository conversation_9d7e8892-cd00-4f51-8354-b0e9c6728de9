/**
 * Updates 'border' and 'borderRadius' properties in style objects.
 * - For 'border' properties: Converts numeric values to CSS border strings (e.g., 2 -> "2px solid").
 * - For 'borderRadius': Maps numeric values to theme tokens or rem values.
 *
 * @param {Node} prop - The property node to update.
 * @param {object} j - jscodeshift API.
 * @param {string} themeArg - The theme argument name (default: 'th').
 */
const updateBorderValue = (prop, j, themeArg = 'th') => {
  // Get the property name, supporting both Identifier and Literal keys
  const keyName = prop.key.name || prop.key.value;

  // Only process border-related properties
  if (!['border', 'borderTop', 'borderLeft', 'borderRight', 'borderBottom', 'borderRadius'].includes(keyName)) {
    return;
  }

  // --- Handle 'border' and directional border properties ---
  // If the property is a border and its value is a number, convert it to a CSS string (e.g., "2px solid")
  if (
    (prop.type === 'ObjectProperty' || prop.type === 'Property') &&
    ['border', 'borderTop', 'borderLeft', 'borderRight', 'borderBottom'].includes(keyName)
  ) {
    if (
      (prop.value.type === 'Literal' || prop.value.type === 'NumericLiteral') &&
      typeof prop.value.value === 'number'
    ) {
      prop.value = j.literal(`${prop.value.value}px solid`);
    }
  }

  // --- Handle 'borderRadius' property ---
  // If the property is 'borderRadius' and its value is a number, map it to a theme token or rem value
  if ((prop.type === 'ObjectProperty' || prop.type === 'Property') && keyName === 'borderRadius') {
    if (
      (prop.value.type === 'Literal' || prop.value.type === 'NumericLiteral') &&
      typeof prop.value.value === 'number'
    ) {
      switch (prop.value.value) {
        case 1:
          // Map 1 to theme token: th.vars.sys.viewport.radius["2xs"]
          prop.value = j.memberExpression(
            j.memberExpression(
              j.memberExpression(
                j.memberExpression(
                  j.memberExpression(j.identifier(themeArg), j.identifier('vars')),
                  j.identifier('sys'),
                ),
                j.identifier('viewport'),
              ),
              j.identifier('radius'),
            ),
            j.literal('2xs'),
            true,
          );
          break;
        case 2:
          // Map 2 to theme token: th.vars.sys.viewport.radius["xs"]
          prop.value = j.memberExpression(
            j.memberExpression(
              j.memberExpression(
                j.memberExpression(
                  j.memberExpression(j.identifier(themeArg), j.identifier('vars')),
                  j.identifier('sys'),
                ),
                j.identifier('viewport'),
              ),
              j.identifier('radius'),
            ),
            j.literal('xs'),
            true,
          );
          break;
        case 3:
          // Map 3 to theme token: th.vars.sys.viewport.radius["sm"]
          prop.value = j.memberExpression(
            j.memberExpression(
              j.memberExpression(
                j.memberExpression(
                  j.memberExpression(j.identifier(themeArg), j.identifier('vars')),
                  j.identifier('sys'),
                ),
                j.identifier('viewport'),
              ),
              j.identifier('radius'),
            ),
            j.literal('sm'),
            true,
          );
          break;
        case 4:
          // Map 4 to theme token: th.vars.sys.viewport.radius["md"]
          prop.value = j.memberExpression(
            j.memberExpression(
              j.memberExpression(
                j.memberExpression(
                  j.memberExpression(j.identifier(themeArg), j.identifier('vars')),
                  j.identifier('sys'),
                ),
                j.identifier('viewport'),
              ),
              j.identifier('radius'),
            ),
            j.literal('md'),
            true,
          );
          break;
        default:
          // For other numbers: use 0 if zero, otherwise convert to rem units (e.g., 5 -> "1.25rem")
          prop.value = prop.value.value === 0 ? j.literal(0) : j.literal(`${prop.value.value * 0.25}rem`);
          break;
      }
    }
  }
};

module.exports = updateBorderValue;
