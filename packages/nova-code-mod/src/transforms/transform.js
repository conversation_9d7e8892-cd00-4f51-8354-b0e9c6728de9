/* eslint-disable @typescript-eslint/no-var-requires */
// Utility functions for parsing and identifying sx prop shapes
const { getSxAttribute, isObjectLiteral, isFunctionReturningObjectLiteral } = require('../utils/parseSx');

// Individual update functions for different style categories
const updateBorderValue = require('./border');
const updateColorValue = require('./colorPalette');
const updateShadowValue = require('./shadow');
const updateSizeValue = require('./sizing');
const updateSpacingValue = require('./spacing');
const updateZIndexValue = require('./zIndex');

/**
 * Applies all style update functions to a given property node.
 * @param {Node} prop - The property node to update.
 * @param {object} j - jscodeshift API.
 * @param {string} themeArg - The theme argument name (default: 'nt').
 */
function updateValues(prop, j, themeArg = 'nt') {
  updateBorderValue(prop, j, themeArg);
  updateSpacingValue(prop, j, themeArg);
  updateColorValue(prop, j, themeArg);
  updateZIndexValue(prop, j, themeArg);
  updateShadowValue(prop, j, themeArg);
  updateSizeValue(prop, j, themeArg);
}

/**
 * Converts responsive style objects in the `sx` prop to use theme breakpoints.
 * Handles both object literals and functions returning object literals.
 * @param {Node} sxAttr - The sx attribute node.
 * @param {object} j - jscodeshift API.
 * @param {string} themeArg - The theme argument name (default: 'nt').
 */
function convertStyles(sxAttr, j, themeArg = 'nt') {
  // Handle sx as an object literal
  if (isObjectLiteral(sxAttr)) {
    sxAttr.value.expression = transformSxResponsiveAST(sxAttr.value.expression, j);

    // Wrap the object in an arrow function: (nt) => ({ ... })
    sxAttr.value = j.jsxExpressionContainer(
      j.arrowFunctionExpression(
        [j.identifier(themeArg)],
        j.parenthesizedExpression(j.objectExpression(sxAttr.value.expression.properties)),
      ),
    );
  }
  // Handle sx as a function (arrow or function expression)
  else if (isFunctionReturningObjectLiteral(sxAttr)) {
    const expr = sxAttr.value.expression;
    // Block body: (t) => { return { ... } }
    if (expr.body && expr.body.type === 'BlockStatement') {
      const returnStmt = expr.body.body.find(
        (stmt) => stmt.type === 'ReturnStatement' && stmt.argument && stmt.argument.type === 'ObjectExpression',
      );
      if (returnStmt && returnStmt.argument) {
        // Transform the returned object expression
        returnStmt.argument = transformSxResponsiveAST(returnStmt.argument, j, sxAttr.value.expression.params[0]?.name);
      }
    }
    // Concise body: (t) => ({ ... })
    else if (expr.body && expr.body.type === 'ObjectExpression') {
      expr.body = transformSxResponsiveAST(expr.body, j, sxAttr.value.expression.params[0]?.name);
    }
  }
}

/**
 * Main codemod transform function.
 * Finds all JSX elements with an 'sx' prop and transforms their responsive styles.
 * @param {object} fileInfo - File information from jscodeshift.
 * @param {object} api - jscodeshift API.
 * @returns {string} - The transformed source code.
 */
function baseTransform(fileInfo, api) {
  const j = api.jscodeshift;
  const root = j(fileInfo.source);

  // Target any JSX element with an 'sx' prop
  root.find(j.JSXElement).forEach((path) => {
    const sxAttr = getSxAttribute(path);

    // If no 'sx' prop is found, skip this element
    if (!sxAttr) {
      return;
    }

    // Convert responsive styles in the `sx` prop
    convertStyles(sxAttr, j);
  });
  return root.toSource();
}

/**
 * Recursively transforms an ObjectExpression representing sx styles,
 * converting responsive breakpoint objects into theme-based media queries.
 * @param {Node} objectExpression - The object expression node to transform.
 * @param {object} j - jscodeshift API.
 * @param {string} themeArg - The theme argument name (default: 'nt').
 * @returns {Node} - The transformed object expression.
 */
function transformSxResponsiveAST(objectExpression, j, themeArg = 'nt') {
  const BREAKPOINTS = ['xs', 'sm', 'md', 'lg', 'xl'];
  const props = [];
  const responsiveCache = {};

  objectExpression.properties.forEach((prop) => {
    // Check if property is a responsive object (e.g., { xs: ..., md: ... })
    if (
      (prop.type === 'ObjectProperty' || prop.type === 'Property') &&
      prop.value.type === 'ObjectExpression' &&
      prop.value.properties.some(
        (p) =>
          (p.type === 'ObjectProperty' || p.type === 'Property') &&
          ((p.key.type === 'Identifier' && BREAKPOINTS.includes(p.key.name)) ||
            (p.key.type === 'Literal' && BREAKPOINTS.includes(p.key.value))),
      )
    ) {
      // For each breakpoint, create a new property or media query
      prop.value.properties.forEach((bpProp) => {
        const bp = bpProp.key.type === 'Identifier' ? bpProp.key.name : bpProp.key.value;
        // Create a new property node for the media query
        const mqProp = j.property('init', prop.key, bpProp.value);
        // Update the new property node (so updateSpacingValue, etc. work)
        updateValues(mqProp, j, themeArg);
        if (bp === 'xs') {
          // xs is the base style, not wrapped in a media query
          props.push(mqProp);
        } else {
          // Other breakpoints are wrapped in a theme media query
          const mq = `\${${themeArg}.breakpoints.up("${bp}")}`;
          if (!responsiveCache[mq]) {
            responsiveCache[mq] = [];
          }
          responsiveCache[mq].push(mqProp);
        }
      });
    } else if ((prop.type === 'ObjectProperty' || prop.type === 'Property') && prop.value.type === 'ObjectExpression') {
      // Recursively process nested objects
      const newProperty = j.property('init', prop.key, transformSxResponsiveAST(prop.value, j, themeArg));
      newProperty.computed = true; // Keep computed if original was computed
      props.push(newProperty);
    } else {
      // Apply style updates to non-responsive properties
      updateValues(prop, j, themeArg);
      props.push(prop);
    }
  });

  // Attach responsive media queries at the end of the object
  Object.entries(responsiveCache).forEach(([mq, mqProps]) => {
    const mediaObject = j.objectProperty(
      j.callExpression(
        j.memberExpression(j.memberExpression(j.identifier(themeArg), j.identifier('breakpoints')), j.identifier('up')),
        [j.literal(mq.match(/up\("(.+)"\)/)[1])],
      ),
      j.objectExpression(mqProps),
      true, // this isn't working so we manually set computed below
    );
    mediaObject.computed = true;
    props.push(mediaObject);
  });

  return j.objectExpression(props);
}

// Set parser to TSX for compatibility with JSX/TSX files
baseTransform.parser = 'tsx';

module.exports = baseTransform;
