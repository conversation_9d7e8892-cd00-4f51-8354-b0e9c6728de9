/**
 * Updates the 'boxShadow' property in style objects.
 * - Converts numeric boxShadow values to theme-based shadow tokens.
 * - Maps the value to a theme variable (e.g., th.vars.shadow[1]).
 *
 * @param {Node} prop - The property node to update.
 * @param {object} j - jscodeshift API.
 * @param {string} themeArg - The theme argument name (default: 'th').
 */
const updateShadowValue = (prop, j, themeArg = 'th') => {
  // Get the property name, supporting both Identifier and Literal keys
  const keyName = prop.key.name || prop.key.value;

  // Only process 'boxShadow' property
  if (keyName !== 'boxShadow') {
    return;
  }

  // Ensure the property is an object property
  if (prop.type === 'ObjectProperty' || prop.type === 'Property') {
    // If the value is a numeric literal, map it to a theme shadow token
    if (
      (prop.value.type === 'Literal' || prop.value.type === 'NumericLiteral') &&
      typeof prop.value.value === 'number'
    ) {
      const n = prop.value.value;
      // Map boxShadow number to a theme shadow token:
      // - 0 stays 0
      // - Other numbers are grouped by 5 (e.g., 6 -> 2)
      const mapped = n === 0 ? 0 : Math.ceil(n / 5);
      // Set the value to the corresponding theme variable, e.g., th.vars.shadow[1]
      prop.value = j.memberExpression(
        j.memberExpression(j.memberExpression(j.identifier(themeArg), j.identifier('vars')), j.identifier('shadow')),
        j.literal(mapped),
        true, // Use computed property access: th.vars.shadow[<mapped>]
      );
    }
  }
};

module.exports = updateShadowValue;
