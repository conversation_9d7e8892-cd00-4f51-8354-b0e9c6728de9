// List of style properties that are color-related and should be processed
const colorProperties = [
  'color',
  'border',
  'borderTop',
  'borderBottom',
  'borderLeft',
  'borderRight',
  'borderColor',
  'backgroundColor',
  'bgcolor',
];

// List of standard CSS color values that should be left as-is
const standardValues = ['inherit', 'transparent', 'currentColor', 'none', 'white', 'black'];

// Mapping from legacy palette keys to new design tokens or CSS variables
const colorMapping = {
  // --- Common ---
  'common.white': 'white',
  'common.black': 'black',

  // --- Primary ---
  'primary.main': 'primary',
  'primary.light': 'primaryContainer',
  'primary.contrastText': 'onPrimary',
  'primary.dark': 'var(--color-brandBlue-10)',
  'primary.50': 'var(--color-brandBlue-95)',
  'primary.100': 'var(--color-brandBlue-90)',
  'primary.200': 'var(--color-brandBlue-80)',
  'primary.300': 'var(--color-brandBlue-70)',
  'primary.400': 'var(--color-brandBlue-60)',
  'primary.500': 'var(--color-brandBlue-50)',
  'primary.600': 'var(--color-brandBlue-40)',
  'primary.700': 'var(--color-brandBlue-30)',
  'primary.800': 'var(--color-brandBlue-20)',
  'primary.900': 'var(--color-brandBlue-10)',

  // --- Secondary ---
  'secondary.main': 'secondary',
  'secondary.light': 'secondaryContainer',
  'secondary.contrastText': 'onSecondary',
  'secondary.dark': 'var(--color-brandGreen-10)',
  'secondary.50': 'var(--color-brandGreen-95)',
  'secondary.100': 'var(--color-brandGreen-90)',
  'secondary.200': 'var(--color-brandGreen-80)',
  'secondary.300': 'var(--color-brandGreen-70)',
  'secondary.400': 'var(--color-brandGreen-60)',
  'secondary.500': 'var(--color-brandGreen-50)',
  'secondary.600': 'var(--color-brandGreen-40)',
  'secondary.700': 'var(--color-brandGreen-30)',
  'secondary.800': 'var(--color-brandGreen-20)',
  'secondary.900': 'var(--color-brandGreen-10)',

  // --- Info ---
  'info.main': 'info',
  'info.light': 'infoContainer',
  'info.contrastText': 'onInfo',
  'info.dark': 'var(--color-blue-10)',
  'info.50': 'var(--color-blue-95)',
  'info.100': 'var(--color-blue-90)',
  'info.200': 'var(--color-blue-80)',
  'info.300': 'var(--color-blue-70)',
  'info.400': 'var(--color-blue-60)',
  'info.500': 'var(--color-blue-50)',
  'info.600': 'var(--color-blue-40)',
  'info.700': 'var(--color-blue-30)',
  'info.800': 'var(--color-blue-20)',
  'info.900': 'var(--color-blue-10)',

  // --- Success ---
  'success.main': 'success',
  'success.light': 'successContainer',
  'success.contrastText': 'onSuccess',
  'success.dark': 'var(--color-green-10)',
  'success.50': 'var(--color-green-95)',
  'success.100': 'var(--color-green-90)',
  'success.200': 'var(--color-green-80)',
  'success.300': 'var(--color-green-70)',
  'success.400': 'var(--color-green-60)',
  'success.500': 'var(--color-green-50)',
  'success.600': 'var(--color-green-40)',
  'success.700': 'var(--color-green-30)',
  'success.800': 'var(--color-green-20)',
  'success.900': 'var(--color-green-10)',

  // --- Warning ---
  'warning.main': 'warning',
  'warning.light': 'warningContainer',
  'warning.contrastText': 'onWarning',
  'warning.dark': 'var(--color-orange-30)',
  'warning.50': 'var(--color-orange-95)',
  'warning.100': 'var(--color-orange-90)',
  'warning.200': 'var(--color-orange-80)',
  'warning.300': 'var(--color-orange-70)',
  'warning.400': 'var(--color-orange-60)',
  'warning.500': 'var(--color-orange-50)',
  'warning.600': 'var(--color-orange-40)',
  'warning.700': 'var(--color-orange-30)',
  'warning.800': 'var(--color-orange-20)',
  'warning.900': 'var(--color-orange-10)',

  // --- Error ---
  'error.main': 'error',
  'error.light': 'errorContainer',
  'error.contrastText': 'onError',
  'error.dark': 'var(--color-red-30)',
  'error.50': 'var(--color-red-95)',
  'error.100': 'var(--color-red-90)',
  'error.200': 'var(--color-red-80)',
  'error.300': 'var(--color-red-70)',
  'error.400': 'var(--color-red-60)',
  'error.500': 'var(--color-red-50)',
  'error.600': 'var(--color-red-40)',
  'error.700': 'var(--color-red-30)',
  'error.800': 'var(--color-red-20)',
  'error.900': 'var(--color-red-10)',

  // --- Grey ---
  'grey.50': 'var(--color-grey-95)',
  'grey.100': 'var(--color-grey-90)',
  'grey.200': 'var(--color-grey-80)',
  'grey.300': 'var(--color-grey-70)',
  'grey.400': 'var(--color-grey-60)',
  'grey.500': 'var(--color-grey-50)',
  'grey.600': 'var(--color-grey-40)',
  'grey.700': 'var(--color-grey-30)',
  'grey.800': 'var(--color-grey-20)',
  'grey.900': 'var(--color-grey-10)',

  // --- Background, divider, text, action ---
  'background.default': 'inherit',
  'background.paper': 'surface',
  divider: 'outline',
  'text.primary': 'onSurface',
  'text.secondary': 'onSurfaceVariant',
  'text.disabled': 'onBackgroundDisabled',
  'action.active': undefined, // No direct mapping
  'action.hover': undefined, // No direct mapping
  'action.selected': undefined, // No direct mapping
  'action.disabled': 'onBackgroundDisabled',
  'action.disabledBackground': undefined, // No direct mapping
  'action.focus': undefined, // No direct mapping
};

/**
 * Helper to extract the palette key from a MemberExpression AST node.
 * For example, theme.palette.primary.main -> "primary.main"
 * Only returns palette keys from theme.palette or theme.vars.palette.
 * @param {Node} expr - The MemberExpression node.
 * @param {string} themeArg - The theme argument name (default: 'th').
 * @returns {string|null} - The palette key or null if not found.
 */
function extractColorKey(expr, themeArg = 'th') {
  let segments = [];
  let current = expr;
  while (current) {
    if (current.property) {
      if (
        current.computed &&
        (current.property.type === 'Literal' ||
          current.property.type === 'NumericLiteral' ||
          current.property.type === 'StringLiteral')
      ) {
        segments.unshift(String(current.property.value));
      } else if (current.property.name) {
        segments.unshift(current.property.name);
      }
    }
    if (current.object && current.object.type === 'MemberExpression') {
      current = current.object;
    } else if (current.object && current.object.name) {
      segments.unshift(current.object.name);
      break;
    } else {
      break;
    }
  }
  // Only return palette keys from theme.palette or theme.vars.palette
  if (
    (segments[0] === 'theme' || segments[0] === themeArg) &&
    (segments[1] === 'palette' || (segments[1] === 'vars' && segments[2] === 'palette'))
  ) {
    const rest = segments.slice(segments[1] === 'palette' ? 2 : 3);
    if (rest.length === 1) {
      return rest[0];
    }
    if (rest.length === 2) {
      return `${rest[0]}.${rest[1]}`;
    }
  }
  return null;
}

/**
 * Helper: transform theme.palette.* in an expression to new mapping.
 * If a mapping exists, returns the mapped value as a literal or a member expression.
 * If no mapping exists, returns the original expression.
 * @param {Node} expr - The MemberExpression node.
 * @param {object} j - jscodeshift API.
 * @param {string} themeArg - The theme argument name.
 * @returns {Node} - The transformed node.
 */
function transformThemePaletteAccess(expr, j, themeArg = 'th') {
  const colorKey = extractColorKey(expr, themeArg);
  if (colorKey && Object.prototype.hasOwnProperty.call(colorMapping, colorKey)) {
    const mapped = colorMapping[colorKey];
    if (typeof mapped === 'string') {
      // Always map to the literal if it's a standard value or a var(...)
      if (mapped.startsWith('var(') || standardValues.includes(mapped)) {
        return j.literal(mapped);
      } else {
        // Map to theme.vars.palette.* for token mappings
        const path = mapped.split('.');
        let newExpr = j.memberExpression(
          j.memberExpression(j.identifier(themeArg), j.identifier('vars')),
          j.identifier('palette'),
        );
        path.forEach((segment) => {
          newExpr = j.memberExpression(newExpr, j.identifier(segment));
        });
        return newExpr;
      }
    }
    // If mapped is undefined, return the original expression (no mapping)
    return expr;
  }
  return expr;
}

/**
 * Recursively transform expressions in template literals and binary/call expressions.
 * Handles nested expressions and applies color mapping where appropriate.
 * @param {Node} node - The AST node to transform.
 * @param {object} j - jscodeshift API.
 * @param {string} themeArg - The theme argument name.
 * @param {Node} propNode - The property node (optional).
 * @returns {Node} - The transformed node.
 */
function transformColorExpressions(node, j, themeArg = 'th', propNode = null) {
  if (!node) return node;
  if (node.type === 'TemplateLiteral') {
    return j.templateLiteral(
      node.quasis,
      node.expressions.map((expr) => transformColorExpressions(expr, j, themeArg, propNode)),
    );
  }
  if (node.type === 'BinaryExpression') {
    return j.binaryExpression(
      node.operator,
      transformColorExpressions(node.left, j, themeArg, propNode),
      transformColorExpressions(node.right, j, themeArg, propNode),
    );
  }
  if (node.type === 'MemberExpression') {
    // Always attempt to map the palette access
    return transformThemePaletteAccess(node, j, themeArg, propNode);
  }
  if (node.type === 'CallExpression') {
    return j.callExpression(
      transformColorExpressions(node.callee, j, themeArg, propNode),
      node.arguments.map((arg) => transformColorExpressions(arg, j, themeArg, propNode)),
    );
  }
  return node;
}

/**
 * Main function to update a color property value in an object property AST node.
 * Handles string literals, member expressions, arrow functions, template literals, and call expressions.
 * Adds a comment for call expressions that require manual transformation.
 * @param {Node} prop - The property node to update.
 * @param {object} j - jscodeshift API.
 * @param {string} themeArg - The theme argument name.
 */
const updateColorValue = (prop, j, themeArg = 'th') => {
  // Get the property key name (e.g., 'color', 'bgcolor', etc.)
  const keyName = prop.key.name || prop.key.value;
  if (!colorProperties.includes(keyName)) {
    return;
  }

  // Only process object properties
  if (prop.type === 'ObjectProperty' || prop.type === 'Property') {
    // Normalize 'bgcolor' to 'backgroundColor'
    if (keyName === 'bgcolor') {
      prop.key = j.identifier('backgroundColor');
    }

    // Handle template literals and binary expressions for color values
    if (prop.value && (prop.value.type === 'TemplateLiteral' || prop.value.type === 'BinaryExpression')) {
      // Recursively transform color expressions within the value
      prop.value = transformColorExpressions(prop.value, j, themeArg, prop);

      // If all expressions in the template literal are string literals, flatten to a single string literal
      if (prop.value.type === 'TemplateLiteral') {
        const allLiteral = prop.value.expressions.every(
          (expr) => expr.type === 'Literal' || expr.type === 'StringLiteral',
        );
        if (allLiteral) {
          let str = '';
          for (let i = 0; i < prop.value.quasis.length; i++) {
            str += prop.value.quasis[i].value.cooked;
            if (i < prop.value.expressions.length) {
              str += prop.value.expressions[i].value;
            }
          }
          prop.value = j.literal(str);
        }
      }
      return;
    }

    // Handle CallExpression values (e.g., theme.palette.primary.main())
    // Add a comment if the mapping is undefined, 'inherit', or a CSS variable
    if (prop.value && prop.value.type === 'CallExpression') {
      const callee = prop.value.callee;
      let shouldComment = false;
      let colorKey, mapped;

      // Case 1: callee is a MemberExpression (e.g., theme.palette.primary.main())
      if (callee.type === 'MemberExpression') {
        colorKey = extractColorKey(callee, themeArg);
        mapped = colorMapping[colorKey];
        if (mapped === undefined || mapped === 'inherit' || (typeof mapped === 'string' && mapped.startsWith('var('))) {
          shouldComment = true;
        }
      }

      // Case 2: callee is an Identifier (e.g., alpha(theme.vars.palette.divider, 0.5))
      //         and first argument is a MemberExpression
      if (
        callee.type === 'Identifier' &&
        prop.value.arguments &&
        prop.value.arguments[0] &&
        prop.value.arguments[0].type === 'MemberExpression'
      ) {
        colorKey = extractColorKey(prop.value.arguments[0], themeArg);
        if (colorKey) {
          shouldComment = true;
        }
      }

      // Add a TODO comment for manual transformation if needed
      if (shouldComment) {
        prop.comments = (prop.comments || []).concat([
          j.commentLine(` TODO (NOVAMOD): this line requires manual transformation`, false, true),
        ]);
      }
      // Leave the value as-is for manual review
      return;
    }

    // Handle string literal color values (e.g., 'primary.main')
    if (
      (prop.value.type === 'Literal' || prop.value.type === 'StringLiteral') &&
      typeof prop.value.value === 'string'
    ) {
      const colorKey = prop.value.value;
      const mapped = colorMapping[colorKey];
      if (typeof mapped === 'string') {
        // If mapped to a CSS variable or standard value, use a literal
        if (mapped.startsWith('var(') || standardValues.includes(mapped)) {
          prop.value = j.literal(mapped);
        } else {
          // Otherwise, map to theme.vars.palette.* for token mappings
          const path = mapped.split('.');
          let expr = j.memberExpression(
            j.memberExpression(j.identifier(themeArg), j.identifier('vars')),
            j.identifier('palette'),
          );
          path.forEach((segment) => {
            expr = j.memberExpression(expr, j.identifier(segment));
          });
          prop.value = expr;
        }
      }
      // If mapped is undefined, do nothing (no direct mapping)
    }
    // Handle MemberExpression like theme.palette.error.main or theme.vars.palette.error.main
    else if (prop.value.type === 'MemberExpression') {
      prop.value = transformThemePaletteAccess(prop.value, j, themeArg, prop);
    }
    // Handle ArrowFunctionExpression like (theme) => theme.palette.primary.main
    else if (prop.value.type === 'ArrowFunctionExpression') {
      const body = prop.value.body;
      const paramName = prop.value.params[0]?.name || themeArg;
      // If the body is a MemberExpression, attempt to map it
      if (body.type === 'MemberExpression') {
        const colorKey = extractColorKey(body, paramName);

        if (colorKey && colorMapping[colorKey]) {
          const mapped = colorMapping[colorKey];
          if (typeof mapped === 'string') {
            if (mapped.startsWith('var(') || standardValues.includes(mapped)) {
              prop.value = j.literal(mapped);
            } else {
              const path = mapped.split('.');
              let expr = j.memberExpression(
                j.memberExpression(j.identifier(themeArg), j.identifier('vars')),
                j.identifier('palette'),
              );
              path.forEach((segment) => {
                expr = j.memberExpression(expr, j.identifier(segment));
              });
              prop.value = expr;
            }
          }
        }
      } else if (body.type === 'TemplateLiteral' || body.type === 'BinaryExpression') {
        // Recursively transform template literals or binary expressions in arrow function bodies
        let newBody = transformColorExpressions(body, j, paramName, prop);

        // Replace all uses of paramName with themeArg in the newBody
        newBody = replaceParamWithThemeArg(newBody, paramName, themeArg, j);

        // If all expressions in the template literal are string literals or member expressions using themeArg, flatten if possible
        if (newBody.type === 'TemplateLiteral') {
          const allSimple = newBody.expressions.every(
            (expr) =>
              expr.type === 'Literal' ||
              expr.type === 'StringLiteral' ||
              (expr.type === 'MemberExpression' &&
                expr.object.type === 'MemberExpression' &&
                expr.object.object.name === themeArg),
          );
          if (allSimple) {
            // If all expressions are string literals, flatten to a string
            const allLiteral = newBody.expressions.every(
              (expr) => expr.type === 'Literal' || expr.type === 'StringLiteral',
            );
            if (allLiteral) {
              let str = '';
              for (let i = 0; i < newBody.quasis.length; i++) {
                str += newBody.quasis[i].value.cooked;
                if (i < newBody.expressions.length) {
                  str += newBody.expressions[i].value;
                }
              }
              prop.value = j.literal(str);
              return;
            }
            // Otherwise, just use the template literal directly
            prop.value = newBody;
            return;
          }
        }
        // If the result is a template literal with no expressions, or a literal, replace the arrow function
        if (
          (newBody.type === 'TemplateLiteral' && newBody.expressions.length === 0) ||
          newBody.type === 'Literal' ||
          newBody.type === 'StringLiteral'
        ) {
          let value = newBody;
          if (newBody.type === 'TemplateLiteral') {
            value = j.literal(newBody.quasis.map((q) => q.value.cooked).join(''));
          }
          prop.value = value;
        } else {
          // If the parameter is no longer used, replace the arrow function with the template literal
          if (!isParamUsed(newBody, paramName)) {
            prop.value = newBody;
          } else {
            prop.value.body = newBody;
          }
        }
      }
    }
  }
};

/**
 * Helper to replace all identifiers matching the old param with themeArg in an AST node.
 * Used to normalize parameter names in arrow functions.
 * @param {Node} node - The AST node.
 * @param {string} oldParam - The old parameter name.
 * @param {string} themeArg - The new theme argument name.
 * @param {object} j - jscodeshift API.
 * @returns {Node} - The transformed node.
 */
function replaceParamWithThemeArg(node, oldParam, themeArg, j) {
  if (!node) return node;
  if (node.type === 'Identifier' && node.name === oldParam) {
    return j.identifier(themeArg);
  }
  // Recursively replace in MemberExpression
  if (node.type === 'MemberExpression') {
    return j.memberExpression(
      replaceParamWithThemeArg(node.object, oldParam, themeArg, j),
      node.property,
      node.computed,
    );
  }
  // Recursively replace in CallExpression
  if (node.type === 'CallExpression') {
    return j.callExpression(
      replaceParamWithThemeArg(node.callee, oldParam, themeArg, j),
      node.arguments.map((arg) => replaceParamWithThemeArg(arg, oldParam, themeArg, j)),
    );
  }
  // Recursively replace in TemplateLiteral
  if (node.type === 'TemplateLiteral') {
    return j.templateLiteral(
      node.quasis,
      node.expressions.map((expr) => replaceParamWithThemeArg(expr, oldParam, themeArg, j)),
    );
  }
  // Recursively replace in BinaryExpression
  if (node.type === 'BinaryExpression') {
    return j.binaryExpression(
      node.operator,
      replaceParamWithThemeArg(node.left, oldParam, themeArg, j),
      replaceParamWithThemeArg(node.right, oldParam, themeArg, j),
    );
  }
  return node;
}

/**
 * Check if a parameter is used within a node.
 * Used to determine if an arrow function parameter is still referenced after transformation.
 * @param {Node} node - The AST node.
 * @param {string} paramName - The parameter name to check.
 * @returns {boolean} - True if the parameter is used, false otherwise.
 */
function isParamUsed(node, paramName) {
  let found = false;
  function search(n) {
    if (!n || found) return;
    if (n.type === 'Identifier' && n.name === paramName) {
      found = true;
      return;
    }
    for (const key in n) {
      if (Object.prototype.hasOwnProperty.call(n, key)) {
        const val = n[key];
        if (Array.isArray(val)) {
          val.forEach((child) => search(child));
        } else if (val && typeof val === 'object') {
          search(val);
        }
      }
    }
  }
  search(node);
  return found;
}

module.exports = updateColorValue;
module.exports.colorMapping = colorMapping;
