// Mapping of named zIndex keys to their numeric values, matching Material UI conventions
const zIndexValues = {
  mobileStepper: 1000,
  fab: 1050,
  speedDial: 1050,
  appBar: 1100,
  drawer: 1200,
  modal: 1300,
  snackbar: 1400,
  tooltip: 1500,
};

/**
 * Extracts a numeric zIndex value from a MemberExpression node.
 * Handles expressions like th.zIndex.appBar, theme.zIndex.drawer, etc.
 * @param {Node} node - The AST node to extract from.
 * @returns {number|undefined} - The numeric zIndex value or undefined.
 */
const extractZIndexValue = (node) => {
  // Handles th.zIndex.appBar, themeArg.zIndex.appBar, t.zIndex.appBar, etc.
  if (
    node.type === 'MemberExpression' &&
    node.object &&
    node.object.type === 'MemberExpression' &&
    node.object.object &&
    node.object.object.type === 'Identifier' && // Allow any identifier
    node.object.property &&
    node.object.property.name === 'zIndex' &&
    node.property &&
    typeof node.property.name === 'string'
  ) {
    return zIndexValues[node.property.name];
  }
  return undefined;
};

/**
 * Updates zIndex properties in style objects.
 * - Converts string keys (e.g., "appBar") to their numeric values.
 * - Evaluates binary expressions involving zIndex values and numbers.
 * - Handles arrow functions returning binary expressions.
 *
 * @param {Node} prop - The property node to update.
 * @param {object} j - jscodeshift API.
 * @param {string} themeArg - The theme argument name (default: 'th').
 */
const updateZIndexValue = (prop, j) => {
  // Support both Identifier and Literal keys
  const keyName = prop.key.name || prop.key.value;
  if (keyName !== 'zIndex') {
    return;
  }
  if (prop.type === 'ObjectProperty' || prop.type === 'Property') {
    // Handle string literal values (e.g., zIndex: "appBar")
    if (
      (prop.value.type === 'Literal' || prop.value.type === 'StringLiteral') &&
      typeof prop.value.value === 'string'
    ) {
      const lookupValue = zIndexValues[prop.value.value];
      if (lookupValue !== undefined) {
        prop.value = j.literal(lookupValue);
      }
    }
    /**
     * Helper to evaluate binary expressions involving zIndex values and numbers.
     * Supports addition and subtraction.
     * @param {Node} expr - The BinaryExpression node.
     * @returns {Node|null} - The resulting literal node or null.
     */
    const handleBinary = (expr) => {
      if (expr.type === 'BinaryExpression') {
        let leftValue = extractZIndexValue(expr.left);
        let rightValue = extractZIndexValue(expr.right);
        if (
          leftValue !== undefined &&
          (expr.right.type === 'Literal' || expr.right.type === 'NumericLiteral') &&
          typeof expr.right.value === 'number'
        ) {
          if (expr.operator === '+') {
            return j.literal(leftValue + expr.right.value);
          } else if (expr.operator === '-') {
            return j.literal(leftValue - expr.right.value);
          }
        } else if (
          rightValue !== undefined &&
          (expr.left.type === 'Literal' || expr.left.type === 'NumericLiteral') &&
          typeof expr.left.value === 'number'
        ) {
          if (expr.operator === '+') {
            return j.literal(expr.left.value + rightValue);
          } else if (expr.operator === '-') {
            return j.literal(expr.left.value - rightValue);
          }
        }
      }
      return null;
    };

    // Direct binary expression (e.g., zIndex: th.zIndex.appBar + 1)
    if (prop.value.type === 'BinaryExpression') {
      const result = handleBinary(prop.value);
      if (result) prop.value = result;
    }

    // Arrow function returning binary expression (e.g., zIndex: () => th.zIndex.drawer + 2)
    if (prop.value.type === 'ArrowFunctionExpression') {
      const body = prop.value.body;
      if (body.type === 'BinaryExpression') {
        const result = handleBinary(body);
        if (result) prop.value = result;
      }
    }
  }
};

module.exports = updateZIndexValue;
module.exports.zIndexValues = zIndexValues; // Export the zIndex values for testing or other purposes
