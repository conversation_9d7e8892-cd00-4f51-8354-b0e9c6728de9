// List of style properties that are related to sizing and should be processed
const sizeProperties = ['width', 'height', 'minWidth', 'minHeight', 'maxWidth', 'maxHeight'];

/**
 * Updates sizing-related properties in style objects.
 * Converts fractional numeric values (0 < n <= 1) to percentages.
 * Also evaluates simple binary expressions (e.g., 1/2, 8/10) and converts to percentages if result is between 0 and 1.
 *
 * @param {Node} prop - The property node to update.
 * @param {object} j - jscodeshift API.
 */
const updateSizeValue = (prop, j) => {
  // Support both Identifier and Literal keys for property names
  const keyName = prop.key.name || prop.key.value;

  // Only process if the property is a recognized sizing property
  if (!sizeProperties.includes(keyName)) {
    return;
  }

  if (prop.type === 'ObjectProperty' || prop.type === 'Property') {
    // Handle direct numeric values between 0 and 1 (e.g., 0.5 -> "50%")
    if (
      (prop.value.type === 'Literal' || prop.value.type === 'NumericLiteral') &&
      typeof prop.value.value === 'number' &&
      prop.value.value > 0 &&
      prop.value.value <= 1
    ) {
      prop.value = j.literal(`${prop.value.value * 100}%`);
      return;
    }

    // Handle binary expressions like 8/10 or 1/2
    if (
      prop.value.type === 'BinaryExpression' &&
      ['/', '*', '+', '-'].includes(prop.value.operator) &&
      (prop.value.left.type === 'Literal' || prop.value.left.type === 'NumericLiteral') &&
      (prop.value.right.type === 'Literal' || prop.value.right.type === 'NumericLiteral')
    ) {
      // Evaluate the binary expression
      const left = prop.value.left.value;
      const right = prop.value.right.value;
      let result;
      switch (prop.value.operator) {
        case '/':
          result = left / right;
          break;
        case '*':
          result = left * right;
          break;
        case '+':
          result = left + right;
          break;
        case '-':
          result = left - right;
          break;
        default:
          result = null;
      }
      // If result is a fraction between 0 and 1, convert to percentage string
      if (typeof result === 'number' && result > 0 && result < 1) {
        prop.value = j.literal(`${result * 100}%`);
        return;
      }
    }
  }
};

module.exports = updateSizeValue;
