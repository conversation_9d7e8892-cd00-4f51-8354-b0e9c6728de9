// Maps shorthand spacing keys (e.g., 'm', 'px') to their full CSS property names
const sxNumericLiteralProperties = {
  margin: 'margin',
  m: 'margin',
  marginLeft: 'marginLeft',
  ml: 'marginLeft',
  marginRight: 'marginRight',
  mr: 'marginRight',
  marginTop: 'marginTop',
  mt: 'marginTop',
  marginBottom: 'marginBottom',
  mb: 'marginBottom',
  marginInline: 'marginInline',
  mx: 'marginInline',
  marginBlock: 'marginBlock',
  my: 'marginBlock',
  padding: 'padding',
  p: 'padding',
  paddingLeft: 'paddingLeft',
  pl: 'paddingLeft',
  paddingRight: 'paddingRight',
  pr: 'paddingRight',
  paddingTop: 'paddingTop',
  pt: 'paddingTop',
  paddingBottom: 'paddingBottom',
  pb: 'paddingBottom',
  paddingInline: 'paddingInline',
  px: 'paddingInline',
  paddingBlock: 'paddingBlock',
  py: 'paddingBlock',
};

/**
 * Updates spacing-related properties in style objects.
 * - Renames shorthand keys (e.g., 'm', 'px') to full CSS property names.
 * - Converts numeric values to rem units (value * 0.25rem).
 * - Handles negative values.
 * - Replaces theme.spacing(x) calls with computed rem values.
 * - Handles arrow/function expressions that return spacing values.
 *
 * @param {Node} prop - The property node to update.
 * @param {object} j - jscodeshift API.
 */
const updateSpacingValue = (prop, j) => {
  // Get the property name, supporting both Identifier and Literal keys
  const keyName = prop.key.name || prop.key.value;

  // Only process recognized spacing shorthand properties
  if (!Object.prototype.hasOwnProperty.call(sxNumericLiteralProperties, keyName)) {
    return;
  }

  if (prop.type === 'ObjectProperty' || prop.type === 'Property') {
    // Always rename the key to the full CSS property name
    prop.key = j.identifier(sxNumericLiteralProperties[keyName]);

    // Handle numeric literal values (e.g., m: 2)
    if (
      (prop.value.type === 'Literal' || prop.value.type === 'NumericLiteral') &&
      typeof prop.value.value === 'number'
    ) {
      prop.value = prop.value.value === 0 ? j.literal(0) : j.literal(`${prop.value.value * 0.25}rem`);
    }

    // Handle negative numbers (e.g., m: -2)
    else if (
      prop.value.type === 'UnaryExpression' &&
      prop.value.operator === '-' &&
      (prop.value.argument.type === 'Literal' || prop.value.argument.type === 'NumericLiteral') &&
      typeof prop.value.argument.value === 'number'
    ) {
      const negativeValue = -prop.value.argument.value;
      prop.value = negativeValue === 0 ? j.literal(0) : j.literal(`${negativeValue * 0.25}rem`);
    }

    // Handle theme.spacing(...) calls (e.g., m: theme.spacing(2, 'auto'))
    else if (
      prop.value.type === 'CallExpression' &&
      prop.value.callee &&
      prop.value.callee.property &&
      prop.value.callee.property.name === 'spacing'
    ) {
      const args = prop.value.arguments;
      if (args.length > 0) {
        const pxString = args
          .map((arg) => {
            // Numeric argument
            if ((arg.type === 'Literal' || arg.type === 'NumericLiteral') && typeof arg.value === 'number') {
              return arg.value === 0 ? '0' : `${arg.value * 0.25}rem`;
            }
            // Negative numeric argument
            if (
              arg.type === 'UnaryExpression' &&
              arg.operator === '-' &&
              (arg.argument.type === 'Literal' || arg.argument.type === 'NumericLiteral') &&
              typeof arg.argument.value === 'number'
            ) {
              const negativeValue = -arg.argument.value;
              return negativeValue === 0 ? '0' : `${negativeValue * 0.25}rem`;
            }
            // String argument (e.g., 'auto')
            if ((arg.type === 'Literal' || arg.type === 'StringLiteral') && typeof arg.value === 'string') {
              return arg.value;
            }
            // Fallback: print as-is (could be identifier, etc.)
            return j(arg).toSource();
          })
          .join(' ');
        prop.value = pxString === '0' ? j.literal(0) : j.literal(pxString);
      }
    }

    // Handle function expressions that return spacing values
    else if (
      (prop.value.type === 'ArrowFunctionExpression' || prop.value.type === 'FunctionExpression') &&
      prop.value.body
    ) {
      let callExpr = null;
      let numericValue = null;

      // Concise body: (theme) => theme.spacing(2, 'auto')
      if (prop.value.body.type === 'CallExpression') {
        callExpr = prop.value.body;
      }
      // Block body: (theme) => { return theme.spacing(2); } or { return 2; }
      else if (
        prop.value.body.type === 'BlockStatement' &&
        prop.value.body.body.length === 1 &&
        prop.value.body.body[0].type === 'ReturnStatement'
      ) {
        const retArg = prop.value.body.body[0].argument;
        // Return theme.spacing(...)
        if (retArg && retArg.type === 'CallExpression') {
          callExpr = retArg;
        }
        // Return numeric literal
        else if (
          retArg &&
          (retArg.type === 'Literal' || retArg.type === 'NumericLiteral') &&
          typeof retArg.value === 'number'
        ) {
          numericValue = retArg.value;
        }
      }

      // If function returns theme.spacing(...), handle each argument
      if (callExpr && callExpr.callee && callExpr.callee.property && callExpr.callee.property.name === 'spacing') {
        const args = callExpr.arguments;
        if (args.length > 0) {
          const pxString = args
            .map((arg) => {
              if ((arg.type === 'Literal' || arg.type === 'NumericLiteral') && typeof arg.value === 'number') {
                return arg.value === 0 ? '0' : `${arg.value * 0.25}rem`;
              }
              if (
                arg.type === 'UnaryExpression' &&
                arg.operator === '-' &&
                (arg.argument.type === 'Literal' || arg.argument.type === 'NumericLiteral') &&
                typeof arg.argument.value === 'number'
              ) {
                const negativeValue = -arg.argument.value;
                return negativeValue === 0 ? '0' : `${negativeValue * 0.25}rem`;
              }
              if ((arg.type === 'Literal' || arg.type === 'StringLiteral') && typeof arg.value === 'string') {
                return arg.value;
              }
              // Fallback: print as-is
              return j(arg).toSource();
            })
            .join(' ');
          prop.value = pxString === '0' ? j.literal(0) : j.literal(pxString);
        }
      } else if (typeof numericValue === 'number') {
        prop.value = j.literal(`${numericValue * 0.25}rem`);
      }
    }
  }
};

module.exports = updateSpacingValue;
