const getSxAttribute = (path) => {
  const attrs = path.node.openingElement.attributes;
  return attrs.find((attr) => attr.type === 'JSXAttribute' && attr.name.name === 'sx');
};

const isObjectLiteral = (sxAttr) => {
  return (
    sxAttr &&
    sxAttr.value &&
    sxAttr.value.type === 'JSXExpressionContainer' &&
    sxAttr.value.expression &&
    sxAttr.value.expression.type === 'ObjectExpression'
  );
};

const isFunctionReturningObjectLiteral = (sxAttr) => {
  return (
    sxAttr &&
    sxAttr.value &&
    sxAttr.value.type === 'JSXExpressionContainer' &&
    sxAttr.value.expression &&
    (sxAttr.value.expression.type === 'ArrowFunctionExpression' ||
      sxAttr.value.expression.type === 'FunctionExpression')
  );
};

module.exports = {
  getSxAttribute,
  isObjectLiteral,
  isFunctionReturningObjectLiteral,
};
