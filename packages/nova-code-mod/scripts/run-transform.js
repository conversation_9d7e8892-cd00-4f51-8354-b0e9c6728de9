#!/usr/bin/env node

/* eslint-disable @typescript-eslint/no-var-requires */
const { run: jscodeshift } = require('jscodeshift/src/Runner');
const path = require('node:path');

// Always use the default transform path
const transformPath = path.resolve(__dirname, '../src/transforms/transform.js');

// Take target paths from command line arguments, default to ['targets'] if none provided
const [, , ...restArgs] = process.argv;
const paths = restArgs.length > 0 ? restArgs : [];

const options = {
  dry: false,
  print: true,
  verbose: 1,
  parser: 'tsx',
  extensions: 'js,jsx,ts,tsx',
};

const run = async () => {
  let res = await jscodeshift(transformPath, paths, options);
  console.log(res);
};
run();
