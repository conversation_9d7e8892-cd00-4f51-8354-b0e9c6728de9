# nova-code-mod

## Overview
`@hxnova/nova-code-mod` is a package that utilizes jscodeshift for code manipulation and migration. It provides a set of transformations that can be applied to JavaScript and TypeScript codebases to automate code modifications. The intent is to ease the migration from NexusUI projects and automatically assist with updating the majority of your sx styles to the new theme structure.

## Usage

We recommend running the code mod using npx to ensure you are getting the latest code and avoid having to manage globally installed packages:

```
npx nova-code-mod <path-to-files>
```

Replace `<path-to-files>` with the path to the JavaScript or TypeScript files or directories you want to transform.



## Usage


## For Maintainers

## Usage

To run the transformations, you can use the provided script. The script is located in the `scripts` directory and can be executed with the following command:

```
node scripts/run-transform.js <path-to-files>
```

Or, if installed globally or linked, you can use the CLI:

```
nova-mod <path-to-files>
```

### Transformations
The main transformation logic is defined in [`src/transforms/transform.js`](src/transforms/transform.js). You can modify this file to customize the transformation rules according to your needs. Additional transforms are available in the same directory.

### Utilities
Utility functions that assist with the transformation process can be found in [`src/utils/index.js`](src/utils/index.js). These functions can help with manipulating AST nodes or logging during the transformation.


## Contributing
If you would like to contribute to the project, please fork the repository and submit a pull request with your changes.

## License
This project is licensed under the BSD-3-Clause License.