/// <reference types="@testing-library/jest-dom" />
import '@testing-library/jest-dom/vitest';
import React from 'react';
import { render, screen, cleanup } from '@testing-library/react';
import { describe, expect, it, afterEach, vi, beforeEach } from 'vitest';
import { Portal } from './Portal';

// Create a simple test child component
const TestChild = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>((props, ref) => (
  <div ref={ref} data-testid="portal-content" {...props}>
    Portal Content
  </div>
));

// Create a custom container for testing
let customContainer: HTMLDivElement;

beforeEach(() => {
  customContainer = document.createElement('div');
  customContainer.setAttribute('data-testid', 'custom-container');
  document.body.appendChild(customContainer);
});

afterEach(() => {
  cleanup();
  if (customContainer && document.body.contains(customContainer)) {
    document.body.removeChild(customContainer);
  }
  vi.clearAllMocks();
});

describe('Portal', () => {
  it('renders children into document.body by default', () => {
    render(
      <Portal>
        <TestChild />
      </Portal>,
    );

    expect(screen.getByTestId('portal-content')).toBeInTheDocument();
    expect(screen.getByText('Portal Content')).toBeInTheDocument();
  });

  it('renders children directly when disablePortal is true', () => {
    render(
      <div data-testid="parent-container">
        <Portal disablePortal>
          <TestChild />
        </Portal>
      </div>,
    );

    const content = screen.getByTestId('portal-content');
    const parent = screen.getByTestId('parent-container');

    expect(content).toBeInTheDocument();
    expect(parent).toContainElement(content);
  });

  it('renders children into custom container when provided', () => {
    render(
      <Portal container={customContainer}>
        <TestChild />
      </Portal>,
    );

    expect(screen.getByTestId('portal-content')).toBeInTheDocument();
    expect(customContainer).toContainElement(screen.getByTestId('portal-content'));
  });

  it('renders children into container returned by function', () => {
    const getContainer = () => customContainer;

    render(
      <Portal container={getContainer}>
        <TestChild />
      </Portal>,
    );

    expect(screen.getByTestId('portal-content')).toBeInTheDocument();
    expect(customContainer).toContainElement(screen.getByTestId('portal-content'));
  });

  it('handles null container gracefully', () => {
    const getContainer = () => null;

    render(
      <Portal container={getContainer}>
        <TestChild />
      </Portal>,
    );

    // Should not render anything when container is null
    expect(screen.queryByTestId('portal-content')).not.toBeInTheDocument();
  });

  it('forwards ref correctly when disablePortal is true', () => {
    const ref = React.createRef<HTMLDivElement>();

    render(
      <Portal disablePortal>
        <TestChild ref={ref} />
      </Portal>,
    );

    expect(ref.current).toBe(screen.getByTestId('portal-content'));
  });

  it('forwards ref to mount node when portal is enabled', () => {
    // Test that ref forwarding works by checking the portal renders in the expected container
    render(
      <Portal container={customContainer}>
        <div data-testid="portal-content">Portal Content</div>
      </Portal>,
    );

    // Verify content is rendered in the custom container
    expect(customContainer).toContainElement(screen.getByTestId('portal-content'));
  });

  it('handles non-React element children when disablePortal is true', () => {
    render(
      <div data-testid="wrapper">
        <Portal disablePortal>Plain text content</Portal>
      </div>,
    );

    const wrapper = screen.getByTestId('wrapper');
    expect(wrapper).toHaveTextContent('Plain text content');
  });

  it('handles multiple children', () => {
    render(
      <Portal>
        <div data-testid="child1">Child 1</div>
        <div data-testid="child2">Child 2</div>
      </Portal>,
    );

    expect(screen.getByTestId('child1')).toBeInTheDocument();
    expect(screen.getByTestId('child2')).toBeInTheDocument();
  });

  it('updates container when container prop changes', () => {
    const newContainer = document.createElement('div');
    newContainer.setAttribute('data-testid', 'new-container');
    document.body.appendChild(newContainer);

    const { rerender } = render(
      <Portal container={customContainer}>
        <TestChild />
      </Portal>,
    );

    // Initially should be in custom container
    expect(customContainer).toContainElement(screen.getByTestId('portal-content'));

    // Update container
    rerender(
      <Portal container={newContainer}>
        <TestChild />
      </Portal>,
    );

    // Should now be in new container
    expect(newContainer).toContainElement(screen.getByTestId('portal-content'));
    expect(customContainer).not.toContainElement(screen.getByTestId('portal-content'));

    // Cleanup
    document.body.removeChild(newContainer);
  });

  it('switches from portal to direct rendering when disablePortal changes', () => {
    const { rerender } = render(
      <div data-testid="parent">
        <Portal container={customContainer}>
          <TestChild />
        </Portal>
      </div>,
    );

    // Initially should be in custom container (portal mode)
    expect(customContainer).toContainElement(screen.getByTestId('portal-content'));

    // Switch to direct rendering
    rerender(
      <div data-testid="parent">
        <Portal container={customContainer} disablePortal>
          <TestChild />
        </Portal>
      </div>,
    );

    // Should now be in parent container
    const parent = screen.getByTestId('parent');
    expect(parent).toContainElement(screen.getByTestId('portal-content'));
    expect(customContainer).not.toContainElement(screen.getByTestId('portal-content'));
  });

  it('handles empty children', () => {
    render(<Portal>{null}</Portal>);
    render(<Portal>{undefined}</Portal>);
    render(<Portal>{false}</Portal>);

    // Should not crash and should not render anything visible
    expect(screen.queryByTestId('portal-content')).not.toBeInTheDocument();
  });

  it('preserves child props when disablePortal is true', () => {
    render(
      <Portal disablePortal>
        <div data-testid="child" className="test-class" id="test-id">
          Content
        </div>
      </Portal>,
    );

    const child = screen.getByTestId('child');
    expect(child).toHaveClass('test-class');
    expect(child).toHaveAttribute('id', 'test-id');
  });

  it('works with React fragments', () => {
    render(
      <Portal>
        <React.Fragment>
          <div data-testid="fragment-child1">Fragment Child 1</div>
          <div data-testid="fragment-child2">Fragment Child 2</div>
        </React.Fragment>
      </Portal>,
    );

    expect(screen.getByTestId('fragment-child1')).toBeInTheDocument();
    expect(screen.getByTestId('fragment-child2')).toBeInTheDocument();
  });

  it('cleans up properly on unmount', () => {
    const { unmount } = render(
      <Portal>
        <div data-testid="portal-content">Portal Content</div>
      </Portal>,
    );

    expect(screen.getByTestId('portal-content')).toBeInTheDocument();

    unmount();

    expect(screen.queryByTestId('portal-content')).not.toBeInTheDocument();
  });

  it('handles container function that returns undefined', () => {
    const getContainer = () => undefined as any;

    render(
      <Portal container={getContainer}>
        <TestChild />
      </Portal>,
    );

    // Should not render anything when container function returns undefined
    expect(screen.queryByTestId('portal-content')).not.toBeInTheDocument();
  });

  it('works with different element types as children', () => {
    render(
      <Portal disablePortal>
        <span data-testid="span-child">Span Content</span>
      </Portal>,
    );

    const spanElement = screen.getByTestId('span-child');
    expect(spanElement.tagName.toLowerCase()).toBe('span');
    expect(spanElement).toHaveTextContent('Span Content');
  });
});
