import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { SlotCommonProps } from '../types/slot';

export interface PortalTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P & {
    /**
     * The children to render into the `container`.
     */
    children?: React.ReactNode;
    /**
     * An HTML element or function that returns one.
     * The `container` will have the portal children appended to it.
     *
     * You can also provide a callback, which is called in a React layout effect.
     * This lets you set the container from a ref, and also makes server-side rendering possible.
     *
     * By default, it uses the body of the top-level document object,
     * so it's simply `document.body` most of the time.
     */
    container?: Element | (() => Element | null) | null;
    /**
     * The `children` will be under the DOM hierarchy of the parent component.
     * @default false
     */
    disablePortal?: boolean;
  };
  defaultComponent: D;
}

export type PortalProps<
  D extends React.ElementType = PortalTypeMap['defaultComponent'],
  P = SlotCommonProps,
> = OverrideProps<PortalTypeMap<P, D>, D>;

export interface PortalOwnerState extends PortalProps {}
