'use client';
import * as React from 'react';
import * as ReactDOM from 'react-dom';
import { PortalProps } from './Portal.types';
import {
  unstable_useEnhancedEffect as useEnhancedEffect,
  unstable_useForkRef as useForkRef,
  unstable_setRef as setRef,
  unstable_getReactElementRef as getReactElementRef,
} from '@mui/utils';

function getContainer(container: PortalProps['container']) {
  return typeof container === 'function' ? container() : container;
}

export const Portal = React.forwardRef<Element, PortalProps>(function Portal(props, ref): React.ReactNode {
  const { children, container, disablePortal = false } = props;
  const [mountNode, setMountNode] = React.useState<ReturnType<typeof getContainer>>(null);

  const handleRef = useForkRef(React.isValidElement(children) ? getReactElementRef(children) : null, ref);

  useEnhancedEffect(() => {
    if (!disablePortal) {
      if (container === undefined) {
        setMountNode(document.body);
        return;
      }
      setMountNode(getContainer(container));
    }
  }, [container, disablePortal]);

  useEnhancedEffect(() => {
    if (mountNode && !disablePortal) {
      setRef(ref, mountNode);
      return () => {
        setRef(ref, null);
      };
    }

    return undefined;
  }, [ref, mountNode, disablePortal]);

  if (disablePortal) {
    if (React.isValidElement(children)) {
      const newProps = {
        ref: handleRef,
      };
      return React.cloneElement(children, newProps);
    }
    return children;
  }

  return mountNode ? ReactDOM.createPortal(children, mountNode) : null;
}) as React.ForwardRefExoticComponent<PortalProps & React.RefAttributes<Element>>;
