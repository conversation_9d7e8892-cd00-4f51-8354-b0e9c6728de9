import { styled } from '@pigment-css/react';
import { Modal } from '../../Modal';
import { dialogContentClasses } from '../Content';
import { dialogHeaderClasses } from '../Header';
import { DialogRootOwnerState } from './DialogRoot.types';

export const DialogRootSlot = styled(Modal)<DialogRootOwnerState>(({ theme }) => ({
  position: 'fixed',
  zIndex: 1300,
  right: 0,
  bottom: 0,
  top: 0,
  left: 0,
  '@media print': {
    // Use !important to override the Modal inline-style.
    position: 'absolute !important',
  },
}));

export const DialogContainer = styled('div')<DialogRootOwnerState>(({ theme }) => ({
  height: '100%',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  outline: 0,
  '@media print': {
    height: 'auto',
  },
}));

export const DialogPaper = styled('div')<DialogRootOwnerState>(({ theme }) => ({
  margin: 32,
  position: 'relative',
  overflowY: 'auto',
  borderRadius: '16px',
  backgroundColor: theme.vars.palette.surfaceContainerHigh,
  border: '1px solid',
  borderColor: theme.vars.palette.outlineVariant,
  '@media print': {
    overflowY: 'visible',
    boxShadow: 'none',
  },
  display: 'flex',
  flexDirection: 'column',
  maxHeight: 'calc(100% - 64px)',
  [`&:not(:has(.${dialogContentClasses.root})) .${dialogHeaderClasses.root}`]: {
    overflowY: 'auto',
    height: '100%',
  },
  variants: [
    {
      props: (ownerState) => !ownerState.maxWidth,
      style: {
        maxWidth: 'calc(100% - 64px)',
      },
    },
    {
      props: { maxWidth: 'xs' },
      style: {
        maxWidth: '375px',
      },
    },
    {
      props: { maxWidth: 'sm' },
      style: {
        maxWidth: '600px',
      },
    },
    {
      props: { maxWidth: 'md' },
      style: {
        maxWidth: '840px',
      },
    },
    {
      props: { maxWidth: 'lg' },
      style: {
        maxWidth: '1200px',
      },
    },
    {
      props: { maxWidth: 'xl' },
      style: {
        maxWidth: '1600px',
      },
    },
    {
      props: { fullWidth: true },
      style: {
        width: 'calc(100% - 64px)',
      },
    },
    {
      props: { fullScreen: true },
      style: {
        margin: 0,
        width: '100%',
        maxWidth: '100%',
        height: '100%',
        maxHeight: 'none',
        borderRadius: 0,
      },
    },
  ],
}));

export const DialogBackdrop = styled('div')<DialogRootOwnerState>(({ theme }) => ({
  zIndex: -1,
  position: 'fixed',
  right: 0,
  bottom: 0,
  top: 0,
  left: 0,
  backgroundColor: 'rgba(0, 0, 0, 0.16)',
  WebkitTapHighlightColor: 'transparent',
}));
