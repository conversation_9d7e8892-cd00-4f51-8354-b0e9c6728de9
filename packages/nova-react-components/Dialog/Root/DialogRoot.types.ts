import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { SlotProps, CreateSlotsAndSlotProps, SlotCommonProps } from '../../types/slot';
import { ModalProps } from '../../Modal';

export type DialogRootSlot = 'root';

export interface DialogRootSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root?: React.ElementType;
}

export type DialogRootSlotsAndSlotProps = CreateSlotsAndSlotProps<
  DialogRootSlots,
  {
    root: SlotProps<'div', object, DialogRootOwnerState>;
  }
>;

export interface DialogRootTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P &
    Omit<ModalProps, 'children'> & {
      /**
       * The id(s) of the element(s) that describe the dialog.
       */
      'aria-describedby'?: string;
      /**
       * The id(s) of the element(s) that label the dialog.
       */
      'aria-labelledby'?: string;
      /**
       * Informs assistive technologies that the element is modal.
       * It's added on the element with role="dialog".
       * @default true
       */
      'aria-modal'?: boolean | 'true' | 'false';
      /**
       * If `true`, the dialog is full-screen.
       * @default false
       */
      fullScreen?: boolean;
      /**
       * If `true`, the dialog stretches to `maxWidth`.
       *
       * Notice that the dialog width grow is limited by the default margin.
       * @default false
       */
      fullWidth?: boolean;
      /**
       * Determine the max-width of the dialog.
       * The dialog width grows with the size of the screen.
       * Set to `false` to disable `maxWidth`.
       * @default 'sm'
       */
      maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | false;
    } & DialogRootSlotsAndSlotProps;
  defaultComponent: D;
}

export type DialogRootProps<
  D extends React.ElementType = DialogRootTypeMap['defaultComponent'],
  P = SlotCommonProps,
> = OverrideProps<DialogRootTypeMap<P, D>, D>;

export interface DialogRootOwnerState extends Partial<DialogRootProps> {}
