import '@testing-library/jest-dom/vitest';
import React from 'react';
import { describe, expect, it } from 'vitest';
import { render, screen } from '@testing-library/react';
import LocaleProvider, { useLocale, useLanguage, LocaleComponentName } from './LocaleProvider';
import enUS from './en_US';

const TestComponent = ({ componentName }: { componentName?: LocaleComponentName }) => {
  const locale = useLocale(componentName);
  const language = useLanguage();
  return (
    <div>
      <div data-testid="locale">{JSON.stringify(locale)}</div>
      <div data-testid="language">{language}</div>
    </div>
  );
};

describe('LocaleProvider', () => {
  it('provides default locale values', () => {
    render(
      <LocaleProvider>
        <TestComponent />
      </LocaleProvider>,
    );

    expect(screen.getByTestId('locale')).toHaveTextContent(JSON.stringify(enUS));
    expect(screen.getByTestId('language')).toBeInTheDocument();
    expect(screen.getByTestId('language')).toHaveTextContent(enUS.locale);
  });

  it('overrides default locale values', () => {
    const customLocale = { ...enUS, locale: 'fr-FR', greeting: 'Bonjour' };

    render(
      <LocaleProvider locale={customLocale}>
        <TestComponent />
      </LocaleProvider>,
    );

    expect(screen.getByTestId('locale')).toHaveTextContent(JSON.stringify(customLocale));
    expect(screen.getByTestId('language')).toHaveTextContent(customLocale.locale);
  });

  it('returns specific component locale', () => {
    const customLocale = { ...enUS, greeting: 'Hello' };

    render(
      <LocaleProvider locale={customLocale}>
        <TestComponent componentName="Autocomplete" />
      </LocaleProvider>,
    );

    expect(screen.getByTestId('locale')).toHaveTextContent(
      JSON.stringify({
        clearText: 'Clear',
        closeText: 'Close',
        loadingText: 'Loading…',
        noOptionsText: 'No options',
        openText: 'Open',
      }),
    );
  });
});
