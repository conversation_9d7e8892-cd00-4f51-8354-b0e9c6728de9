import { extendTheme, pigment } from '@pigment-css/vite-plugin';
import { defineConfig, coverageConfigDefaults, ViteUserConfig } from 'vitest/config';

export default defineConfig(async (): Promise<ViteUserConfig> => {
  const { NovaTheme } = await import('@hxnova/themes');
  return {
    plugins: [
      ...pigment({
        theme: extendTheme(NovaTheme),
        babelOptions: {
          plugins: [`@babel/plugin-transform-export-namespace-from`],
        },
      }),
    ],
    test: {
      environment: 'jsdom',
      setupFiles: './vitest.setup.ts',
      coverage: {
        provider: 'v8',
        reporter: ['cobertura', 'html'],
        reportsDirectory: '../../coverage',
        exclude: [
          '**/*.styled.ts?(x)',
          '**/*.type?(s).ts',
          '**/CssBaseline.tsx',
          '**Locale/*.ts',
          '**/index.ts',
          '**/TreeView/**',
          'consumer-vitest.setup.tsx',
          ...coverageConfigDefaults.exclude,
        ],
        thresholds: {
          lines: 80,
          branches: 80,
          functions: 80,
          statements: 80,
        },
      },
      reporters: ['default', ['junit', { outputFile: '../../coverage/junit.xml' }]],
    },
  };
});
