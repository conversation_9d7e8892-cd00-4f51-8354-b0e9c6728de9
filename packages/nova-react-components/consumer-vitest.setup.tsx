import { vi } from 'vitest';

/**
 * <PERSON>cks @pigment-css/react components to prevent PigmentCSS from throwing errors during Vitest tests.
 *
 * Boolean-only props (like `container` and `divider`) are omitted from the mocked `div`
 * to avoid React warnings about invalid HTML attributes.
 */
vi.mock('@pigment-css/react/Grid', () => {
  return {
    default: ({ container, ...props }: any) => <div {...props} />,
  };
});

vi.mock('@pigment-css/react/Stack', () => {
  return {
    default: ({ divider, ...props }: any) => <div {...props} />,
  };
});
