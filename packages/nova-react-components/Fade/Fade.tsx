'use client';
import * as React from 'react';
import { Transition } from 'react-transition-group';
import { duration, reflow, getTransitionProps, createTransition } from '../transitions/utils';
import { FadeProps } from './Fade.types';
import useForkRef from '@mui/utils/useForkRef';
import getReactElementRef from '@mui/utils/getReactElementRef';

const styles = {
  entering: {
    opacity: 1,
  },
  entered: {
    opacity: 1,
  },
};

// eslint-disable-next-line react/display-name
export const Fade = React.forwardRef((props: FadeProps, ref: React.ForwardedRef<Element>) => {
  const defaultTimeout = {
    enter: duration.enteringScreen,
    exit: duration.leavingScreen,
  };

  const {
    addEndListener,
    appear = true,
    children,
    easing,
    in: inProp,
    onEnter,
    onEntered,
    onEntering,
    onExit,
    onExited,
    onExiting,
    style,
    timeout = defaultTimeout,
    TransitionComponent = Transition,
    ...other
  } = props;

  const enableStrictModeCompat = true;
  const nodeRef = React.useRef(null);
  const handleRef = useForkRef(nodeRef, getReactElementRef(children), ref);

  const normalizedTransitionCallback =
    (callback?: (node: HTMLElement, isAppearing?: boolean) => void) => (maybeIsAppearing?: boolean) => {
      if (callback && nodeRef.current) {
        const node = nodeRef.current as HTMLElement;

        // onEnterXxx and onExitXxx callbacks have a different arguments.length value.
        if (maybeIsAppearing === undefined) {
          callback(node);
        } else {
          callback(node, maybeIsAppearing);
        }
      }
    };

  const handleEntering = normalizedTransitionCallback(onEntering as (node: HTMLElement, isAppearing?: boolean) => void);

  const handleEnter = normalizedTransitionCallback((node: HTMLElement, isAppearing?: boolean) => {
    reflow(node); // So the animation always start from the start.

    const transitionProps = getTransitionProps(
      { style, timeout, easing },
      {
        mode: 'enter',
      },
    );

    node.style.webkitTransition = createTransition('opacity', transitionProps);
    node.style.transition = createTransition('opacity', transitionProps);

    if (onEnter) {
      onEnter(node, isAppearing!);
    }
  });

  const handleEntered = normalizedTransitionCallback(onEntered as (node: HTMLElement, isAppearing?: boolean) => void);

  const handleExiting = normalizedTransitionCallback(onExiting);

  const handleExit = normalizedTransitionCallback((node: HTMLElement) => {
    const transitionProps = getTransitionProps(
      { style, timeout, easing },
      {
        mode: 'exit',
      },
    );

    node.style.webkitTransition = createTransition('opacity', transitionProps);
    node.style.transition = createTransition('opacity', transitionProps);

    if (onExit) {
      onExit(node);
    }
  });

  const handleExited = normalizedTransitionCallback(onExited);

  const handleAddEndListener = (next: () => void) => {
    if (addEndListener) {
      // Old call signature before `react-transition-group` implemented `nodeRef`
      addEndListener(nodeRef.current! as HTMLElement, next);
    }
  };

  return (
    <TransitionComponent
      appear={appear}
      in={inProp}
      nodeRef={enableStrictModeCompat ? nodeRef : undefined}
      onEnter={handleEnter}
      onEntered={handleEntered}
      onEntering={handleEntering}
      onExit={handleExit}
      onExited={handleExited}
      onExiting={handleExiting}
      addEndListener={handleAddEndListener}
      timeout={timeout}
      {...other}
    >
      {(state: string, childProps?: any) => {
        return React.cloneElement(children, {
          style: {
            visibility: state === 'exited' && !inProp ? 'hidden' : undefined,
            ...styles[state as keyof typeof styles],
            ...style,
            ...(children.props as any).style,
          },
          ref: handleRef,
          ...childProps,
        });
      }}
    </TransitionComponent>
  );
});
