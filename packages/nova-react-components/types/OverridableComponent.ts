import { DefaultComponentProps, OverridableTypeMap, OverrideProps } from '@mui/types';
import { SxProps } from './theme';

export interface OverridableComponent<M extends OverridableTypeMap> {
  <C extends React.ElementType>(
    props: {
      /**
       * The root node component, which can be specified as either
       * a string representing an HTML tag or a React component.
       */
      component: C;
      /**
       * The system prop that allows defining system overrides as well as additional CSS styles.
       */
      sx?: SxProps;
    } & OverrideProps<M, C>,
  ): React.JSX.Element | null;
  (props: DefaultComponentProps<M>): React.JSX.Element | null;
}
