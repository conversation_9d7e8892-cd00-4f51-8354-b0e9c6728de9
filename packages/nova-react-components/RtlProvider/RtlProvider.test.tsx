import '@testing-library/jest-dom/vitest';
import React from 'react';
import { describe, expect, it } from 'vitest';
import { render, screen } from '@testing-library/react';
import RtlProvider, { useRtl } from './RtlProvider';

const TestComponent = () => {
  const rtl = useRtl();
  return <div data-testid="rtl-value">{rtl ? 'RTL' : 'LTR'}</div>;
};

describe('RtlProvider', () => {
  it('provides default RTL value as false', () => {
    render(
      <RtlProvider>
        <TestComponent />
      </RtlProvider>,
    );

    expect(screen.getByTestId('rtl-value')).toHaveTextContent('LTR');
  });

  it('overrides default RTL value', () => {
    render(
      <RtlProvider rtl={true}>
        <TestComponent />
      </RtlProvider>,
    );

    expect(screen.getByTestId('rtl-value')).toHaveTextContent('RTL');
  });

  it('handles undefined rtl prop', () => {
    render(
      <RtlProvider rtl={undefined}>
        <TestComponent />
      </RtlProvider>,
    );

    expect(screen.getByTestId('rtl-value')).toHaveTextContent('LTR');
  });
});
