'use client';
import * as React from 'react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import clsx from 'clsx';
import { Tooltip as BaseTooltip } from '@base-ui-components/react/tooltip';
import { TooltipProps } from './Tooltip.types';
import { getTooltipUtilityClass } from './Tooltip.classes';
import useSlotProps from '@mui/utils/useSlotProps';
import { StyledPopup, StyledArrow, BaseTooltipTrigger } from './Tooltip.styled';

// Wrapper component to ensure compatibility

const useUtilityClasses = (ownerState: TooltipProps & { isOpen?: boolean }) => {
  const { isOpen } = ownerState;

  const slots = {
    root: ['root', isOpen && 'open', !isOpen && 'closed'],
    trigger: ['trigger'],
    portal: ['portal'],
    positioner: ['positioner'],
    popup: ['popup'],
    arrow: ['arrow'],
  };

  return composeClasses(slots, getTooltipUtilityClass, {});
};
// eslint-disable-next-line react/display-name
export const Tooltip = React.forwardRef<HTMLDivElement, TooltipProps>((props, ref) => {
  const {
    children,
    title,
    className,
    open,
    defaultOpen,
    onOpenChange,
    placement = 'top',
    showArrow = false,
    slotProps = {},
    ...other
  } = props;

  const [isOpen, setIsOpen] = React.useState(defaultOpen || slotProps.root?.defaultOpen || false);

  const handleOpenChange = React.useCallback(
    (nextOpen: boolean, event?: Event, reason?: any) => {
      setIsOpen(nextOpen);
      onOpenChange?.(nextOpen, event, reason);
      slotProps.root?.onOpenChange?.(nextOpen, event, reason);
    },
    [onOpenChange, slotProps.root],
  );

  const ownerState = {
    ...props,
    open: open ?? slotProps.root?.open ?? isOpen,
    placement,
    className,
  };

  const classes = useUtilityClasses({ ...props, isOpen });

  const popupProps = useSlotProps({
    elementType: StyledPopup,
    externalSlotProps: {},
    additionalProps: {},
    ownerState,
    className: classes.popup,
  });

  const arrowProps = useSlotProps({
    elementType: StyledArrow,
    externalSlotProps: {},
    additionalProps: {},
    ownerState,
    className: classes.arrow,
  });

  return (
    <BaseTooltip.Provider {...slotProps.provider}>
      <BaseTooltip.Root
        open={open ?? slotProps.root?.open ?? isOpen}
        defaultOpen={defaultOpen ?? slotProps.root?.defaultOpen}
        onOpenChange={handleOpenChange}
        {...slotProps.root}
      >
        <BaseTooltipTrigger
          ref={ref}
          className={clsx(classes.trigger, className)}
          render={<div />}
          {...slotProps.trigger}
          {...other}
          style={{ ...slotProps.trigger?.style }}
        >
          {children}
        </BaseTooltipTrigger>
        <BaseTooltip.Portal {...slotProps.portal} className={classes.portal}>
          <BaseTooltip.Positioner
            side={placement ?? slotProps.positioner?.side}
            className={clsx(classes.positioner, slotProps.positioner?.className)}
            sideOffset={8}
            align="center"
            alignOffset={0}
            style={{ zIndex: 1500 }}
            {...slotProps.positioner}
          >
            <BaseTooltip.Popup
              {...slotProps.popup}
              className={clsx(classes.popup, slotProps.popup?.className)}
              data-placement={placement}
            >
              <StyledPopup {...popupProps}>
                {title}
                {showArrow && <StyledArrow data-placement={placement} {...arrowProps} />}
              </StyledPopup>
            </BaseTooltip.Popup>
          </BaseTooltip.Positioner>
        </BaseTooltip.Portal>
      </BaseTooltip.Root>
    </BaseTooltip.Provider>
  );
});
