import { styled } from '@pigment-css/react';
import { Tooltip as BaseTooltip } from '@base-ui-components/react/tooltip';
import { TooltipProps, BaseTriggerProps } from './Tooltip.types';

const TriggerWrapper = (props: BaseTriggerProps) => <BaseTooltip.Trigger {...props} />;

export const StyledPopup = styled('div')<Partial<TooltipProps>>(({ theme }) => ({
  backgroundColor: theme.vars.palette.inverseSurface,
  color: theme.vars.palette.onPrimary,
  padding: `${theme.vars.sys.viewport.spacing.padding.leftRight['2xs']} ${theme.vars.sys.viewport.spacing.padding.leftRight.xs}`,
  borderRadius: 'var(--radius-2xs)',
  fontSize: '0.75rem',
  fontFamily: theme.typography.fontFamily,
  lineHeight: '1rem',
  whiteSpace: 'nowrap',
  boxShadow: 'none',
  pointerEvents: 'none',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'flex-start',
  position: 'relative',
}));

export const StyledArrow = styled('div')<Partial<TooltipProps>>(({ theme }) => ({
  position: 'absolute',
  width: '8px',
  height: '8px',
  backgroundColor: 'inherit',
  '&[data-placement="top"]': {
    bottom: 0,
    left: '50%',
    transform: 'translate(-50%, 50%) rotate(45deg)',
  },
  '&[data-placement="bottom"]': {
    top: 0,
    left: '50%',
    transform: 'translate(-50%, -50%) rotate(45deg)',
  },
  '&[data-placement="left"]': {
    top: '50%',
    right: 0,
    transform: 'translate(50%, -50%) rotate(45deg)',
  },
  '&[data-placement="right"]': {
    top: '50%',
    left: 0,
    transform: 'translate(-50%, -50%) rotate(45deg)',
  },
}));

export const BaseTooltipTrigger = styled(TriggerWrapper)<Partial<BaseTriggerProps>>(({ theme }) => ({
  display: 'inline-block',
  backgroundColor: 'transparent',
  border: 'none',
  padding: 0,
}));
