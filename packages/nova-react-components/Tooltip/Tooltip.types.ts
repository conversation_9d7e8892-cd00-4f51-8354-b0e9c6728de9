import { ComponentProps, HTMLAttributes } from 'react';
import { Tooltip as BaseTooltip } from '@base-ui-components/react';

export type BaseTriggerProps = ComponentProps<typeof BaseTooltip.Trigger>;
export interface TooltipProps
  extends ComponentProps<typeof BaseTooltip.Root>,
    Omit<HTMLAttributes<HTMLDivElement>, 'title'> {
  /**
   * The content to be displayed in the tooltip.
   */
  title: React.ReactNode;
  /**
   * The placement of the trigger element to place the tooltip.
   * @default 'top'
   */
  placement?: 'top' | 'right' | 'bottom' | 'left';
  /**
   * Whether to show the arrow.
   * @default false
   */
  showArrow?: boolean;
  /**
   * Class name applied to the root element.
   */
  className?: string;
  /**
   * Props passed to the sub-components.
   */
  slotProps?: {
    /**
     * Props passed to the Provider component.
     */
    provider?: Omit<ComponentProps<typeof BaseTooltip.Provider>, 'children'>;
    /**
     * Props passed to the Root component.
     */
    root?: Omit<ComponentProps<typeof BaseTooltip.Root>, 'children'>;
    /**
     * Props passed to the Trigger component.
     */
    trigger?: Omit<ComponentProps<typeof BaseTooltip.Trigger>, 'children'>;
    /**
     * Props passed to the Portal component.
     */
    portal?: Omit<ComponentProps<typeof BaseTooltip.Portal>, 'children'>;
    /**
     * Props passed to the Positioner component.
     */
    positioner?: Omit<ComponentProps<typeof BaseTooltip.Positioner>, 'children'>;
    /**
     * Props passed to the Popup component.
     */
    popup?: Omit<ComponentProps<typeof BaseTooltip.Popup>, 'children'>;
    /**
     * Props passed to the Arrow component.
     */
    arrow?: Omit<ComponentProps<typeof BaseTooltip.Arrow>, 'children'>;
  };
}
