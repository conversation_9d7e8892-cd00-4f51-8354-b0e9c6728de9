import { styled } from '@pigment-css/react';
import { ModalOwnerState } from './Modal.types';
import { Backdrop } from '../Backdrop';

export const ModalRoot = styled('div', {
  name: 'NovaModal',
  slot: 'Root',
  overridesResolver: (props, styles) => {
    const { ownerState } = props;
    return [styles.root, !ownerState.open && ownerState.exited && styles.hidden];
  },
})<ModalOwnerState>(({ theme }) => ({
  position: 'fixed',
  zIndex: 1300,
  right: 0,
  bottom: 0,
  top: 0,
  left: 0,
  variants: [
    {
      props: ({ ownerState }) => !ownerState.open && ownerState.exited,
      style: {
        visibility: 'hidden',
      },
    },
  ],
}));

export const ModalBackdrop = styled(Backdrop, {
  name: 'NovaModal',
  slot: 'Backdrop',
  overridesResolver: (props, styles) => {
    return styles.backdrop;
  },
})({
  zIndex: -1,
});
