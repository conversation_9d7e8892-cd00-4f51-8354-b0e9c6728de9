/// <reference types="@testing-library/jest-dom" />
import '@testing-library/jest-dom/vitest';
import React from 'react';
import { render, screen, cleanup, fireEvent, waitFor } from '@testing-library/react';
import { describe, expect, it, afterEach, vi, beforeEach } from 'vitest';
import { Modal } from './Modal';
import { Box } from '@mui/system';

// Mock the Portal component
vi.mock('../Portal', () => ({
  Portal: React.forwardRef<Element, any>(({ children, disablePortal }, ref) => {
    if (disablePortal) {
      return children;
    }
    // For testing, just render children directly
    return (
      <Box ref={ref} data-testid="portal">
        {children}
      </Box>
    );
  }),
}));

// Mock the FocusTrap component
vi.mock('../internal/components/FocusTrap', () => ({
  FocusTrap: ({
    children,
    disableAutoFocus,
    disableEnforceFocus,
    disableRestoreFocus,
    isEnabled,
    open,
    ...props
  }: any) => {
    // Filter out non-DOM props to avoid React warnings
    const { onEnter, onExited, ...validProps } = props;

    // Add test attributes for boolean props (as data attributes to avoid warnings)
    const testProps: any = { ...validProps };
    if (disableAutoFocus) testProps['data-disable-auto-focus'] = 'true';
    if (disableEnforceFocus) testProps['data-disable-enforce-focus'] = 'true';
    if (disableRestoreFocus) testProps['data-disable-restore-focus'] = 'true';
    if (isEnabled) testProps['data-is-enabled'] = 'true';
    if (open) testProps['data-open'] = 'true';

    return (
      <div data-testid="focus-trap" {...testProps}>
        {children}
      </div>
    );
  },
}));

// Mock the useModal hook
vi.mock('../internal/hooks/useModal/useModal', () => ({
  useModal: (props: any) => {
    // Call transition callbacks if provided and hasTransition is true
    const hasTransition = !!props.onTransitionEnter || !!props.onTransitionExited;

    // Use useEffect to call transition callbacks after render
    React.useEffect(() => {
      if (hasTransition) {
        if (props.open && props.onTransitionEnter) {
          setTimeout(() => props.onTransitionEnter(), 0);
        }
        if (!props.open && props.onTransitionExited) {
          setTimeout(() => props.onTransitionExited(), 0);
        }
      }
    }, [props.open, props.onTransitionEnter, props.onTransitionExited, hasTransition, props]);

    return {
      getRootProps: () => ({
        'data-testid': 'modal-root',
        role: 'presentation',
      }),
      getBackdropProps: () => ({
        'data-testid': 'modal-backdrop',
        onClick: (event: any) => {
          if (props.onClose) {
            props.onClose(event, 'backdropClick');
          }
        },
      }),
      getTransitionProps: () => ({
        onEnter: props.onTransitionEnter,
        onExited: props.onTransitionExited,
      }),
      portalRef: { current: null },
      isTopModal: true,
      exited: !props.open,
      hasTransition,
    };
  },
}));

// Create a simple test child component
const TestChild = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>((props, ref) => (
  <div ref={ref} data-testid="modal-content" {...props}>
    Modal Content
  </div>
));

afterEach(() => {
  cleanup();
  vi.clearAllMocks();
});

describe('Modal', () => {
  it('renders when open is true', () => {
    render(
      <Modal open onClose={() => {}}>
        <TestChild />
      </Modal>,
    );

    expect(screen.getByTestId('modal-content')).toBeInTheDocument();
    expect(screen.getByText('Modal Content')).toBeInTheDocument();
  });

  it('does not render when open is false and keepMounted is false', () => {
    render(
      <Modal open={false} onClose={() => {}}>
        <TestChild />
      </Modal>,
    );

    expect(screen.queryByTestId('modal-content')).not.toBeInTheDocument();
  });

  it('renders when open is false but keepMounted is true', () => {
    render(
      <Modal open={false} keepMounted onClose={() => {}}>
        <TestChild />
      </Modal>,
    );

    expect(screen.getByTestId('modal-content')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    render(
      <Modal open className="custom-modal" onClose={() => {}}>
        <TestChild />
      </Modal>,
    );

    const modalRoot = document.querySelector('.NovaModal-root');
    expect(modalRoot).toHaveClass('custom-modal');
  });

  it('renders backdrop by default', () => {
    render(
      <Modal open onClose={() => {}}>
        <TestChild />
      </Modal>,
    );

    // Check for backdrop by class name since the testid is not available in the actual implementation
    const backdrop = document.querySelector('.NovaModal-backdrop');
    expect(backdrop).toBeInTheDocument();
  });

  it('hides backdrop when hideBackdrop is true', () => {
    render(
      <Modal open hideBackdrop onClose={() => {}}>
        <TestChild />
      </Modal>,
    );

    expect(screen.queryByTestId('modal-backdrop')).not.toBeInTheDocument();
  });

  it('calls onClose when backdrop is clicked', () => {
    const onClose = vi.fn();

    render(
      <Modal open onClose={onClose}>
        <TestChild />
      </Modal>,
    );

    // Use class selector since testid is not available in actual implementation
    const backdrop = document.querySelector('.NovaModal-backdrop');
    expect(backdrop).toBeInTheDocument();
    fireEvent.click(backdrop!);

    expect(onClose).toHaveBeenCalledWith(expect.any(Object), 'backdropClick');
  });

  it('calls onTransitionEnter when provided', async () => {
    const onTransitionEnter = vi.fn();

    render(
      <Modal open onClose={() => {}} onTransitionEnter={onTransitionEnter}>
        <TestChild />
      </Modal>,
    );

    await waitFor(() => {
      expect(onTransitionEnter).toHaveBeenCalled();
    });
  });

  it('calls onTransitionExited when provided', async () => {
    const onTransitionExited = vi.fn();

    const { rerender } = render(
      <Modal open onClose={() => {}} onTransitionExited={onTransitionExited}>
        <TestChild />
      </Modal>,
    );

    rerender(
      <Modal open={false} onClose={() => {}} onTransitionExited={onTransitionExited}>
        <TestChild />
      </Modal>,
    );

    await waitFor(() => {
      expect(onTransitionExited).toHaveBeenCalled();
    });
  });

  it('passes closeAfterTransition prop', () => {
    render(
      <Modal open closeAfterTransition onClose={() => {}}>
        <TestChild />
      </Modal>,
    );

    expect(screen.getByTestId('modal-content')).toBeInTheDocument();
  });

  it('passes disableAutoFocus prop to FocusTrap', () => {
    render(
      <Modal open disableAutoFocus onClose={() => {}}>
        <TestChild />
      </Modal>,
    );

    const focusTrap = screen.getByTestId('focus-trap');
    expect(focusTrap).toHaveAttribute('data-disable-auto-focus', 'true');
  });

  it('passes disableEnforceFocus prop to FocusTrap', () => {
    render(
      <Modal open disableEnforceFocus onClose={() => {}}>
        <TestChild />
      </Modal>,
    );

    const focusTrap = screen.getByTestId('focus-trap');
    expect(focusTrap).toHaveAttribute('data-disable-enforce-focus', 'true');
  });

  it('passes disableRestoreFocus prop to FocusTrap', () => {
    render(
      <Modal open disableRestoreFocus onClose={() => {}}>
        <TestChild />
      </Modal>,
    );

    const focusTrap = screen.getByTestId('focus-trap');
    expect(focusTrap).toHaveAttribute('data-disable-restore-focus', 'true');
  });

  it('renders with Portal by default', () => {
    render(
      <Modal open onClose={() => {}}>
        <TestChild />
      </Modal>,
    );

    expect(screen.getByTestId('portal')).toBeInTheDocument();
  });

  it('renders without Portal when disablePortal is true', () => {
    render(
      <Modal open disablePortal onClose={() => {}}>
        <TestChild />
      </Modal>,
    );

    expect(screen.queryByTestId('portal')).not.toBeInTheDocument();
  });

  it('renders with custom root component', () => {
    const CustomRoot = React.forwardRef<HTMLDivElement, any>((props, ref) => {
      // Filter out ownerState to avoid React warnings
      const { ownerState, ...filteredProps } = props;
      return <section ref={ref} {...filteredProps} data-testid="custom-root" />;
    });

    render(
      <Modal
        open
        onClose={() => {}}
        slots={{
          root: CustomRoot,
        }}
      >
        <TestChild />
      </Modal>,
    );

    // The custom root should be rendered as a section element
    const sectionElement = document.querySelector('section');
    expect(sectionElement).not.toBeNull();
    // The section element should have the custom data-testid attribute
    expect(sectionElement).toHaveAttribute('data-testid', 'custom-root');
    // Verify it's a section element, which confirms custom slot is working
    expect(sectionElement?.tagName.toLowerCase()).toBe('section');
  });

  it('renders with custom backdrop component', () => {
    const CustomBackdrop = (props: any) => {
      // Filter out the data-testid and ownerState that might come from mocked props
      const { 'data-testid': _, ownerState, ...filteredProps } = props;
      return <div data-testid="custom-backdrop" {...filteredProps} />;
    };

    render(
      <Modal
        open
        onClose={() => {}}
        slots={{
          backdrop: CustomBackdrop,
        }}
      >
        <TestChild />
      </Modal>,
    );

    expect(screen.getByTestId('custom-backdrop')).toBeInTheDocument();
  });

  it('passes slotProps to root slot', () => {
    render(
      <Modal
        open
        onClose={() => {}}
        slotProps={{
          root: {
            'data-custom': 'test-value',
          } as any,
        }}
      >
        <TestChild />
      </Modal>,
    );

    const modalRoot = document.querySelector('.NovaModal-root');
    expect(modalRoot).toHaveAttribute('data-custom', 'test-value');
  });

  it('passes slotProps to backdrop slot', () => {
    render(
      <Modal
        open
        onClose={() => {}}
        slotProps={{
          backdrop: {
            'data-custom-backdrop': 'backdrop-value',
          } as any,
        }}
      >
        <TestChild />
      </Modal>,
    );

    // The backdrop should have the custom attribute, but it might not have the testid from the mock
    const backdrop = document.querySelector('.NovaModal-backdrop');
    expect(backdrop).toHaveAttribute('data-custom-backdrop', 'backdrop-value');
  });

  it('adds tabIndex -1 to child if not provided', () => {
    render(
      <Modal open onClose={() => {}}>
        <div data-testid="child-element">Content</div>
      </Modal>,
    );

    const child = screen.getByTestId('child-element');
    expect(child).toHaveAttribute('tabIndex', '-1');
  });

  it('preserves existing tabIndex on child', () => {
    render(
      <Modal open onClose={() => {}}>
        <div data-testid="child-element" tabIndex={0}>
          Content
        </div>
      </Modal>,
    );

    const child = screen.getByTestId('child-element');
    expect(child).toHaveAttribute('tabIndex', '0');
  });

  it('renders with custom component prop', () => {
    render(
      <Modal open component="section" onClose={() => {}}>
        <TestChild />
      </Modal>,
    );

    expect(screen.getByTestId('modal-content')).toBeInTheDocument();
  });

  it('forwards ref to the root element', () => {
    const ref = React.createRef<HTMLDivElement>();

    render(
      <Modal open ref={ref} onClose={() => {}}>
        <TestChild />
      </Modal>,
    );

    // The ref should be forwarded to the root element
    expect(ref.current).toBeDefined();
  });

  it('spreads additional props to root element', () => {
    render(
      <Modal open onClose={() => {}} data-testid="modal-with-props" id="test-modal">
        <TestChild />
      </Modal>,
    );

    const modalRoot = document.querySelector('.NovaModal-root');
    expect(modalRoot).toHaveAttribute('id', 'test-modal');
  });

  it('handles boolean props correctly', () => {
    render(
      <Modal open onClose={() => {}} disableEscapeKeyDown disableScrollLock closeAfterTransition keepMounted>
        <TestChild />
      </Modal>,
    );

    expect(screen.getByTestId('modal-content')).toBeInTheDocument();
  });

  it('handles container prop for Portal', () => {
    const customContainer = document.createElement('div');
    document.body.appendChild(customContainer);

    render(
      <Modal open container={customContainer} onClose={() => {}}>
        <TestChild />
      </Modal>,
    );

    expect(screen.getByTestId('modal-content')).toBeInTheDocument();

    document.body.removeChild(customContainer);
  });

  it('applies utility classes correctly', () => {
    render(
      <Modal open onClose={() => {}}>
        <TestChild />
      </Modal>,
    );

    const modalRoot = document.querySelector('.NovaModal-root');
    expect(modalRoot).toHaveClass('NovaModal-root');
  });

  it('applies hidden class when modal is closed and exited', () => {
    render(
      <Modal open={false} onClose={() => {}}>
        <TestChild />
      </Modal>,
    );

    // This test would need the actual useModal implementation to work properly
    // For now, we're just checking that the component doesn't crash
    expect(screen.queryByTestId('modal-content')).not.toBeInTheDocument();
  });

  it('handles complex children structure', () => {
    render(
      <Modal open onClose={() => {}}>
        <div>
          <h1>Title</h1>
          <p>Description</p>
          <button>Action</button>
        </div>
      </Modal>,
    );

    expect(screen.getByText('Title')).toBeInTheDocument();
    expect(screen.getByText('Description')).toBeInTheDocument();
    expect(screen.getByText('Action')).toBeInTheDocument();
  });
});
