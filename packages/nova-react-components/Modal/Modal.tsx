'use client';
import * as React from 'react';
import clsx from 'clsx';
import composeClasses from '@mui/utils/composeClasses';
import { ModalProps, ModalOwnerState } from './Modal.types';
import { getModalUtilityClass } from './Modal.classes';
import { useModal } from '../internal/hooks/useModal/useModal';
import useSlotProps from '@mui/utils/useSlotProps';
import { Portal } from '../Portal';
import { FocusTrap } from '../internal/components/FocusTrap';
import { ModalBackdrop, ModalRoot } from './Modal.styled';

const useUtilityClasses = (ownerState: ModalOwnerState) => {
  const { open, exited, classes } = ownerState;

  const slots = {
    root: ['root', !open && exited && 'hidden'],
    backdrop: ['backdrop'],
  };

  return composeClasses(slots, getModalUtilityClass, classes);
};

// eslint-disable-next-line react/display-name
export const Modal = React.forwardRef<Element, ModalProps>((props, ref) => {
  const {
    classes: classesProp,
    className,
    closeAfterTransition = false,
    children,
    container,
    component,
    disableAutoFocus = false,
    disableEnforceFocus = false,
    disableEscapeKeyDown = false,
    disablePortal = false,
    disableRestoreFocus = false,
    disableScrollLock = false,
    hideBackdrop = false,
    keepMounted = false,
    onClose,
    onTransitionEnter,
    onTransitionExited,
    open,
    slotProps = {},
    slots = {},
    ...other
  } = props;

  const propsWithDefaults = {
    ...props,
    closeAfterTransition,
    disableAutoFocus,
    disableEnforceFocus,
    disableEscapeKeyDown,
    disablePortal,
    disableRestoreFocus,
    disableScrollLock,
    hideBackdrop,
    keepMounted,
  };

  const { getRootProps, getBackdropProps, getTransitionProps, portalRef, isTopModal, exited, hasTransition } = useModal(
    {
      ...propsWithDefaults,
      rootRef: ref,
    },
  );

  const ownerState: ModalOwnerState = {
    ...propsWithDefaults,
    exited,
  };

  const classes = useUtilityClasses(ownerState);

  const childProps: Partial<React.HTMLAttributes<HTMLElement> & { onEnter?: () => void; onExited?: () => void }> = {};
  if (React.isValidElement(children) && (children.props as any).tabIndex === undefined) {
    childProps.tabIndex = -1;
  }

  // It's a Transition like component
  if (hasTransition) {
    const { onEnter, onExited } = getTransitionProps();
    childProps.onEnter = onEnter;
    childProps.onExited = onExited;
  }

  const RootSlot = slots.root ?? ModalRoot;
  const rootProps = useSlotProps({
    elementType: RootSlot,
    externalSlotProps: slotProps.root,
    externalForwardedProps: other,
    getSlotProps: getRootProps,
    additionalProps: {
      ref,
      as: component,
    },
    ownerState,
    className: clsx(className, classes?.root),
  });

  const BackdropSlot = slots.backdrop ?? ModalBackdrop;
  const backdropProps = useSlotProps({
    elementType: BackdropSlot,
    externalSlotProps: slotProps.backdrop,
    externalForwardedProps: {},
    getSlotProps: getBackdropProps,
    additionalProps: {
      open,
    },
    ownerState,
    className: classes?.backdrop,
  });

  if (!keepMounted && !open && (!hasTransition || exited)) {
    return null;
  }

  return (
    <Portal ref={portalRef} container={container} disablePortal={disablePortal}>
      <RootSlot {...rootProps}>
        {!hideBackdrop && <BackdropSlot {...backdropProps} />}
        <FocusTrap
          disableEnforceFocus={disableEnforceFocus}
          disableAutoFocus={disableAutoFocus}
          disableRestoreFocus={disableRestoreFocus}
          isEnabled={isTopModal}
          open={open}
        >
          {React.cloneElement(children, childProps)}
        </FocusTrap>
      </RootSlot>
    </Portal>
  );
});
