import * as React from 'react';
import { OverrideProps } from '@mui/types';
import { CreateSlotsAndSlotProps, SlotCommonProps, SlotProps } from '../types/slot';
import { TransitionProps } from '../transitions/transition';
import { FadeProps } from '../Fade';
import { BackdropClasses } from './Backdrop.classes';

export interface BackdropSlots {
  /**
   * The component that renders the root.
   * @default 'div'
   */
  root: React.ElementType;
  /**
   * The component that renders the transition.
   * [Follow this guide](https://mui.com/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.
   * @default Fade
   */
  transition: React.JSXElementConstructor<TransitionProps & { children: React.ReactElement<unknown, any> }>;
}

export interface BackdropComponentsPropsOverrides {}

export interface BackdropTransitionSlotPropsOverrides {}

export type BackdropSlotsAndSlotProps = CreateSlotsAndSlotProps<
  BackdropSlots,
  {
    root: SlotProps<React.ElementType<HTMLDivElement>, BackdropComponentsPropsOverrides, BackdropOwnerState>;
    transition: SlotProps<
      React.JSXElementConstructor<TransitionProps>,
      BackdropTransitionSlotPropsOverrides,
      BackdropOwnerState
    >;
  }
>;

export interface BackdropTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P & {
    /**
     * The content of the component.
     */
    children?: React.ReactNode;
    /**
     * Override or extend the styles applied to the component.
     */
    classes?: Partial<BackdropClasses>;
    /**
     * If `true`, the backdrop is invisible.
     * It can be used when rendering a popover or a custom select component.
     * @default false
     */
    invisible?: boolean;
    /**
     * If `true`, the component is shown.
     */
    open: boolean;
    /**
     * The duration for the transition, in milliseconds.
     * You may specify a single timeout for all transitions, or individually with an object.
     */
    transitionDuration?: TransitionProps['timeout'];
  } & Partial<Omit<FadeProps, 'children'>> &
    BackdropSlotsAndSlotProps;
  defaultComponent: D;
}

export type BackdropProps<
  D extends React.ElementType = BackdropTypeMap['defaultComponent'],
  P = SlotCommonProps,
> = OverrideProps<BackdropTypeMap<P, D>, D>;

export interface BackdropOwnerState extends BackdropProps {}
