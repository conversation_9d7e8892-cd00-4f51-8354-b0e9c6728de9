/// <reference types="@testing-library/jest-dom" />
import '@testing-library/jest-dom/vitest';
import React from 'react';
import { render, screen, cleanup } from '@testing-library/react';
import { describe, expect, it, afterEach, vi } from 'vitest';
import { Backdrop } from './Backdrop';

// Mock the Fade component
vi.mock('../Fade', () => ({
  Fade: ({ children, in: inProp, timeout, 'data-testid': dataTestId, ...props }: any) => {
    if (inProp) {
      // Preserve the fade-transition testid unless overridden
      const testId = dataTestId || 'fade-transition';
      return (
        <div data-testid={testId} data-timeout={timeout} {...props}>
          {children}
        </div>
      );
    }
    return null;
  },
}));

afterEach(() => {
  cleanup();
  vi.clearAllMocks();
});

describe('Backdrop', () => {
  it('renders when open is true', () => {
    render(
      <Backdrop open>
        <div data-testid="backdrop-content">Backdrop Content</div>
      </Backdrop>,
    );

    expect(screen.getByTestId('backdrop-content')).toBeInTheDocument();
    expect(screen.getByText('Backdrop Content')).toBeInTheDocument();
    expect(screen.getByTestId('fade-transition')).toBeInTheDocument();
  });

  it('does not render when open is false', () => {
    render(
      <Backdrop open={false}>
        <div data-testid="backdrop-content">Backdrop Content</div>
      </Backdrop>,
    );

    expect(screen.queryByTestId('backdrop-content')).not.toBeInTheDocument();
    expect(screen.queryByTestId('fade-transition')).not.toBeInTheDocument();
  });

  it('applies default component as div', () => {
    render(
      <Backdrop open>
        <div>Content</div>
      </Backdrop>,
    );

    const backdrop = document.querySelector('.NovaBackdrop-root');
    expect(backdrop?.tagName).toBe('DIV');
  });

  it('applies custom component', () => {
    render(
      <Backdrop open component="section">
        <div>Content</div>
      </Backdrop>,
    );

    const backdrop = document.querySelector('.NovaBackdrop-root');
    expect(backdrop?.tagName).toBe('SECTION');
  });

  it('applies invisible class when invisible is true', () => {
    render(
      <Backdrop open invisible>
        <div>Content</div>
      </Backdrop>,
    );

    const backdrop = document.querySelector('.NovaBackdrop-root');
    expect(backdrop).toHaveClass('NovaBackdrop-invisible');
  });

  it('does not apply invisible class when invisible is false', () => {
    render(
      <Backdrop open invisible={false}>
        <div>Content</div>
      </Backdrop>,
    );

    const backdrop = document.querySelector('.NovaBackdrop-root');
    expect(backdrop).not.toHaveClass('NovaBackdrop-invisible');
  });

  it('applies custom className', () => {
    render(
      <Backdrop open className="custom-backdrop">
        <div>Content</div>
      </Backdrop>,
    );

    const backdrop = document.querySelector('.NovaBackdrop-root');
    expect(backdrop).toHaveClass('custom-backdrop');
  });

  it('passes transitionDuration to transition component', () => {
    render(
      <Backdrop open transitionDuration={500}>
        <div>Content</div>
      </Backdrop>,
    );

    const transition = screen.getByTestId('fade-transition');
    expect(transition).toHaveAttribute('data-timeout', '500');
  });

  it('renders with custom root slot', () => {
    const CustomRoot = React.forwardRef<HTMLDivElement, any>((props, ref) => (
      <div ref={ref} data-testid="custom-root" {...props} />
    ));

    render(
      <Backdrop
        open
        slots={
          {
            root: CustomRoot,
            transition: undefined,
          } as any
        }
      >
        <div>Content</div>
      </Backdrop>,
    );

    expect(screen.getByTestId('custom-root')).toBeInTheDocument();
  });

  it('renders with custom transition slot', () => {
    const CustomTransition = ({ children, in: inProp, ...props }: any) => {
      if (inProp) {
        return (
          <div data-testid="custom-transition" {...props}>
            {children}
          </div>
        );
      }
      return null;
    };

    render(
      <Backdrop
        open
        slots={
          {
            root: undefined,
            transition: CustomTransition,
          } as any
        }
      >
        <div>Content</div>
      </Backdrop>,
    );

    expect(screen.getByTestId('custom-transition')).toBeInTheDocument();
  });

  it('renders with legacy TransitionComponent prop', () => {
    const CustomTransition = ({ children, in: inProp, ...props }: any) => {
      if (inProp) {
        return (
          <div data-testid="legacy-transition" {...props}>
            {children}
          </div>
        );
      }
      return null;
    };

    render(
      <Backdrop open TransitionComponent={CustomTransition}>
        <div>Content</div>
      </Backdrop>,
    );

    expect(screen.getByTestId('legacy-transition')).toBeInTheDocument();
    expect(screen.queryByTestId('fade-transition')).not.toBeInTheDocument();
  });

  it('passes slotProps to root slot', () => {
    render(
      <Backdrop
        open
        slotProps={{
          root: {
            'data-custom': 'test-value',
          } as any,
        }}
      >
        <div>Content</div>
      </Backdrop>,
    );

    const backdrop = document.querySelector('.NovaBackdrop-root');
    expect(backdrop).toHaveAttribute('data-custom', 'test-value');
  });

  it('passes slotProps to transition slot', () => {
    render(
      <Backdrop
        open
        slotProps={{
          transition: {
            'data-custom-transition': 'transition-value',
          },
        }}
      >
        <div>Content</div>
      </Backdrop>,
    );

    const transition = screen.getByTestId('fade-transition');
    expect(transition).toHaveAttribute('data-custom-transition', 'transition-value');
  });

  it('forwards ref to the root element', () => {
    const ref = React.createRef<HTMLDivElement>();

    render(
      <Backdrop open ref={ref}>
        <div>Content</div>
      </Backdrop>,
    );

    expect(ref.current).toBeDefined();
    expect(ref.current).toHaveClass('NovaBackdrop-root');
  });

  it('spreads additional props to root component', () => {
    render(
      <Backdrop open data-custom="backdrop-props" id="test-backdrop">
        <div>Content</div>
      </Backdrop>,
    );

    const backdrop = document.querySelector('.NovaBackdrop-root');
    expect(backdrop).toHaveAttribute('data-custom', 'backdrop-props');
    expect(backdrop).toHaveAttribute('id', 'test-backdrop');
  });

  it('sets aria-hidden on root element', () => {
    render(
      <Backdrop open>
        <div>Content</div>
      </Backdrop>,
    );

    const backdrop = document.querySelector('.NovaBackdrop-root');
    expect(backdrop).toHaveAttribute('aria-hidden');
  });

  it('applies utility classes correctly', () => {
    render(
      <Backdrop open>
        <div>Content</div>
      </Backdrop>,
    );

    const backdrop = document.querySelector('.NovaBackdrop-root');
    expect(backdrop).toHaveClass('NovaBackdrop-root');
  });

  it('renders children correctly', () => {
    render(
      <Backdrop open>
        <div>
          <h1>Title</h1>
          <p>Description</p>
          <button>Action</button>
        </div>
      </Backdrop>,
    );

    expect(screen.getByText('Title')).toBeInTheDocument();
    expect(screen.getByText('Description')).toBeInTheDocument();
    expect(screen.getByText('Action')).toBeInTheDocument();
  });

  it('handles boolean props correctly', () => {
    render(
      <Backdrop open invisible>
        <div>Content</div>
      </Backdrop>,
    );

    const backdrop = document.querySelector('.NovaBackdrop-root');
    expect(backdrop).toHaveClass('NovaBackdrop-invisible');
  });

  it('handles custom classes prop', () => {
    const customClasses = {
      root: 'custom-root-class',
      invisible: 'custom-invisible-class',
    };

    render(
      <Backdrop open invisible classes={customClasses}>
        <div>Content</div>
      </Backdrop>,
    );

    const backdrop = document.querySelector('.NovaBackdrop-root');
    expect(backdrop).toHaveClass('custom-root-class');
    expect(backdrop).toHaveClass('custom-invisible-class');
  });

  it('prioritizes slots.transition over TransitionComponent prop', () => {
    const SlotsTransition = ({ children, in: inProp }: any) => {
      if (inProp) {
        return <div data-testid="slots-transition">{children}</div>;
      }
      return null;
    };

    const LegacyTransition = ({ children, in: inProp }: any) => {
      if (inProp) {
        return <div data-testid="legacy-transition">{children}</div>;
      }
      return null;
    };

    render(
      <Backdrop
        open
        slots={
          {
            root: undefined,
            transition: SlotsTransition,
          } as any
        }
        TransitionComponent={LegacyTransition}
      >
        <div>Content</div>
      </Backdrop>,
    );

    expect(screen.getByTestId('slots-transition')).toBeInTheDocument();
    expect(screen.queryByTestId('legacy-transition')).not.toBeInTheDocument();
  });
});
