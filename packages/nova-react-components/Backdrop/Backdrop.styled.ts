import { styled } from '@pigment-css/react';
import { BackdropOwnerState } from './Backdrop.types';

export const BackdropRoot = styled('div', {
  name: 'NovaBackdrop',
  slot: 'Root',
  overridesResolver: (props, styles) => {
    const { ownerState } = props;
    return [styles.root, ownerState.invisible && styles.invisible];
  },
})<BackdropOwnerState>({
  position: 'fixed',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  right: 0,
  bottom: 0,
  top: 0,
  left: 0,
  backgroundColor: 'rgba(0, 0, 0, 0.5)',
  WebkitTapHighlightColor: 'transparent',
  variants: [
    {
      props: { invisible: true },
      style: {
        backgroundColor: 'transparent',
      },
    },
  ],
});
