'use client';
import * as React from 'react';
import { unstable_composeClasses as composeClasses } from '@mui/utils';
import { getBackdropUtilityClass } from './Backdrop.classes';
import { BackdropProps, BackdropSlots, BackdropOwnerState } from './Backdrop.types';
import useSlotProps from '@mui/utils/useSlotProps';
import { BackdropRoot } from './Backdrop.styled';
import { Fade } from '../Fade';

const removeOwnerState = (props: any) => {
  const { ownerState, ...rest } = props;
  return rest;
};

const useUtilityClasses = (ownerState: BackdropOwnerState) => {
  const { classes, invisible } = ownerState;

  const slots = {
    root: ['root', invisible && 'invisible'],
  };

  return composeClasses(slots, getBackdropUtilityClass, classes);
};

// eslint-disable-next-line react/display-name
export const Backdrop = React.forwardRef<HTMLElement, BackdropProps>(function Backdrop(props, ref) {
  const {
    children,
    className,
    component = 'div',
    invisible = false,
    open,
    slotProps = {},
    slots = {} as Partial<BackdropSlots>,
    TransitionComponent,
    transitionDuration,
    ...other
  } = props;

  const ownerState: BackdropOwnerState = {
    ...props,
    component,
    invisible,
  };

  const classes = useUtilityClasses(ownerState);

  const backwardCompatibleSlots = {
    transition: TransitionComponent,
    ...slots,
  };

  const RootSlot = backwardCompatibleSlots.root ?? BackdropRoot;
  const rootProps = useSlotProps({
    elementType: RootSlot,
    externalSlotProps: slotProps.root as any,
    externalForwardedProps: other,
    additionalProps: {
      ref,
      as: component,
    },
    ownerState,
    className: [classes.root, className],
  });

  const TransitionSlot = backwardCompatibleSlots.transition ?? Fade;
  const transitionProps = useSlotProps({
    elementType: TransitionSlot,
    externalSlotProps: slotProps.transition,
    ownerState,
  });
  const transitionPropsRemoved = removeOwnerState(transitionProps);

  return (
    <TransitionSlot in={open} timeout={transitionDuration} {...transitionPropsRemoved}>
      <RootSlot aria-hidden {...rootProps}>
        {children}
      </RootSlot>
    </TransitionSlot>
  );
});
