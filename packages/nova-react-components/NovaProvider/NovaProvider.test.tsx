import '@testing-library/jest-dom/vitest';
import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import NovaProvider from './NovaProvider';
import { Locale } from '../Locale';
import enUS from '../Locale/en_US';

const TestComponent = () => <div data-testid="test-component">Test Component</div>;

describe('NovaProvider', () => {
  it('renders children without locale or RTL', () => {
    render(
      <NovaProvider>
        <TestComponent />
      </NovaProvider>,
    );

    expect(screen.getByTestId('test-component')).toBeInTheDocument();
  });

  it('renders children with locale', () => {
    const locale: Locale = enUS; // Adjust according to your Locale type

    render(
      <NovaProvider locale={locale}>
        <TestComponent />
      </NovaProvider>,
    );

    expect(screen.getByTestId('test-component')).toBeInTheDocument();
  });

  it('renders children with RTL', () => {
    render(
      <NovaProvider rtl>
        <TestComponent />
      </NovaProvider>,
    );

    expect(screen.getByTestId('test-component')).toBeInTheDocument();
  });

  it('renders children with both locale and RTL', () => {
    const locale: Locale = enUS; // Adjust according to your Locale type

    render(
      <NovaProvider locale={locale} rtl>
        <TestComponent />
      </NovaProvider>,
    );

    expect(screen.getByTestId('test-component')).toBeInTheDocument();
  });
});
