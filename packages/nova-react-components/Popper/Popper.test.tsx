/// <reference types="@testing-library/jest-dom" />
import '@testing-library/jest-dom/vitest';
import React from 'react';
import { render, screen, cleanup } from '@testing-library/react';
import { describe, expect, it, afterEach, vi, beforeEach } from 'vitest';
import { Popper } from './Popper';

// Mock Portal component
vi.mock('../Portal', () => ({
  Portal: ({ children, disablePortal }: any) => {
    return disablePortal ? children : <div data-testid="portal">{children}</div>;
  },
}));

// Mock @popperjs/core
vi.mock('@popperjs/core', () => ({
  createPopper: vi.fn(() => ({
    destroy: vi.fn(),
    forceUpdate: vi.fn(),
    setOptions: vi.fn(),
    state: {
      placement: 'bottom',
    },
  })),
}));

// Mock RTL Provider
vi.mock('../RtlProvider', () => ({
  useRtl: vi.fn(() => false),
}));

// Mock utils
vi.mock('../utils/ClassNameConfigurator', () => ({
  useClassNamesOverride: vi.fn((fn) => fn),
}));

// Create a mock anchor element
const createMockAnchorEl = () => {
  const element = document.createElement('div');
  element.getBoundingClientRect = vi.fn(() => ({
    top: 100,
    left: 100,
    right: 200,
    bottom: 150,
    width: 100,
    height: 50,
  })) as any;
  document.body.appendChild(element);
  return element;
};

afterEach(() => {
  cleanup();
  vi.clearAllMocks();
  // Clean up any elements added to document.body
  document.body.innerHTML = '';
});

describe('Popper', () => {
  let anchorEl: HTMLElement;

  beforeEach(() => {
    anchorEl = createMockAnchorEl();
  });

  it('renders children when open is true', () => {
    render(
      <Popper open anchorEl={anchorEl}>
        <div data-testid="popper-content">Popper content</div>
      </Popper>,
    );

    expect(screen.getByTestId('popper-content')).toBeInTheDocument();
    expect(screen.getByText('Popper content')).toBeInTheDocument();
  });

  it('does not render children when open is false and keepMounted is false', () => {
    render(
      <Popper open={false} anchorEl={anchorEl}>
        <div data-testid="popper-content">Popper content</div>
      </Popper>,
    );

    expect(screen.queryByTestId('popper-content')).not.toBeInTheDocument();
  });

  it('renders children when open is false but keepMounted is true', () => {
    render(
      <Popper open={false} anchorEl={anchorEl} keepMounted>
        <div data-testid="popper-content">Popper content</div>
      </Popper>,
    );

    expect(screen.getByTestId('popper-content')).toBeInTheDocument();
  });

  it('renders with default placement bottom', () => {
    render(
      <Popper open anchorEl={anchorEl}>
        <div>Content</div>
      </Popper>,
    );

    const popperElement = screen.getByTestId('NovaPopper');
    expect(popperElement).toHaveAttribute('data-placement', 'bottom');
  });

  it('renders with custom placement', () => {
    render(
      <Popper open anchorEl={anchorEl} placement="top-start">
        <div>Content</div>
      </Popper>,
    );

    const popperElement = screen.getByTestId('NovaPopper');
    expect(popperElement).toHaveAttribute('data-placement', 'top-start');
  });

  it('applies disablePortal prop', () => {
    render(
      <Popper open anchorEl={anchorEl} disablePortal>
        <div>Content</div>
      </Popper>,
    );

    const popperElement = screen.getByTestId('NovaPopper');
    expect(popperElement).toHaveAttribute('data-disable-portal', 'true');
    expect(screen.queryByTestId('portal')).not.toBeInTheDocument();
  });

  it('uses portal by default', () => {
    render(
      <Popper open anchorEl={anchorEl}>
        <div>Content</div>
      </Popper>,
    );

    expect(screen.getByTestId('portal')).toBeInTheDocument();
    const popperElement = screen.getByTestId('NovaPopper');
    expect(popperElement).toHaveAttribute('data-disable-portal', 'false');
  });

  it('renders function children with correct props', () => {
    const mockChildren = vi.fn().mockReturnValue(<div>Function children</div>);

    render(
      <Popper open anchorEl={anchorEl} placement="top">
        {mockChildren}
      </Popper>,
    );

    expect(mockChildren).toHaveBeenCalledWith({
      placement: 'top',
    });
    expect(screen.getByText('Function children')).toBeInTheDocument();
  });

  it('renders function children with TransitionProps when transition is enabled', () => {
    const mockChildren = vi.fn().mockReturnValue(<div>Function children</div>);

    render(
      <Popper open anchorEl={anchorEl} transition>
        {mockChildren}
      </Popper>,
    );

    expect(mockChildren).toHaveBeenCalledWith(
      expect.objectContaining({
        placement: 'bottom',
        TransitionProps: expect.objectContaining({
          in: true,
          onEnter: expect.any(Function),
          onExited: expect.any(Function),
        }),
      }),
    );
  });

  it('handles anchorEl as function', () => {
    const anchorElFunction = () => anchorEl;

    render(
      <Popper open anchorEl={anchorElFunction}>
        <div>Content</div>
      </Popper>,
    );

    expect(screen.getByText('Content')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    render(
      <Popper open anchorEl={anchorEl} className="custom-popper">
        <div>Content</div>
      </Popper>,
    );

    const popperElement = screen.getByTestId('NovaPopper');
    expect(popperElement).toHaveClass('custom-popper');
  });

  it('renders with custom component', () => {
    const CustomComponent = React.forwardRef<HTMLDivElement, any>(({ children, ownerState, ...props }, ref) => (
      <div ref={ref} {...props}>
        {children}
      </div>
    ));

    render(
      <Popper open anchorEl={anchorEl} slots={{ root: CustomComponent }}>
        <div>Content</div>
      </Popper>,
    );

    expect(screen.getByTestId('NovaPopper')).toBeInTheDocument();
    // The default popper testid should not be present when using custom component
    expect(screen.queryByTestId('popper')).not.toBeInTheDocument();
  });

  it('forwards ref to the root element', () => {
    const ref = React.createRef<HTMLDivElement>();

    render(
      <Popper open anchorEl={anchorEl} ref={ref}>
        <div>Content</div>
      </Popper>,
    );

    expect(ref.current).toBeInstanceOf(HTMLDivElement);
  });

  it('spreads additional props to root element', () => {
    render(
      <Popper open anchorEl={anchorEl} id="test-popper" data-custom="test">
        <div>Content</div>
      </Popper>,
    );

    const popperElement = screen.getByTestId('NovaPopper');
    expect(popperElement).toHaveAttribute('id', 'test-popper');
    expect(popperElement).toHaveAttribute('data-custom', 'test');
  });

  it('applies correct role attribute', () => {
    render(
      <Popper open anchorEl={anchorEl}>
        <div>Content</div>
      </Popper>,
    );

    const popperElement = screen.getByTestId('NovaPopper');
    expect(popperElement).toHaveAttribute('role', 'tooltip');
  });

  it('applies fixed positioning styles', () => {
    render(
      <Popper open anchorEl={anchorEl}>
        <div>Content</div>
      </Popper>,
    );

    const popperElement = screen.getByTestId('NovaPopper');
    expect(popperElement).toHaveStyle({
      position: 'fixed',
      top: '0',
      left: '0',
    });
  });

  it('applies custom style prop', () => {
    render(
      <Popper open anchorEl={anchorEl} style={{ zIndex: 1000, backgroundColor: 'red' }}>
        <div>Content</div>
      </Popper>,
    );

    const popperElement = screen.getByTestId('NovaPopper');
    expect(popperElement).toHaveStyle({
      'z-index': '1000',
      'background-color': 'rgb(255, 0, 0)',
    });
  });

  it('applies display none when closed and keepMounted', () => {
    render(
      <Popper open={false} anchorEl={anchorEl} keepMounted>
        <div>Content</div>
      </Popper>,
    );

    const popperElement = screen.getByTestId('NovaPopper');
    expect(popperElement).toHaveStyle({ display: 'none' });
  });

  it('handles container prop', () => {
    const customContainer = document.createElement('div');
    document.body.appendChild(customContainer);

    render(
      <Popper open anchorEl={anchorEl} container={customContainer}>
        <div>Content</div>
      </Popper>,
    );

    expect(screen.getByText('Content')).toBeInTheDocument();
  });

  it('applies custom slotProps', () => {
    render(
      <Popper
        open
        anchorEl={anchorEl}
        slotProps={{
          root: {
            className: 'slot-class',
          } as any,
        }}
      >
        <div>Content</div>
      </Popper>,
    );

    const popperElement = screen.getByTestId('NovaPopper');
    expect(popperElement).toHaveClass('slot-class');
  });

  it('handles modifiers prop', () => {
    const customModifiers = [
      {
        name: 'offset',
        options: {
          offset: [0, 8],
        },
      },
    ];

    render(
      <Popper open anchorEl={anchorEl} modifiers={customModifiers}>
        <div>Content</div>
      </Popper>,
    );

    expect(screen.getByText('Content')).toBeInTheDocument();
  });

  it('handles popperOptions prop', () => {
    const popperOptions = {
      strategy: 'fixed' as const,
      placement: 'top' as const,
    };

    render(
      <Popper open anchorEl={anchorEl} popperOptions={popperOptions}>
        <div>Content</div>
      </Popper>,
    );

    expect(screen.getByText('Content')).toBeInTheDocument();
  });

  it('handles popperRef prop', () => {
    const popperRef = React.createRef<any>();

    render(
      <Popper open anchorEl={anchorEl} popperRef={popperRef}>
        <div>Content</div>
      </Popper>,
    );

    expect(screen.getByText('Content')).toBeInTheDocument();
  });

  it('handles direction prop for RTL', () => {
    render(
      <Popper open anchorEl={anchorEl} direction="rtl" placement="bottom-start">
        <div>Content</div>
      </Popper>,
    );

    const popperElement = screen.getByTestId('NovaPopper');
    expect(popperElement).toHaveAttribute('dir', 'rtl');
  });

  it('flips placement for RTL direction', () => {
    render(
      <Popper open anchorEl={anchorEl} direction="rtl" placement="bottom-end">
        <div>Content</div>
      </Popper>,
    );

    // The component should flip bottom-end to bottom-start for RTL
    expect(screen.getByText('Content')).toBeInTheDocument();
  });

  it('handles null anchorEl gracefully', () => {
    render(
      <Popper open anchorEl={null} keepMounted>
        <div>Content</div>
      </Popper>,
    );

    expect(screen.getByText('Content')).toBeInTheDocument();
  });

  it('handles undefined anchorEl gracefully', () => {
    render(
      <Popper open anchorEl={undefined} keepMounted>
        <div>Content</div>
      </Popper>,
    );

    expect(screen.getByText('Content')).toBeInTheDocument();
  });

  it('applies utility classes', () => {
    render(
      <Popper open anchorEl={anchorEl}>
        <div>Content</div>
      </Popper>,
    );

    const popperElement = screen.getByTestId('NovaPopper');
    expect(popperElement).toHaveClass('NovaPopper-root');
  });
});
