import {
  unstable_generateUtilityClasses as generateUtilityClasses,
  unstable_generateUtilityClass as generateUtilityClass,
} from '@mui/utils';

const COMPONENT_NAME = 'NovaPopper';

export interface PopperClasses {
  /** Class name applied to the root element. */
  root: string;
}

export type PopperClassKey = keyof PopperClasses;

export function getPopperUtilityClass(slot: string): string {
  return generateUtilityClass(COMPONENT_NAME, slot, 'Nova');
}

export const popperClasses: PopperClasses = generateUtilityClasses(COMPONENT_NAME, ['root'], 'Nova');
