import { styled } from '@pigment-css/react';
import { Modal, ModalProps } from '../../Modal';
import { DrawerRootOwnerState } from './DrawerRoot.types';

export const DrawerModalRoot = styled(Modal, {
  name: 'NovaDrawer',
  slot: 'ModalRoot',
})<ModalProps>(({ theme }) => ({
  position: 'fixed',
  right: 0,
  bottom: 0,
  top: 0,
  left: 0,
  zIndex: 1200,
  transitionProperty: 'visibility',
  transitionDelay: '0s',
  variants: [
    {
      props: { open: false },
      style: {
        visibility: 'hidden',
        transitionDelay: 'var(--nova-drawer-modal-transition-delay)',
      },
    },
  ],
}));

export const DrawerDockedRoot = styled('div', {
  name: 'NovaDrawer',
  slot: 'DockedRoot',
})<DrawerRootOwnerState>(({ theme }) => ({
  flex: '0 0 auto',
}));

export const DrawerContainer = styled('div', {
  name: '<PERSON>Drawer',
  slot: 'Container',
})<DrawerRootOwnerState>(({ theme }) => ({
  '--nova-drawer-rail-width': '80px',
  '@media (max-width: 599px)': {
    '--nova-drawer-rail-width': '72px',
  },
  boxSizing: 'border-box',
  width: 'var(--nova-drawer-container-width)',
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  flex: '1 0 auto',
  overflowY: 'auto',
  overflowX: 'hidden',
  WebkitOverflowScrolling: 'touch',
  position: 'fixed',
  zIndex: 1200,
  outline: 0,
  backgroundColor: theme.vars.palette.surfaceContainerLow,
  borderWidth: '0',
  borderColor: theme.vars.palette.outlineVariant,
  borderStyle: 'solid',
  transform: 'translateX(-100%)',
  transitionProperty: 'transform, width',
  transitionDuration: 'var(--nova-drawer-transition-duration-enter)',
  transitionTimingFunction: 'cubic-bezier(0.4, 0, 0.6, 1)',
  variants: [
    {
      props: (ownerState) => !ownerState.expanded || !ownerState.open,
      style: {
        transitionDuration: 'var(--nova-drawer-transition-duration-exit)',
      },
    },
    // Left anchor
    {
      props: { anchor: 'left' },
      style: {
        top: 0,
        left: 0,
        borderRightWidth: '1px',
        '@media (max-width: 599px)': {
          borderTopRightRadius: '1rem',
          borderBottomRightRadius: '1rem',
        },
      },
    },
    {
      props: { anchor: 'left', open: false },
      style: {
        transform: 'translateX(-100%)',
      },
    },
    {
      props: { anchor: 'left', open: true },
      style: {
        transform: 'translateX(0)',
      },
    },
    // Right anchor
    {
      props: { anchor: 'right' },
      style: {
        top: 0,
        right: 0,
        borderLeftWidth: '1px',
        '@media (max-width: 599px)': {
          borderTopLeftRadius: '1rem',
          borderBottomLeftRadius: '1rem',
        },
      },
    },
    {
      props: { anchor: 'right', open: false },
      style: {
        transform: 'translateX(100%)',
      },
    },
    {
      props: { anchor: 'right', open: true },
      style: {
        transform: 'translateX(0)',
      },
    },
    // Top anchor
    {
      props: { anchor: 'top' },
      style: {
        width: '100%',
        height: 'var(--nova-drawer-width)',
        top: 0,
        left: 0,
        borderBottomWidth: '1px',
        '@media (max-width: 599px)': {
          borderBottomLeftRadius: '1rem',
          borderBottomRightRadius: '1rem',
        },
      },
    },
    {
      props: { anchor: 'top', open: false },
      style: {
        transform: 'translateY(-100%)',
      },
    },
    {
      props: { anchor: 'top', open: true },
      style: {
        transform: 'translateY(0)',
      },
    },
    // Bottom anchor
    {
      props: { anchor: 'bottom' },
      style: {
        width: '100%',
        height: 'var(--nova-drawer-width)',
        bottom: 0,
        left: 0,
        borderTopWidth: '1px',
        '@media (max-width: 599px)': {
          borderTopLeftRadius: '1rem',
          borderTopRightRadius: '1rem',
        },
      },
    },
    {
      props: { anchor: 'bottom', open: false },
      style: {
        transform: 'translateY(100%)',
      },
    },
    {
      props: { anchor: 'bottom', open: true },
      style: {
        transform: 'translateY(0)',
      },
    },
  ],
}));

export const SubNavDrawerContainer = styled(DrawerContainer, {
  name: 'NovaDrawer',
  slot: 'SubNavDrawerContainer',
})<DrawerRootOwnerState>(({ theme }) => ({
  zIndex: 1199,
  width: 'calc(var(--nova-drawer-rail-width) + 274px)',
  paddingLeft: 'var(--nova-drawer-rail-width)',
  transform: 'translateX(-100%)',
  transitionProperty: 'transform',
  transitionDuration: 'var(--nova-drawer-transition-duration-enter)',
  transitionTimingFunction: 'cubic-bezier(0.4, 0, 0.6, 1)',
  variants: [
    {
      props: { isSubNavDrawerOpen: false },
      style: {
        transitionDuration: 'var(--nova-drawer-transition-duration-exit)',
      },
    },
    // Left anchor
    {
      props: { anchor: 'left' },
      style: {
        top: 0,
        left: 0,
      },
    },
    {
      props: { anchor: 'left', isSubNavDrawerOpen: false },
      style: {
        transform: 'translateX(-100%)',
      },
    },
    {
      props: { anchor: 'left', isSubNavDrawerOpen: true },
      style: {
        transform: 'translateX(0)',
      },
    },
    // Right anchor
    {
      props: { anchor: 'right' },
      style: {
        top: 0,
        right: 0,
        paddingLeft: 0,
        paddingRight: 'var(--nova-drawer-rail-width)',
      },
    },
    {
      props: { anchor: 'right', isSubNavDrawerOpen: false },
      style: {
        transform: 'translateX(100%)',
      },
    },
    {
      props: { anchor: 'right', isSubNavDrawerOpen: true },
      style: {
        transform: 'translateX(0)',
      },
    },
  ],
}));

export const Backdrop = styled('div', {
  name: 'NovaDrawer',
  slot: 'Backdrop',
})<DrawerRootOwnerState>(({ theme }) => ({
  position: 'fixed',
  inset: 0,
  right: 0,
  bottom: 0,
  top: 0,
  left: 0,
  zIndex: -1,
  backgroundColor: 'rgba(0, 0, 0, 0.5)',
  WebkitTapHighlightColor: 'transparent',
  opacity: 0,
  transitionProperty: 'opacity',
  transitionDuration: 'var(--nova-drawer-transition-duration-enter)',
  transitionTimingFunction: 'cubic-bezier(0.4, 0, 0.6, 1)',
  variants: [
    {
      props: { open: true },
      style: {
        opacity: 1,
        transitionDuration: 'var(--nova-drawer-transition-duration-exit)',
      },
    },
  ],
}));
