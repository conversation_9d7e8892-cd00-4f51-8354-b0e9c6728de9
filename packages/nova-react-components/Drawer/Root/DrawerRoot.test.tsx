import '@testing-library/jest-dom/vitest';
import React from 'react';
import { render, screen, cleanup } from '@testing-library/react';
import { describe, expect, it, afterEach } from 'vitest';
import { DrawerRoot } from './DrawerRoot.tsx';
import { DrawerNavItem } from '../NavItem/DrawerNavItem.tsx';

afterEach(() => {
  cleanup();
});

describe('Drawer', () => {
  describe('Render Drawer', () => {
    it('should render with default slot classes', () => {
      render(<DrawerRoot data-testid="NovaDrawerRoot-root" />);
      expect(screen.getByTestId('NovaDrawerRoot-root')).toBeDefined();
      expect(screen.getByTestId('NovaDrawerRoot-root')).toHaveClass('NovaDrawerRoot-root');
    });

    it('should render slot classes in collapsed mode', () => {
      render(<DrawerRoot data-testid="NovaDrawerRoot-root" expanded={false} />);
      expect(screen.getByTestId('NovaDrawerRoot-root')).toHaveClass('NovaDrawerRoot-collapsed');
    });

    it('should change width successfully', () => {
      render(
        <DrawerRoot
          width={300}
          slotProps={{
            container: { 'data-testid': 'NovaDrawerRoot-container' },
          }}
        />,
      );
      expect(screen.getByTestId('NovaDrawerRoot-container')).toBeDefined();
      expect(screen.getByTestId('NovaDrawerRoot-container')).toHaveStyle({ width: 300 });
    });

    it('should render showRailLabel when label defined', () => {
      render(
        <DrawerRoot data-testid="NovaDrawerRoot-root" expanded={false} showRailLabel>
          <DrawerNavItem label="Label" />
        </DrawerRoot>,
      );
      expect(screen.getAllByText('Label')).toHaveLength(2);
    });

    it('should render showRailLabel when railLabel defined', () => {
      render(
        <DrawerRoot data-testid="NovaDrawerRoot-root" expanded={false} showRailLabel>
          <DrawerNavItem label="Label" />
        </DrawerRoot>,
      );
      expect(screen.getAllByText('Label').length).toBeGreaterThan(0);
    });

    it('should slotProps working', () => {
      render(
        <DrawerRoot
          slotProps={{
            container: { 'data-testid': 'NovaDrawerRoot-container' },
          }}
        />,
      );
      expect(screen.getByTestId('NovaDrawerRoot-container')).toBeDefined();
    });

    it('should render with className', () => {
      const { container } = render(
        <DrawerRoot
          data-testid="NovaDrawerRoot-root"
          className="drawer-class"
          slotProps={{
            container: { className: 'container-class' },
          }}
        />,
      );
      expect(screen.getByTestId('NovaDrawerRoot-root')).toHaveClass('drawer-class');
      expect(container.querySelector('.container-class')).toBeDefined();
    });
  });

  describe('prop: variant=temporary', () => {
    it('should render with default slot classes', () => {
      render(<DrawerRoot data-testid="NovaDrawerRoot-root" />);
      expect(screen.getByTestId('NovaDrawerRoot-root')).toHaveClass('NovaDrawerRoot-root');
      expect(screen.getByTestId('NovaDrawerRoot-root')).toHaveClass('NovaDrawerRoot-variantTemporary');
    });
  });
  it('should set the custom className for Modal when variant is temporary', () => {
    const { container } = render(
      <DrawerRoot data-testid="NovaDrawerRoot-root" className="woofDrawer" open variant="temporary">
        <div />
      </DrawerRoot>,
    );
    expect(screen.getByTestId('NovaDrawerRoot-root')).toHaveClass('woofDrawer');
  });

  it('should be closed by default', () => {
    render(
      <DrawerRoot data-testid="NovaDrawerRoot-root">
        <div />
      </DrawerRoot>,
    );
    expect(screen.queryByTestId('NovaDrawerRoot-root')).toHaveClass('NovaModal-hidden');
  });

  it('should be opened when open=true', () => {
    render(
      <DrawerRoot open>
        <div data-testid="child" />
      </DrawerRoot>,
    );
    expect(screen.getByTestId('child')).toBeDefined();
  });
});

describe('prop: variant=persistent', () => {
  it('should render with default slot classes', () => {
    render(<DrawerRoot data-testid="NovaDrawerRoot-root" variant="persistent" />);
    expect(screen.getByTestId('NovaDrawerRoot-root')).toHaveClass('NovaDrawerRoot-root');
    expect(screen.getByTestId('NovaDrawerRoot-root')).toHaveClass('NovaDrawerRoot-variantPersistent');
  });
});

describe('prop: variant=permanent', () => {
  it('should render with default slot classes', () => {
    render(<DrawerRoot data-testid="NovaDrawerRoot-root" variant="permanent" />);
    expect(screen.getByTestId('NovaDrawerRoot-root')).toHaveClass('NovaDrawerRoot-root');
    expect(screen.getByTestId('NovaDrawerRoot-root')).toHaveClass('NovaDrawerRoot-variantPermanent');
  });
});

describe('Anchor', () => {
  it('Anchor: left', () => {
    render(<DrawerRoot data-testid="NovaDrawerRoot-root" />);
    expect(screen.getByTestId('NovaDrawerRoot-root')).toHaveClass('NovaDrawerRoot-anchorLeft');
  });

  it('Anchor: top', () => {
    render(<DrawerRoot data-testid="NovaDrawerRoot-root" anchor="top" />);
    expect(screen.getByTestId('NovaDrawerRoot-root')).toHaveClass('NovaDrawerRoot-anchorTop');
  });

  it('Anchor: right', () => {
    render(<DrawerRoot data-testid="NovaDrawerRoot-root" anchor="right" />);
    expect(screen.getByTestId('NovaDrawerRoot-root')).toHaveClass('NovaDrawerRoot-anchorRight');
  });

  it('Anchor: bottom', () => {
    render(<DrawerRoot data-testid="NovaDrawerRoot-root" anchor="bottom" />);
    expect(screen.getByTestId('NovaDrawerRoot-root')).toHaveClass('NovaDrawerRoot-anchorBottom');
  });
});
