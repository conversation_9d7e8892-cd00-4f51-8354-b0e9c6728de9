import { describe, expect, it, beforeEach } from 'vitest';
import { PickerDateType } from '../models/pickers';
import { findClosestEnabledDate, mergeDateAndTime, getMonthsInYear } from './dateUtils';
import { DayjsAdapter } from '../DayjsAdapter';

describe('dateUtils', () => {
  let utils: DayjsAdapter;

  beforeEach(() => {
    utils = new DayjsAdapter();
  });

  describe('findClosestEnabledDate', () => {
    let baseDate: PickerDateType;
    let minDate: PickerDateType;
    let maxDate: PickerDateType;

    beforeEach(() => {
      baseDate = utils.date('2024-01-15');
      minDate = utils.date('2024-01-10');
      maxDate = utils.date('2024-01-20');
    });

    // Helper function to create base params
    const createBaseParams = () => ({
      date: baseDate,
      utils,
      isDateDisabled: () => false,
    });

    describe('basic functionality', () => {
      it('should return the same date if it is enabled', () => {
        const params = {
          ...createBaseParams(),
          isDateDisabled: () => false,
        };

        const result = findClosestEnabledDate(params);
        expect(utils.isSameDay(result!, baseDate)).toBe(true);
      });

      it('should return null when all dates are disabled in range', () => {
        const params = {
          ...createBaseParams(),
          isDateDisabled: () => true, // All dates are disabled
          minDate,
          maxDate,
        };

        const result = findClosestEnabledDate(params);
        expect(result).toBeNull();
      });
    });

    describe('edge cases', () => {
      it('should handle date exactly at minDate boundary when disabled', () => {
        const params = {
          ...createBaseParams(),
          date: minDate,
          minDate,
          maxDate,
          isDateDisabled: (date: PickerDateType) => utils.isSameDay(date, minDate),
        };

        const result = findClosestEnabledDate(params);
        expect(result).not.toBeNull();
        expect(utils.isAfter(result!, minDate)).toBe(true);
      });

      it('should handle date exactly at maxDate boundary when disabled', () => {
        const params = {
          ...createBaseParams(),
          date: maxDate,
          minDate,
          maxDate,
          isDateDisabled: (date: PickerDateType) => utils.isSameDay(date, maxDate),
        };

        const result = findClosestEnabledDate(params);
        expect(result).not.toBeNull();
        expect(utils.isBefore(result!, maxDate)).toBe(true);
      });

      it('should handle case when input date is before minDate', () => {
        const earlyDate = utils.date('2024-01-05');
        const params = {
          ...createBaseParams(),
          date: earlyDate,
          minDate,
          maxDate,
        };

        const result = findClosestEnabledDate(params);
        expect(result).not.toBeNull();
        expect(utils.isSameDay(result!, minDate) || utils.isAfter(result!, minDate)).toBe(true);
      });

      it('should handle case when input date is after maxDate', () => {
        const lateDate = utils.date('2024-01-25');
        const params = {
          ...createBaseParams(),
          date: lateDate,
          minDate,
          maxDate,
        };

        const result = findClosestEnabledDate(params);
        expect(result).not.toBeNull();
        expect(utils.isSameDay(result!, maxDate) || utils.isBefore(result!, maxDate)).toBe(true);
      });

      it('should find closest enabled date in alternating pattern', () => {
        const params = {
          ...createBaseParams(),
          date: utils.date('2024-01-16'), // Day 16 (even, will be disabled)
          minDate,
          maxDate,
          isDateDisabled: (date: PickerDateType) => utils.getDate(date) % 2 === 0, // Disable even dates
        };

        const result = findClosestEnabledDate(params);
        expect(result).not.toBeNull();
        expect(utils.getDate(result!) % 2).toBe(1); // Should find an odd-numbered date
        // Should find either day 15 or 17 (both odd)
        const isDay15 = utils.isSameDay(result!, utils.date('2024-01-15'));
        const isDay17 = utils.isSameDay(result!, utils.date('2024-01-17'));
        expect(isDay15 || isDay17).toBe(true);
      });

      it('should prefer forward direction when both directions have equal distance', () => {
        const targetDate = utils.date('2024-01-16');
        const params = {
          ...createBaseParams(),
          date: targetDate,
          minDate,
          maxDate,
          isDateDisabled: (date: PickerDateType) => utils.isSameDay(date, targetDate), // Only disable the target date
        };

        const result = findClosestEnabledDate(params);
        expect(result).not.toBeNull();
        // The algorithm actually prefers forward direction first
        expect(utils.isSameDay(result!, utils.date('2024-01-17'))).toBe(true);
      });
    });

    describe('disablePast functionality', () => {
      it('should respect disablePast constraint', () => {
        const pastDate = utils.date('2024-01-14');
        const params = {
          ...createBaseParams(),
          date: pastDate,
          disablePast: true,
          isDateDisabled: () => false,
        };

        const result = findClosestEnabledDate(params);
        expect(result).not.toBeNull();
        // Result should be today or later
        const today = utils.date('2024-01-15'); // baseDate represents today
        expect(utils.isSameDay(result!, today) || utils.isAfter(result!, today)).toBe(true);
      });

      it('should return null when only past dates are valid but disablePast is true', () => {
        const params = {
          ...createBaseParams(),
          date: baseDate,
          disablePast: true,
          minDate: utils.date('2024-01-01'),
          maxDate: baseDate,
          isDateDisabled: (date: PickerDateType) => utils.isSameDay(date, baseDate) || utils.isAfter(date, baseDate),
        };

        const result = findClosestEnabledDate(params);
        expect(result).toBeNull();
      });
    });

    describe('disableFuture functionality', () => {
      it('should respect disableFuture constraint', () => {
        const futureDate = utils.date('2024-01-16');
        const today = utils.date('2024-01-15');
        const params = {
          ...createBaseParams(),
          date: futureDate,
          maxDate: today, // Explicitly set maxDate to simulate disableFuture behavior
          isDateDisabled: () => false,
        };

        const result = findClosestEnabledDate(params);

        expect(result).not.toBeNull();
        // Should find the closest enabled date which is today (2024-01-15)
        expect(utils.isSameDay(result!, today)).toBe(true);
      });

      it('should return null when all valid dates are disabled in restricted range', () => {
        const today = utils.date('2024-01-15');
        const params = {
          ...createBaseParams(),
          date: baseDate, // 2024-01-15
          minDate: today,
          maxDate: today, // Only today is in the valid range
          isDateDisabled: (date: PickerDateType) => utils.isSameDay(date, today), // Disable today
        };

        const result = findClosestEnabledDate(params);
        expect(result).toBeNull();
      });
    });

    describe('complex scenarios', () => {
      it('should handle narrow valid range with disabled dates', () => {
        const params = {
          ...createBaseParams(),
          date: utils.date('2024-01-15'),
          minDate: utils.date('2024-01-14'),
          maxDate: utils.date('2024-01-16'),
          isDateDisabled: (date: PickerDateType) => {
            const day = utils.getDate(date);
            return day === 14 || day === 15; // Disable 14th and 15th
          },
        };

        const result = findClosestEnabledDate(params);
        expect(result).not.toBeNull();
        expect(utils.getDate(result!)).toBe(16); // Should find 16th
      });

      it('should handle infinite loop prevention', () => {
        const params = {
          ...createBaseParams(),
          date: baseDate,
          minDate: utils.date('2024-01-14'),
          maxDate: utils.date('2024-01-16'),
          isDateDisabled: () => true, // All dates disabled
        };

        const result = findClosestEnabledDate(params);
        expect(result).toBeNull();
      });
    });
  });

  describe('mergeDateAndTime', () => {
    it('should merge date and time correctly', () => {
      const dateOnly = utils.date('2024-01-15');
      const timeOnly = utils.date('2024-01-01T14:30:45.123');

      const result = mergeDateAndTime(utils, dateOnly, timeOnly);

      expect(utils.getYear(result)).toBe(2024);
      expect(utils.getMonth(result)).toBe(0); // January
      expect(utils.getDate(result)).toBe(15);
      expect(utils.getHours(result)).toBe(14);
      expect(utils.getMinutes(result)).toBe(30);
      expect(utils.getSeconds(result)).toBe(45);
      expect(utils.getMilliseconds(result)).toBe(123);
    });
  });

  describe('getMonthsInYear', () => {
    it('should return 12 months for a given year', () => {
      const year = utils.date('2024-01-01');
      const months = getMonthsInYear(utils, year);

      expect(months).toHaveLength(12);
      expect(utils.getMonth(months[0])).toBe(0); // January
      expect(utils.getMonth(months[11])).toBe(11); // December
    });

    it('should return months starting from beginning of year', () => {
      const midYear = utils.date('2024-06-15');
      const months = getMonthsInYear(utils, midYear);

      expect(months).toHaveLength(12);
      // Should start from January, not June
      expect(utils.getMonth(months[0])).toBe(0);
      expect(utils.getDate(months[0])).toBe(1);
    });
  });
});
