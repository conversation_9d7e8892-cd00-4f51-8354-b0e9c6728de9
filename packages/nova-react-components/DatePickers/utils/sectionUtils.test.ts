import { describe, it, expect, vi } from 'vitest';
import {
  cleanString,
  getTokenConfig,
  findFormatTokens,
  cleanSeparator,
  parseFormatIntoSections,
  expandDateFormat,
  getEscapedPartsFromFormat,
  getSectionTypeFromFormatToken,
  getSectionMaxLength,
  addPositionPropertiesToSections,
  parseDateSections,
  sectionsToDateString,
  cleanDigitValue,
  getInputValueFromSections,
  mergeParsedDateWithReference,
  getSectionsFromFormat,
  formatContainsSection,
  extractSectionsFromValueString,
  createSectionFromToken,
  getSectionFormatPatterns,
  getUtils,
} from './sectionUtils';
import { FieldSectionType } from '../models/dateSection';
import dayjs from 'dayjs';

describe('dateSectionUtils', () => {
  describe('cleanString', () => {
    it('should remove invisible unicode characters', () => {
      const dirtyString = '12\u206634\u2067/\u206810\u2069/2023';
      expect(cleanString(dirtyString)).toBe('1234/10/2023');
    });

    it('should return the same string if no invisible characters', () => {
      expect(cleanString('12/34/5678')).toBe('12/34/5678');
    });

    it('should handle empty string', () => {
      expect(cleanString('')).toBe('');
    });
  });

  describe('getTokenConfig', () => {
    it('should return correct config for year tokens', () => {
      expect(getTokenConfig('YYYY')).toEqual({
        type: 'year',
        contentType: 'digit',
        maxLength: 4,
      });
      expect(getTokenConfig('YY')).toEqual({
        type: 'year',
        contentType: 'digit',
        maxLength: 4,
      });
    });

    it('should return correct config for month tokens', () => {
      expect(getTokenConfig('MMMM')).toEqual({
        type: 'month',
        contentType: 'letter',
      });
      expect(getTokenConfig('MM')).toEqual({
        type: 'month',
        contentType: 'digit',
        maxLength: 2,
      });
      expect(getTokenConfig('M')).toEqual({
        type: 'month',
        contentType: 'digit',
        maxLength: 2,
      });
    });

    it('should return default config for unknown tokens', () => {
      expect(getTokenConfig('XYZ')).toEqual({
        type: 'day',
        contentType: 'digit',
        maxLength: 2,
      });
    });
  });

  describe('findFormatTokens', () => {
    it('should find tokens in format string', () => {
      const format = 'YYYY-MM-DD';
      const tokens = findFormatTokens(format);
      expect(tokens).toEqual([
        { token: 'YYYY', index: 0 },
        { token: 'MM', index: 5 },
        { token: 'DD', index: 8 },
      ]);
    });

    it('should ignore escaped parts', () => {
      const format = "'Escaped' YYYY-MM-DD";
      const tokens = findFormatTokens(format);
      expect(tokens).toEqual([
        { token: 'YYYY', index: 10 },
        { token: 'MM', index: 15 },
        { token: 'DD', index: 18 },
      ]);
    });

    it('should handle empty format', () => {
      expect(findFormatTokens('')).toEqual([]);
    });
  });

  describe('cleanSeparator', () => {
    it('should remove single quotes from separator', () => {
      expect(cleanSeparator("'/ '")).toBe('/ ');
    });

    it('should return same string if no quotes', () => {
      expect(cleanSeparator('/ ')).toBe('/ ');
    });
  });

  describe('parseFormatIntoSections', () => {
    it('should parse simple date format', () => {
      const sections = parseFormatIntoSections('MM/DD/YYYY');
      expect(sections).toEqual([
        {
          token: 'MM',
          type: 'month',
          separator: '',
          index: 0,
        },
        {
          token: 'DD',
          type: 'day',
          separator: '/',
          index: 3,
        },
        {
          token: 'YYYY',
          type: 'year',
          separator: '/',
          index: 6,
        },
      ]);
    });

    it('should handle leading separator', () => {
      const sections = parseFormatIntoSections(' Date: MM/DD/YYYY');
      expect(sections[0].separator).toBe(' ');
    });

    it('should handle trailing separator', () => {
      const sections = parseFormatIntoSections('MM/DD/YYYY UTC');
      expect(sections[2].trailingSeparator).toBe(' UTC');
    });
  });

  describe('expandDateFormat', () => {
    it('should expand format using utils', () => {
      const utils = {
        expandFormat: (format: string) => format.replace('M', 'MM'),
      };
      expect(expandDateFormat(utils, 'M/DD/YYYY')).toBe('MMMMMMMMMMMM/DD/YYYY');
    });

    it('should prevent infinite loops', () => {
      const utils = {
        expandFormat: (format: string) => format + 'M',
      };
      const consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      expect(expandDateFormat(utils, 'MM/DD')).toMatch(/MM\/DDM+/);
      expect(consoleWarnSpy).toHaveBeenCalled();
      consoleWarnSpy.mockRestore();
    });
  });

  describe('getEscapedPartsFromFormat', () => {
    it('should find escaped parts in format', () => {
      const utils = {
        escapedCharacters: { start: "'", end: "'" },
      };
      const escapedParts = getEscapedPartsFromFormat(utils, "'Escaped' YYYY-MM-DD");
      expect(escapedParts).toEqual([{ start: 0, end: 8 }]);
    });

    it('should return empty array if no escaped parts', () => {
      const utils = {
        escapedCharacters: { start: "'", end: "'" },
      };
      expect(getEscapedPartsFromFormat(utils, 'YYYY-MM-DD')).toEqual([]);
    });
  });

  describe('getSectionTypeFromFormatToken', () => {
    it('should identify year tokens', () => {
      expect(getSectionTypeFromFormatToken('YYYY')).toEqual({
        type: 'year',
        contentType: 'digit',
      });
    });

    it('should identify month tokens', () => {
      expect(getSectionTypeFromFormatToken('MMMM')).toEqual({
        type: 'month',
        contentType: 'letter',
      });
      expect(getSectionTypeFromFormatToken('MM')).toEqual({
        type: 'month',
        contentType: 'digit',
      });
    });

    it('should identify day tokens', () => {
      expect(getSectionTypeFromFormatToken('DD')).toEqual({
        type: 'day',
        contentType: 'digit',
      });
    });
  });

  describe('getSectionMaxLength', () => {
    it('should return correct max length for year', () => {
      expect(getSectionMaxLength('year', 'YYYY')).toBe(4);
      expect(getSectionMaxLength('year', 'YY')).toBe(2);
    });

    it('should return correct max length for month', () => {
      expect(getSectionMaxLength('month', 'MMMM')).toBe(9);
      expect(getSectionMaxLength('month', 'MM')).toBe(2);
    });

    it('should return correct max length for day', () => {
      expect(getSectionMaxLength('day', 'DD')).toBe(2);
    });

    it('should return correct max length for weekDay', () => {
      expect(getSectionMaxLength('weekDay', 'DD')).toBe(10);
    });

    it('should return correct max length for hours', () => {
      expect(getSectionMaxLength('hours', 'hh')).toBe(2);
    });

    it('should return correct max length for minutes', () => {
      expect(getSectionMaxLength('minutes', 'mm')).toBe(2);
    });

    it('should return correct max length for seconds', () => {
      expect(getSectionMaxLength('seconds', 'ss')).toBe(2);
    });

    it('should return correct max length for meridiem', () => {
      expect(getSectionMaxLength('meridiem', 'ms')).toBe(2);
    });

    it('should return default max length for unknown types', () => {
      expect(getSectionMaxLength('unknown' as any, 'XX')).toBe(2);
    });
  });

  describe('addPositionPropertiesToSections', () => {
    it('should add position properties to sections', () => {
      const sections = [
        {
          type: 'month' as FieldSectionType,
          contentType: 'digit' as const,
          value: '12',
          displayValue: '12',
          placeholder: 'MM',
          maxLength: 2,
          separator: '/',
          token: 'MM',
          startIndex: 0,
          endIndex: 1,
          invalid: false,
          modified: true,
        },
        {
          type: 'day' as FieldSectionType,
          contentType: 'digit' as const,
          value: '31',
          displayValue: '31',
          placeholder: 'DD',
          maxLength: 2,
          separator: '/',
          token: 'DD',
          startIndex: 2,
          endIndex: 3,
          invalid: false,
          modified: true,
        },
      ];

      const positioned = addPositionPropertiesToSections(sections);
      expect(positioned[0]).toMatchObject({
        startInInput: 0,
        endInInput: 1,
        separatorStart: 2,
        separatorEnd: 2,
      });
      expect(positioned[1]).toMatchObject({
        startInInput: 3,
        endInInput: 4,
      });
    });

    it('should handle RTL formatting', () => {
      const sections = [
        {
          type: 'month' as FieldSectionType,
          contentType: 'digit' as const,
          value: '12',
          displayValue: '12',
          placeholder: 'MM',
          maxLength: 2,
          separator: '/',
          token: 'MM',
          startIndex: 0,
          endIndex: 1,
          invalid: false,
          modified: true,
        },
      ];

      const positioned = addPositionPropertiesToSections(sections, { isRtl: true });
      expect(positioned[0].separator).toMatch(/[\u2066-\u2069]/);
    });
  });

  describe('parseDateSections', () => {
    it('should parse valid date string into sections', () => {
      const sections = parseDateSections('MM/DD/YYYY', '12/31/2023');
      expect(sections).toEqual([
        expect.objectContaining({ type: 'month', value: '12' }),
        expect.objectContaining({ type: 'day', value: '31' }),
        expect.objectContaining({ type: 'year', value: '2023' }),
      ]);
    });

    it('should handle empty value', () => {
      const sections = parseDateSections('MM/DD/YYYY', '');
      expect(sections[0].value).toBe('');
    });

    it('should handle invalid date string', () => {
      const sections = parseDateSections('MM/DD/YYYY', 'invalid');
      expect(sections[0].value).toBe('');
    });
  });

  describe('sectionsToDateString', () => {
    it('should convert sections back to date string', () => {
      const sections = [
        {
          type: 'month' as FieldSectionType,
          contentType: 'digit' as const,
          value: '12',
          displayValue: '12',
          placeholder: 'MM',
          maxLength: 2,
          separator: '/',
          token: 'MM',
          startIndex: 0,
          endIndex: 1,
          invalid: false,
          modified: true,
        },
        {
          type: 'day' as FieldSectionType,
          contentType: 'digit' as const,
          value: '31',
          displayValue: '31',
          placeholder: 'DD',
          maxLength: 2,
          separator: '',
          token: 'DD',
          startIndex: 2,
          endIndex: 3,
          invalid: false,
          modified: true,
        },
      ];

      expect(sectionsToDateString(sections, 'MM/DD')).toBe('12/31');
    });

    it('should handle empty sections', () => {
      expect(sectionsToDateString([], 'MM/DD')).toBe('');
    });
  });

  describe('cleanDigitValue', () => {
    it('should clean day value', () => {
      expect(cleanDigitValue('day', '5')).toBe('05');
      expect(cleanDigitValue('day', '32')).toBe('31');
      expect(cleanDigitValue('day', '0')).toBe('01');
    });

    it('should clean month value', () => {
      expect(cleanDigitValue('month', '2')).toBe('02');
      expect(cleanDigitValue('month', '13')).toBe('12');
      expect(cleanDigitValue('month', '0')).toBe('01');
    });

    it('should clean year value', () => {
      expect(cleanDigitValue('year', '2023')).toBe('2023');
      expect(cleanDigitValue('year', '23')).toBe('23');
    });

    it('should return empty string for invalid input', () => {
      expect(cleanDigitValue('day', 'abc')).toBe('');
    });
  });

  describe('getInputValueFromSections', () => {
    it('should build input value from sections', () => {
      const sections: any = [
        {
          type: 'month' as FieldSectionType,
          value: '12',
          placeholder: 'MM',
          startInInput: 0,
          endInInput: 1,
          startIndex: 0,
          endIndex: 1,
          separator: '/',
          displayValue: '12',
          maxLength: 2,
          token: 'MM',
          invalid: false,
          modified: true,
          contentType: 'digit' as const,
        },
        {
          type: 'day' as FieldSectionType,
          value: '31',
          placeholder: 'DD',
          startInInput: 3,
          endInInput: 4,
          startIndex: 2,
          endIndex: 3,
          separator: '',
          displayValue: '31',
          maxLength: 2,
          token: 'DD',
          invalid: false,
          modified: true,
          contentType: 'digit' as const,
        },
      ];

      expect(getInputValueFromSections(sections)).toBe('⁦12/31⁩');
    });

    it('should handle RTL characters', () => {
      const sections = [
        {
          type: 'month' as FieldSectionType,
          value: '12',
          placeholder: 'MM',
          startInInput: 0,
          endInInput: 1,
          startIndex: 0,
          endIndex: 1,
          separator: '',
          displayValue: '12',
          maxLength: 2,
          token: 'MM',
          invalid: false,
          modified: true,
          contentType: 'digit' as const,
        },
      ];

      expect(getInputValueFromSections(sections, { includeRTLCharacters: true })).toMatch(/[\u2066-\u2069]/);
    });
  });

  describe('mergeParsedDateWithReference', () => {
    const utils = {
      date: (v?: any) => dayjs(v),
      isValid: (d: any) => dayjs(d).isValid(),
      getYear: (d: any) => dayjs(d).year(),
      getMonth: (d: any) => dayjs(d).month(),
      getDate: (d: any) => dayjs(d).date(),
      getHours: (d: any) => dayjs(d).hour(),
      getMinutes: (d: any) => dayjs(d).minute(),
      getSeconds: (d: any) => dayjs(d).second(),
      setYear: (d: any, y: number) => dayjs(d).year(y),
      setMonth: (d: any, m: number) => dayjs(d).month(m),
      setDate: (d: any, day: number) => dayjs(d).date(day),
      setHours: (d: any, h: number) => dayjs(d).hour(h),
      setMinutes: (d: any, m: number) => dayjs(d).minute(m),
      setSeconds: (d: any, s: number) => dayjs(d).second(s),
    };

    it('should merge year from parsed date', () => {
      const parsed = utils.date('2023-01-01');
      const reference = utils.date('2000-01-01');
      const merged = mergeParsedDateWithReference(utils, parsed, reference, ['year']);
      expect(utils.getYear(merged)).toBe(2023);
    });

    it('should return reference date if parsed is invalid', () => {
      const reference = utils.date('2000-01-01');
      const merged = mergeParsedDateWithReference(utils, null, reference, ['year']);
      expect(merged).toBe(reference);
    });
  });

  describe('getSectionsFromFormat', () => {
    it('should identify sections in format string', () => {
      expect(getSectionsFromFormat('YYYY-MM-DD')).toEqual(['year', 'month', 'day']);
      expect(getSectionsFromFormat('HH:mm A')).toEqual(['hours', 'minutes', 'meridiem']);
    });
  });

  describe('formatContainsSection', () => {
    it('should detect sections in format', () => {
      expect(formatContainsSection('YYYY-MM-DD', 'year')).toBe(true);
      expect(formatContainsSection('HH:mm', 'year')).toBe(false);
    });
  });

  describe('extractSectionsFromValueString', () => {
    const utils = {
      parse: (v: string, f: string) => dayjs(v, f),
      isValid: (d: any) => dayjs(d).isValid(),
      getYear: (d: any) => dayjs(d).year(),
      getMonth: (d: any) => dayjs(d).month(),
      getDate: (d: any) => dayjs(d).date(),
      getHours: (d: any) => dayjs(d).hour(),
      getMinutes: (d: any) => dayjs(d).minute(),
      getSeconds: (d: any) => dayjs(d).second(),
    };

    it('should extract sections from valid date string', () => {
      const result = extractSectionsFromValueString('2023-12-31', 'YYYY-MM-DD', utils);
      expect(result.year).toBe('2023');
      expect(result.month).toBe('12');
      expect(result.day).toBe('31');
    });

    it('should handle invalid date string', () => {
      const result = extractSectionsFromValueString('invalid', 'YYYY-MM-DD', utils);
      expect(result.year).toBeNull();
    });
  });

  describe('createSectionFromToken', () => {
    const utils = {
      date: (v?: any) => dayjs(v),
    };

    it('should create month section', () => {
      const section = createSectionFromToken(utils, 'MM', '12');
      expect(section).toEqual({
        type: 'month',
        contentType: 'digit',
        value: '12',
        displayValue: '12',
        placeholder: 'MM',
        maxLength: 2,
        separator: '',
        token: 'MM',
        invalid: false,
        modified: true,
        startIndex: 0,
        startSeparator: '',
        endIndex: 0,
      });
    });

    it('should create invalid section for invalid value', () => {
      const section = createSectionFromToken(utils, 'MM', '13');
      expect(section.invalid).toBe(true);
    });

    it('should create year section with 4-digit format', () => {
      const section = createSectionFromToken(utils, 'YYYY', '2024', 'prefix_', 1);
      expect(section.type).toBe('year');
      expect(section.maxLength).toBe(4);
      expect(section.placeholder).toBe('YYYY');
      expect(section.startSeparator).toBe('prefix_');
      expect(section.startIndex).toBe(1);
    });

    it('should create year section with 2-digit format', () => {
      const section = createSectionFromToken(utils, 'YY', '24');
      expect(section.type).toBe('year');
      expect(section.maxLength).toBe(2);
      expect(section.placeholder).toBe('YY');
    });

    it('should create month text section', () => {
      const section = createSectionFromToken(utils, 'MMMM', 'January');
      expect(section.type).toBe('month');
      expect(section.contentType).toBe('letter');
      expect(section.maxLength).toBe(9);
    });

    it('should create day section', () => {
      const section = createSectionFromToken(utils, 'DD', '15');
      expect(section.type).toBe('day');
      expect(section.placeholder).toBe('DD');
      expect(section.maxLength).toBe(2);
    });

    it('should create hours section', () => {
      const section = createSectionFromToken(utils, 'HH', '14');
      expect(section.type).toBe('hours');
      expect(section.placeholder).toBe('HH');
      expect(section.maxLength).toBe(2);
    });

    it('should create minutes section', () => {
      const section = createSectionFromToken(utils, 'mm', '30');
      expect(section.type).toBe('minutes');
      expect(section.placeholder).toBe('MM');
      expect(section.maxLength).toBe(2);
    });

    it('should create seconds section', () => {
      const section = createSectionFromToken(utils, 'ss', '45');
      expect(section.type).toBe('seconds');
      expect(section.placeholder).toBe('SS');
      expect(section.maxLength).toBe(2);
    });

    it('should create meridiem section', () => {
      const section = createSectionFromToken(utils, 'A', 'PM');
      expect(section.type).toBe('meridiem');
      expect(section.placeholder).toBe('AM/PM');
      expect(section.maxLength).toBe(2);
    });

    it('should create weekDay section', () => {
      const section = createSectionFromToken(utils, 'dddd', 'Monday');
      expect(section.type).toBe('weekDay');
      expect(section.placeholder).toBe('Day');
      expect(section.maxLength).toBe(10);
    });

    it('should validate invalid month value', () => {
      const section = createSectionFromToken(utils, 'MM', '13');
      expect(section.invalid).toBe(true);
    });

    it('should validate invalid day value', () => {
      const section = createSectionFromToken(utils, 'DD', '32');
      expect(section.invalid).toBe(true);
    });

    it('should validate invalid year value', () => {
      const section = createSectionFromToken(utils, 'YYYY', '-1');
      expect(section.invalid).toBe(true);
    });

    it('should validate invalid hours value', () => {
      const section = createSectionFromToken(utils, 'HH', '25');
      expect(section.invalid).toBe(true);
    });

    it('should validate invalid minutes value', () => {
      const section = createSectionFromToken(utils, 'mm', '60');
      expect(section.invalid).toBe(true);
    });

    it('should validate invalid seconds value', () => {
      const section = createSectionFromToken(utils, 'ss', '61');
      expect(section.invalid).toBe(true);
    });

    it('should validate invalid meridiem value', () => {
      const section = createSectionFromToken(utils, 'A', 'XX');
      expect(section.invalid).toBe(true);
    });

    it('should handle empty value', () => {
      const section = createSectionFromToken(utils, 'MM', '');
      expect(section.value).toBe('');
      expect(section.invalid).toBe(false);
      expect(section.modified).toBe(false);
    });
  });

  describe('getSectionFormatPatterns', () => {
    it('should return correct patterns for all section types', () => {
      const patterns = getSectionFormatPatterns();
      expect(patterns.year).toContain('YYYY');
      expect(patterns.month).toContain('MMMM');
      expect(patterns.day).toContain('DD');
      expect(patterns.weekDay).toContain('dddd');
      expect(patterns.hours).toContain('HH');
      expect(patterns.minutes).toContain('mm');
      expect(patterns.seconds).toContain('ss');
      expect(patterns.meridiem).toContain('A');
      expect(patterns.empty).toEqual([]);
    });
  });

  describe('getUtils', () => {
    it('should return default utils object', () => {
      const utils = getUtils();
      expect(utils.date).toBeDefined();
      expect(utils.parse).toBeDefined();
      expect(utils.isValid).toBeDefined();
      expect(utils.formatByString).toBeDefined();
    });

    it('should handle date creation', () => {
      const utils = getUtils();
      const date = utils.date('2024-01-15');
      expect(utils.isValid(date)).toBe(true);
    });

    it('should handle parsing with try-catch', () => {
      const utils = getUtils();
      const result = utils.parse('invalid', 'YYYY-MM-DD');
      expect(utils.isValid(result)).toBe(false);
    });

    it('should handle date manipulation', () => {
      const utils = getUtils();
      const date = utils.date('2024-01-15');
      const newDate = utils.setYear(date, 2025);
      expect(utils.getYear(newDate)).toBe(2025);
    });

    it('should handle all utility functions', () => {
      const utils = getUtils();
      const date = utils.date('2024-01-15T14:30:45');

      // Test getters
      expect(utils.getYear(date)).toBe(2024);
      expect(utils.getMonth(date)).toBe(0); // 0-based month
      expect(utils.getDate(date)).toBe(15);
      expect(utils.getHours(date)).toBe(14);
      expect(utils.getMinutes(date)).toBe(30);
      expect(utils.getSeconds(date)).toBe(45);

      // Test setters
      const modifiedDate = utils.setMonth(date, 5);
      expect(utils.getMonth(modifiedDate)).toBe(5);

      // Test add functions
      const futureDate = utils.addYears(date, 1);
      expect(utils.getYear(futureDate)).toBe(2025);

      const futureMonth = utils.addMonths(date, 1);
      expect(utils.getMonth(futureMonth)).toBe(1);

      const futureDay = utils.addDays(date, 1);
      expect(utils.getDate(futureDay)).toBe(16);

      // Test start/end of day
      const startOfDay = utils.startOfDay(date);
      expect(utils.getHours(startOfDay)).toBe(0);

      const endOfDay = utils.endOfDay(date);
      expect(utils.getHours(endOfDay)).toBe(23);

      // Test format
      const formatted = utils.formatByString(date, 'YYYY-MM-DD');
      expect(formatted).toBe('2024-01-15');

      // Test expandFormat (pass-through)
      expect(utils.expandFormat('MM/DD/YYYY')).toBe('MM/DD/YYYY');
    });
  });
});
