import { styled } from '@pigment-css/react';
import { DayCalendar } from '../DayCalendar/DayCalendar';
import { dateRangeCalendarClasses } from './DateRangeCalendar.classes';
import { dateRangePickerDayClasses } from '../DateRangePickerDay';

// Main root component with animation support
export const CalendarRoot = styled('div')<{
  variant: 'modal' | 'docked';
  animating?: boolean;
  direction?: 'up' | 'down';
}>({
  display: 'flex',
  flexDirection: 'row',
  overflow: 'hidden',
  position: 'relative',
  transition: 'transform 400ms cubic-bezier(0.2, 0.8, 0.2, 1)' /* iOS-like spring curve */,
  /* Improve touch scrolling on iOS */
  WebkitOverflowScrolling: 'touch',
  /* Optimize animation performance */
  willChange: 'transform',
  variants: [
    {
      props: { variant: 'modal' },
      style: {
        flexDirection: 'column',
      },
    },
  ],
});

export const MonthsContainer = styled('div')<{ variant: 'modal' | 'docked' }>({
  display: 'flex',
  flexDirection: 'column',
});

// Create a styled variant of DayCalendar for range selection
export const DayCalendarForRange = styled(DayCalendar)(({ theme }) => ({
  [`&.${dateRangeCalendarClasses.dayDragging}`]: {
    [`& .${dateRangePickerDayClasses.day}`]: {
      cursor: 'grabbing',
    },
    [`& .${dateRangePickerDayClasses.root}:not(.${dateRangePickerDayClasses.rangeIntervalDayHighlightStart}):not(.${dateRangePickerDayClasses.rangeIntervalDayHighlightEnd}) .${dateRangePickerDayClasses.day}:not(.${dateRangePickerDayClasses.notSelectedDate})`]:
      {
        opacity: 0.6,
      },
  },
}));
