/**
 * @vitest-environment jsdom
 */
import { renderHook, act } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { useDateFieldState, fieldValueManager, DateFieldStateProps } from './useDateFieldState';
import { FieldSectionType } from '../../models/dateSection';
import { DayjsAdapter } from '../../DayjsAdapter';
import { PickerDateType } from '../../models/pickers';

// Mock dependencies
const realUtils = new DayjsAdapter();

const mockUtils = {
  date: vi.fn((value?: any) => {
    if (value === undefined) return realUtils.date('2024-01-15'); // Current date for tests
    if (typeof value === 'string') {
      return realUtils.date(value);
    }
    return realUtils.date(value);
  }),
  parse: vi.fn(),
  isValid: vi.fn(),
  isBefore: vi.fn(),
  isAfter: vi.fn(),
  format: vi.fn(),
  formatByString: vi.fn((date: PickerDateType, format: string) => realUtils.formatByString(date, format)),
  startOfDay: vi.fn(),
  endOfDay: vi.fn(),
  getYear: vi.fn(),
  getMonth: vi.fn(),
  getDate: vi.fn(),
  getHours: vi.fn(),
  getMinutes: vi.fn(),
  getSeconds: vi.fn(),
  setYear: vi.fn(),
  setMonth: vi.fn(),
  setDate: vi.fn(),
  setHours: vi.fn(),
  setMinutes: vi.fn(),
  setSeconds: vi.fn(),
} as any;

vi.mock('../useUtils', () => ({
  useUtils: () => mockUtils,
}));

vi.mock('./useSectionBoundaries', () => ({
  useSectionBoundaries: () => ({
    getSectionBoundaries: vi.fn((type: FieldSectionType) => {
      const boundaries: Record<string, { minimum: number; maximum: number }> = {
        year: { minimum: 1900, maximum: 2100 },
        month: { minimum: 1, maximum: 12 },
        day: { minimum: 1, maximum: 31 },
        hours: { minimum: 0, maximum: 23 },
        minutes: { minimum: 0, maximum: 59 },
        seconds: { minimum: 0, maximum: 59 },
        meridiem: { minimum: 0, maximum: 1 },
        weekDay: { minimum: 0, maximum: 6 },
      };
      return boundaries[type] || { minimum: 0, maximum: 9999 };
    }),
  }),
}));

// Mock section utils
vi.mock('../../utils/sectionUtils', () => ({
  parseDateSections: vi.fn((format: string, value: string) => [
    { type: 'month', value: '01', modified: false },
    { type: 'day', value: '15', modified: false },
    { type: 'year', value: '2024', modified: false },
  ]),
  parseFormatIntoSections: vi.fn((format: string) => [
    { type: 'month', format: 'MM' },
    { type: 'day', format: 'DD' },
    { type: 'year', format: 'YYYY' },
  ]),
  mergeParsedDateWithReference: vi.fn((utils, parsedDate, referenceDate, formatSectionTypes) => parsedDate),
  getSectionsFromFormat: vi.fn((format: string) => ['month', 'day', 'year']),
  extractSectionsFromValueString: vi.fn((value: string, format: string, utils: any) => ({
    year: '2024',
    month: '01',
    day: '15',
    hours: '12',
    minutes: '30',
    seconds: '45',
    meridiem: 'PM',
  })),
}));

describe('useDateFieldState', () => {
  let defaultProps: DateFieldStateProps;
  let utils: DayjsAdapter;

  beforeEach(() => {
    vi.clearAllMocks();
    utils = new DayjsAdapter();
    defaultProps = {
      format: 'MM/DD/YYYY',
      onChange: vi.fn(),
    };
  });

  describe('initialization', () => {
    it('should initialize with default values', () => {
      const { result } = renderHook(() => useDateFieldState(defaultProps));

      expect(result.current.date).toBeNull();
      expect(result.current.inputValue).toBe('');
      expect(result.current.error).toBeNull();
      expect(result.current.isValid).toBe(true);
      expect(result.current.characterQuery).toBeNull();
    });

    it('should initialize with defaultValue', () => {
      const defaultValue = utils.date('2024-01-15');
      const props = { ...defaultProps, defaultValue };

      const { result } = renderHook(() => useDateFieldState(props));

      expect(result.current.date).toBe(defaultValue);
      expect(mockUtils.formatByString).toHaveBeenCalledWith(defaultValue, 'MM/DD/YYYY');
    });

    it('should initialize with controlled value', () => {
      const value = utils.date('2024-01-16');
      const props = { ...defaultProps, value };

      const { result } = renderHook(() => useDateFieldState(props));

      expect(result.current.date).toBe(value);
    });
  });

  describe('controlled value updates', () => {
    it('should update when controlled value changes', () => {
      const initialValue = utils.date('2024-01-15');
      const { result, rerender } = renderHook((props) => useDateFieldState(props), {
        initialProps: { ...defaultProps, value: initialValue },
      });

      expect(result.current.date).toBe(initialValue);

      const newValue = utils.date('2024-01-16');
      rerender({ ...defaultProps, value: newValue });

      expect(result.current.date).toBe(newValue);
    });

    it('should update reference date when valid controlled value changes', () => {
      const initialValue = utils.date('2024-01-15');
      mockUtils.isValid.mockReturnValue(true);

      const { rerender } = renderHook((props) => useDateFieldState(props), {
        initialProps: { ...defaultProps, value: initialValue },
      });

      const newValue = utils.date('2024-01-16');
      rerender({ ...defaultProps, value: newValue });

      expect(mockUtils.isValid).toHaveBeenCalledWith(newValue);
    });
  });

  describe('validation', () => {
    it('should validate minDate constraint', () => {
      const minDate = utils.date('2024-01-10');
      const props = { ...defaultProps, minDate };
      const testDate = utils.date('2024-01-05');

      mockUtils.parse.mockReturnValue(testDate);
      mockUtils.isValid.mockReturnValue(true);
      mockUtils.isBefore.mockReturnValue(true);

      const { result } = renderHook(() => useDateFieldState(props));

      act(() => {
        result.current.updateValueFromValueStr('01/05/2024');
      });

      expect(result.current.error).toBe('Date is before minimum date');
      expect(result.current.isValid).toBe(false);
    });

    it('should validate maxDate constraint', () => {
      const maxDate = utils.date('2024-01-20');
      const props = { ...defaultProps, maxDate };
      const testDate = utils.date('2024-01-25');

      mockUtils.parse.mockReturnValue(testDate);
      mockUtils.isValid.mockReturnValue(true);
      mockUtils.isAfter.mockReturnValue(true);

      const { result } = renderHook(() => useDateFieldState(props));

      act(() => {
        result.current.updateValueFromValueStr('01/25/2024');
      });

      expect(result.current.error).toBe('Date is after maximum date');
      expect(result.current.isValid).toBe(false);
    });

    it('should validate custom shouldDisableDate function', () => {
      const shouldDisableDate = vi.fn(() => true);
      const props = { ...defaultProps, shouldDisableDate };
      const testDate = utils.date('2024-01-15');

      mockUtils.parse.mockReturnValue(testDate);
      mockUtils.isValid.mockReturnValue(true);
      mockUtils.isBefore.mockReturnValue(false);
      mockUtils.isAfter.mockReturnValue(false);

      const { result } = renderHook(() => useDateFieldState(props));

      act(() => {
        result.current.updateValueFromValueStr('01/15/2024');
      });

      expect(result.current.error).toBe('Date is disabled');
      expect(result.current.isValid).toBe(false);
    });

    it('should validate disableFuture constraint', () => {
      const props = { ...defaultProps, disableFuture: true };
      const testDate = utils.date('2024-01-16');

      mockUtils.parse.mockReturnValue(testDate);
      mockUtils.isValid.mockReturnValue(true);
      mockUtils.isBefore.mockReturnValue(false);
      mockUtils.isAfter.mockReturnValue(true);

      const { result } = renderHook(() => useDateFieldState(props));

      act(() => {
        result.current.updateValueFromValueStr('01/16/2024');
      });

      expect(result.current.error).toBe('Date is in the future');
      expect(result.current.isValid).toBe(false);
    });

    it('should validate disablePast constraint', () => {
      const props = { ...defaultProps, disablePast: true };
      const testDate = utils.date('2024-01-14');

      mockUtils.parse.mockReturnValue(testDate);
      mockUtils.isValid.mockReturnValue(true);
      mockUtils.isBefore.mockReturnValue(true);
      mockUtils.isAfter.mockReturnValue(false);

      const { result } = renderHook(() => useDateFieldState(props));

      act(() => {
        result.current.updateValueFromValueStr('01/14/2024');
      });

      expect(result.current.error).toBe('Date is in the past');
      expect(result.current.isValid).toBe(false);
    });

    it('should clear error when date is valid', () => {
      const testDate = utils.date('2024-01-15');

      mockUtils.parse.mockReturnValue(testDate);
      mockUtils.isValid.mockReturnValue(true);
      mockUtils.isBefore.mockReturnValue(false);
      mockUtils.isAfter.mockReturnValue(false);

      const { result } = renderHook(() => useDateFieldState(defaultProps));

      act(() => {
        result.current.updateValueFromValueStr('01/15/2024');
      });

      expect(result.current.error).toBeNull();
      expect(result.current.isValid).toBe(true);
    });

    it('should allow null dates in validation', () => {
      const { result } = renderHook(() => useDateFieldState(defaultProps));

      act(() => {
        result.current.updateValueFromValueStr('');
      });

      expect(result.current.error).toBeNull();
      expect(result.current.isValid).toBe(true);
    });
  });

  describe('date parsing', () => {
    it('should parse date with primary format', () => {
      const parsedDate = utils.date('2024-01-15');
      mockUtils.parse.mockReturnValue(parsedDate);
      mockUtils.isValid.mockReturnValue(true);

      const { result } = renderHook(() => useDateFieldState(defaultProps));

      act(() => {
        result.current.updateValueFromValueStr('01/15/2024');
      });

      expect(mockUtils.parse).toHaveBeenCalledWith('01/15/2024', 'MM/DD/YYYY');
      expect(result.current.date).toEqual(parsedDate);
    });

    it('should try fallback formats when primary format fails', () => {
      const parsedDate = utils.date('2024-01-15');

      // Make parse fail multiple times, then succeed
      mockUtils.parse.mockImplementation(() => {
        const calls = mockUtils.parse.mock.calls.length;
        if (calls <= 8) {
          return null; // Fail for first 8 attempts
        }
        return parsedDate; // Succeed on 9th attempt
      });

      mockUtils.isValid.mockImplementation((date: any) => {
        return date !== null; // Valid if not null
      });

      const { result } = renderHook(() => useDateFieldState(defaultProps));

      act(() => {
        result.current.updateValueFromValueStr('2024-01-15');
      });

      // Check that multiple parse attempts were made (should be more than 1)
      expect(mockUtils.parse.mock.calls.length).toBeGreaterThan(1);
      expect(result.current.date).toEqual(parsedDate);
    });

    it('should handle empty input', () => {
      const { result } = renderHook(() => useDateFieldState(defaultProps));

      act(() => {
        result.current.updateValueFromValueStr('');
      });

      expect(result.current.date).toBeNull();
      expect(result.current.inputValue).toBe('');
    });

    it('should handle whitespace-only input', () => {
      const { result } = renderHook(() => useDateFieldState(defaultProps));

      act(() => {
        result.current.updateValueFromValueStr('   ');
      });

      expect(result.current.date).toBeNull();
      expect(result.current.inputValue).toBe('');
    });

    it('should keep input value even when parsing fails', () => {
      mockUtils.parse.mockReturnValue(null);
      mockUtils.isValid.mockReturnValue(false);

      const { result } = renderHook(() => useDateFieldState(defaultProps));

      act(() => {
        result.current.updateValueFromValueStr('invalid-date');
      });

      expect(result.current.inputValue).toBe('invalid-date');
      expect(result.current.date).toBeNull();
    });
  });

  describe('partial input parsing', () => {
    it('should parse meridiem input (AM)', () => {
      const { result } = renderHook(() => useDateFieldState(defaultProps));

      act(() => {
        result.current.setActiveSection('meridiem');
      });

      act(() => {
        result.current.updateValueFromValueStr('a');
      });

      expect(mockUtils.setHours).toHaveBeenCalled();
    });

    it('should parse meridiem input (PM)', () => {
      const { result } = renderHook(() => useDateFieldState(defaultProps));

      act(() => {
        result.current.setActiveSection('meridiem');
      });

      act(() => {
        result.current.updateValueFromValueStr('p');
      });

      expect(mockUtils.setHours).toHaveBeenCalled();
    });

    it('should handle invalid meridiem input', () => {
      const { result } = renderHook(() => useDateFieldState(defaultProps));

      act(() => {
        result.current.setActiveSection('meridiem');
      });

      act(() => {
        result.current.updateValueFromValueStr('x');
      });

      // Should not crash and should handle gracefully
      expect(result.current.date).toBeNull();
    });

    it('should parse year input', () => {
      const { result } = renderHook(() => useDateFieldState(defaultProps));

      act(() => {
        result.current.setActiveSection('year');
      });

      act(() => {
        result.current.updateValueFromValueStr('2024');
      });

      expect(mockUtils.setYear).toHaveBeenCalled();
    });

    it('should validate year boundaries', () => {
      const { result } = renderHook(() => useDateFieldState(defaultProps));

      act(() => {
        result.current.setActiveSection('year');
      });

      act(() => {
        result.current.updateValueFromValueStr('1800'); // Below minimum
      });

      expect(result.current.date).toBeNull();
    });

    it('should parse month input', () => {
      const { result } = renderHook(() => useDateFieldState(defaultProps));

      act(() => {
        result.current.setActiveSection('month');
      });

      act(() => {
        result.current.updateValueFromValueStr('12');
      });

      expect(mockUtils.setMonth).toHaveBeenCalledWith(expect.any(Object), 11); // 0-indexed
    });

    it('should validate month boundaries', () => {
      const { result } = renderHook(() => useDateFieldState(defaultProps));

      act(() => {
        result.current.setActiveSection('month');
      });

      act(() => {
        result.current.updateValueFromValueStr('13'); // Invalid month
      });

      expect(result.current.date).toBeNull();
    });

    it('should parse day input', () => {
      const { result } = renderHook(() => useDateFieldState(defaultProps));

      act(() => {
        result.current.setActiveSection('day');
      });

      act(() => {
        result.current.updateValueFromValueStr('15');
      });

      expect(mockUtils.setDate).toHaveBeenCalledWith(expect.any(Object), 15);
    });

    it('should parse hours input', () => {
      const { result } = renderHook(() => useDateFieldState(defaultProps));

      act(() => {
        result.current.setActiveSection('hours');
      });

      act(() => {
        result.current.updateValueFromValueStr('14');
      });

      expect(mockUtils.setHours).toHaveBeenCalledWith(expect.any(Object), 14);
    });

    it('should parse minutes input', () => {
      const { result } = renderHook(() => useDateFieldState(defaultProps));

      act(() => {
        result.current.setActiveSection('minutes');
      });

      act(() => {
        result.current.updateValueFromValueStr('30');
      });

      expect(mockUtils.setMinutes).toHaveBeenCalledWith(expect.any(Object), 30);
    });

    it('should parse seconds input', () => {
      const { result } = renderHook(() => useDateFieldState(defaultProps));

      act(() => {
        result.current.setActiveSection('seconds');
      });

      act(() => {
        result.current.updateValueFromValueStr('45');
      });

      expect(mockUtils.setSeconds).toHaveBeenCalledWith(expect.any(Object), 45);
    });

    it('should handle non-numeric input for numeric sections', () => {
      const { result } = renderHook(() => useDateFieldState(defaultProps));

      act(() => {
        result.current.setActiveSection('day');
      });

      act(() => {
        result.current.updateValueFromValueStr('abc');
      });

      expect(result.current.date).toBeNull();
    });

    it('should handle unsupported section types', () => {
      const { result } = renderHook(() => useDateFieldState(defaultProps));

      act(() => {
        result.current.setActiveSection('weekDay');
      });

      act(() => {
        result.current.updateValueFromValueStr('1');
      });

      expect(result.current.date).toBeNull();
    });
  });

  describe('handleInputChange', () => {
    it('should handle input change with new value', () => {
      const { result } = renderHook(() => useDateFieldState(defaultProps));

      const mockEvent = {
        target: { value: '01/15/2024' },
        nativeEvent: { data: null },
      } as any;

      act(() => {
        result.current.handleInputChange(mockEvent);
      });

      expect(result.current.inputValue).toBe('01/15/2024');
    });

    it('should handle empty input change', () => {
      const { result } = renderHook(() => useDateFieldState(defaultProps));

      const mockEvent = {
        target: { value: '' },
        nativeEvent: { data: null },
      } as any;

      act(() => {
        result.current.handleInputChange(mockEvent);
      });

      expect(result.current.date).toBeNull();
      expect(result.current.inputValue).toBe('');
    });

    it('should handle paste data with multiple characters', () => {
      const { result } = renderHook(() => useDateFieldState(defaultProps));

      const mockEvent = {
        target: { value: '01/15/2024' },
        nativeEvent: { data: '01/15/2024' },
      } as any;

      act(() => {
        result.current.handleInputChange(mockEvent);
      });

      expect(result.current.inputValue).toBe('01/15/2024');
    });

    it('should set active section when activeSectionIndex is provided', () => {
      const { result } = renderHook(() => useDateFieldState(defaultProps));

      const mockEvent = {
        target: { value: '01/15/2024' },
        nativeEvent: { data: null },
      } as any;

      act(() => {
        result.current.handleInputChange(mockEvent, 1); // Second section (day)
      });

      // Active section should be set based on format sections
    });
  });

  describe('handleClear', () => {
    it('should clear all values', () => {
      const onChange = vi.fn();
      const props = { ...defaultProps, onChange };
      const { result } = renderHook(() => useDateFieldState(props));

      // Set some initial state
      act(() => {
        result.current.updateValueFromValueStr('01/15/2024');
      });

      act(() => {
        result.current.handleClear();
      });

      expect(result.current.date).toBeNull();
      expect(result.current.inputValue).toBe('');
      expect(result.current.error).toBeNull();
      expect(result.current.characterQuery).toBeNull();
      expect(onChange).toHaveBeenCalledWith(null);
    });
  });

  describe('setActiveSection', () => {
    it('should set active section type', () => {
      const { result } = renderHook(() => useDateFieldState(defaultProps));

      act(() => {
        result.current.setActiveSection('month');
      });

      // Active section should be set (internal state)
    });

    it('should clear character query when setting active section to null', () => {
      const { result } = renderHook(() => useDateFieldState(defaultProps));

      act(() => {
        result.current.setActiveSection('month');
      });

      act(() => {
        result.current.setActiveSection(null);
      });

      expect(result.current.characterQuery).toBeNull();
    });
  });

  describe('error handling', () => {
    it('should handle parsing errors gracefully', () => {
      mockUtils.parse.mockImplementation(() => {
        throw new Error('Parse error');
      });

      const { result } = renderHook(() => useDateFieldState(defaultProps));

      act(() => {
        result.current.updateValueFromValueStr('invalid');
      });

      expect(result.current.error).toBe('Error processing date');
    });

    it('should handle section extraction errors', () => {
      // Mock the extractSectionsFromValueString to throw an error
      const mockExtractSections = vi.fn(() => {
        throw new Error('Extraction error');
      });

      // Access the mocked module and override the function
      vi.doMock('../../utils/sectionUtils', async () => {
        const actual = await vi.importActual('../../utils/sectionUtils');
        return {
          ...actual,
          extractSectionsFromValueString: mockExtractSections,
        };
      });

      const { result } = renderHook(() => useDateFieldState(defaultProps));

      act(() => {
        result.current.updateValueFromValueStr('01/15/2024');
      });

      // Should handle gracefully without crashing
    });
  });

  describe('active section merging', () => {
    it('should merge only active section when in active section mode', () => {
      const parsedDate = utils.date('2024-01-20');
      mockUtils.parse.mockReturnValue(parsedDate);
      mockUtils.isValid.mockReturnValue(true);

      const { result } = renderHook(() => useDateFieldState(defaultProps));

      // Set an active section
      act(() => {
        result.current.setActiveSection('day');
      });

      // Update value - should use active section merging
      act(() => {
        result.current.updateValueFromValueStr('20');
      });

      // Should have used fieldValueManager.mergeDateIntoReferenceDate with shouldLimitToEditedSections: true
    });

    it('should use standard merging when no active section', () => {
      const parsedDate = utils.date('2024-01-20');
      mockUtils.parse.mockReturnValue(parsedDate);
      mockUtils.isValid.mockReturnValue(true);

      const { result } = renderHook(() => useDateFieldState(defaultProps));

      act(() => {
        result.current.updateValueFromValueStr('01/20/2024');
      });

      // Should have used mergeParsedDateWithReference
    });
  });
});

describe('fieldValueManager', () => {
  const utils = new DayjsAdapter();
  const mockUtilsForFieldManager = {
    formatByString: vi.fn((date: PickerDateType, format: string) => utils.formatByString(date, format)),
    setYear: vi.fn((date: PickerDateType, year: number) => utils.setYear(date, year)),
    setMonth: vi.fn((date: PickerDateType, month: number) => utils.setMonth(date, month)),
    setDate: vi.fn((date: PickerDateType, day: number) => utils.setDate(date, day)),
    setHours: vi.fn((date: PickerDateType, hours: number) => utils.setHours(date, hours)),
    setMinutes: vi.fn((date: PickerDateType, minutes: number) => utils.setMinutes(date, minutes)),
    setSeconds: vi.fn((date: PickerDateType, seconds: number) => utils.setSeconds(date, seconds)),
    getYear: vi.fn((date: PickerDateType) => utils.getYear(date)),
    getMonth: vi.fn((date: PickerDateType) => utils.getMonth(date)),
    getDate: vi.fn((date: PickerDateType) => utils.getDate(date)),
    getHours: vi.fn((date: PickerDateType) => utils.getHours(date)),
    getMinutes: vi.fn((date: PickerDateType) => utils.getMinutes(date)),
    getSeconds: vi.fn((date: PickerDateType) => utils.getSeconds(date)),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('getSectionsFromValue', () => {
    it('should return empty array for null date', () => {
      const result = fieldValueManager.getSectionsFromValue(null, 'MM/DD/YYYY', mockUtilsForFieldManager);
      expect(result).toEqual([]);
    });

    it('should parse sections from valid date', () => {
      const date = utils.date('2024-01-15');
      mockUtilsForFieldManager.formatByString.mockReturnValue('01/15/2024');

      const result = fieldValueManager.getSectionsFromValue(date, 'MM/DD/YYYY', mockUtilsForFieldManager);

      expect(mockUtilsForFieldManager.formatByString).toHaveBeenCalledWith(date, 'MM/DD/YYYY');
    });
  });

  describe('mergeDateIntoReferenceDate', () => {
    const testSections = [
      { type: 'month' as FieldSectionType, value: '01', modified: true },
      { type: 'day' as FieldSectionType, value: '15', modified: false },
      { type: 'year' as FieldSectionType, value: '2024', modified: true },
    ];

    const dateToTransferFrom = utils.date('2024-01-15');
    const referenceDate = utils.date('2023-12-31');

    it('should merge all sections when shouldLimitToEditedSections is false', () => {
      const result = fieldValueManager.mergeDateIntoReferenceDate(
        mockUtilsForFieldManager,
        dateToTransferFrom,
        testSections,
        referenceDate,
        false,
        fieldValueManager.reliableSectionModificationOrder,
      );

      expect(mockUtilsForFieldManager.setMonth).toHaveBeenCalled();
      expect(mockUtilsForFieldManager.setDate).toHaveBeenCalled();
      expect(mockUtilsForFieldManager.setYear).toHaveBeenCalled();
    });

    it('should merge only modified sections when shouldLimitToEditedSections is true', () => {
      const result = fieldValueManager.mergeDateIntoReferenceDate(
        mockUtilsForFieldManager,
        dateToTransferFrom,
        testSections,
        referenceDate,
        true,
        fieldValueManager.reliableSectionModificationOrder,
      );

      expect(mockUtilsForFieldManager.setMonth).toHaveBeenCalled();
      expect(mockUtilsForFieldManager.setYear).toHaveBeenCalled();
      // setDate should not be called since day section is not modified
    });

    it('should sort sections by modification order', () => {
      const unsortedSections = [
        { type: 'year' as FieldSectionType, value: '2024', modified: true },
        { type: 'month' as FieldSectionType, value: '01', modified: true },
        { type: 'day' as FieldSectionType, value: '15', modified: true },
      ];

      fieldValueManager.mergeDateIntoReferenceDate(
        mockUtilsForFieldManager,
        dateToTransferFrom,
        unsortedSections,
        referenceDate,
        true,
        fieldValueManager.reliableSectionModificationOrder,
      );

      // Should process in order: month (2), day (3), year (1) -> but year should be first due to order
    });
  });

  describe('transferDateSectionValue', () => {
    const dateToTransferFrom = utils.date('2024-01-15T14:30:45');
    const dateToTransferTo = utils.date('2023-12-31T12:00:00');

    it('should transfer year value', () => {
      const section = { type: 'year' as FieldSectionType, value: '2024' };

      fieldValueManager.transferDateSectionValue(
        mockUtilsForFieldManager,
        section,
        dateToTransferFrom,
        dateToTransferTo,
      );

      expect(mockUtilsForFieldManager.getYear).toHaveBeenCalledWith(dateToTransferFrom);
      expect(mockUtilsForFieldManager.setYear).toHaveBeenCalled();
    });

    it('should transfer month value', () => {
      const section = { type: 'month' as FieldSectionType, value: '01' };

      fieldValueManager.transferDateSectionValue(
        mockUtilsForFieldManager,
        section,
        dateToTransferFrom,
        dateToTransferTo,
      );

      expect(mockUtilsForFieldManager.getMonth).toHaveBeenCalledWith(dateToTransferFrom);
      expect(mockUtilsForFieldManager.setMonth).toHaveBeenCalled();
    });

    it('should transfer day value', () => {
      const section = { type: 'day' as FieldSectionType, value: '15' };

      fieldValueManager.transferDateSectionValue(
        mockUtilsForFieldManager,
        section,
        dateToTransferFrom,
        dateToTransferTo,
      );

      expect(mockUtilsForFieldManager.getDate).toHaveBeenCalledWith(dateToTransferFrom);
      expect(mockUtilsForFieldManager.setDate).toHaveBeenCalled();
    });

    it('should transfer hours value', () => {
      const section = { type: 'hours' as FieldSectionType, value: '14' };

      fieldValueManager.transferDateSectionValue(
        mockUtilsForFieldManager,
        section,
        dateToTransferFrom,
        dateToTransferTo,
      );

      expect(mockUtilsForFieldManager.getHours).toHaveBeenCalledWith(dateToTransferFrom);
      expect(mockUtilsForFieldManager.setHours).toHaveBeenCalled();
    });

    it('should transfer minutes value', () => {
      const section = { type: 'minutes' as FieldSectionType, value: '30' };

      fieldValueManager.transferDateSectionValue(
        mockUtilsForFieldManager,
        section,
        dateToTransferFrom,
        dateToTransferTo,
      );

      expect(mockUtilsForFieldManager.setMinutes).toHaveBeenCalled();
    });

    it('should transfer seconds value', () => {
      const section = { type: 'seconds' as FieldSectionType, value: '45' };

      fieldValueManager.transferDateSectionValue(
        mockUtilsForFieldManager,
        section,
        dateToTransferFrom,
        dateToTransferTo,
      );

      expect(mockUtilsForFieldManager.setSeconds).toHaveBeenCalled();
    });

    it('should handle weekDay section (no-op)', () => {
      const section = { type: 'weekDay' as FieldSectionType, value: '1' };

      const result = fieldValueManager.transferDateSectionValue(
        mockUtilsForFieldManager,
        section,
        dateToTransferFrom,
        dateToTransferTo,
      );

      expect(result).toBe(dateToTransferTo);
    });

    it('should handle unknown section type', () => {
      const section = { type: 'empty' as FieldSectionType, value: 'test' };

      const result = fieldValueManager.transferDateSectionValue(
        mockUtilsForFieldManager,
        section,
        dateToTransferFrom,
        dateToTransferTo,
      );

      expect(result).toBe(dateToTransferTo);
    });

    describe('meridiem handling', () => {
      it('should convert PM to AM when source is AM and target is PM', () => {
        const sourceDate = utils.date('2024-01-15T10:30:00'); // 10 AM
        const targetDate = utils.date('2024-01-15T14:30:00'); // 2 PM

        mockUtilsForFieldManager.getHours.mockReturnValueOnce(10).mockReturnValueOnce(14);

        const section = { type: 'meridiem' as FieldSectionType, value: 'AM' };

        fieldValueManager.transferDateSectionValue(mockUtilsForFieldManager, section, sourceDate, targetDate);

        expect(mockUtilsForFieldManager.setHours).toHaveBeenCalledWith(targetDate, 2); // 14 - 12 = 2
      });

      it('should convert AM to PM when source is PM and target is AM', () => {
        const sourceDate = utils.date('2024-01-15T14:30:00'); // 2 PM
        const targetDate = utils.date('2024-01-15T10:30:00'); // 10 AM

        mockUtilsForFieldManager.getHours.mockReturnValueOnce(14).mockReturnValueOnce(10);

        const section = { type: 'meridiem' as FieldSectionType, value: 'PM' };

        fieldValueManager.transferDateSectionValue(mockUtilsForFieldManager, section, sourceDate, targetDate);

        expect(mockUtilsForFieldManager.setHours).toHaveBeenCalledWith(targetDate, 22); // 10 + 12 = 22
      });

      it('should not change hours when both are in same meridiem', () => {
        const sourceDate = utils.date('2024-01-15T10:30:00'); // 10 AM
        const targetDate = utils.date('2024-01-15T08:30:00'); // 8 AM

        mockUtilsForFieldManager.getHours.mockReturnValueOnce(10).mockReturnValueOnce(8);

        const section = { type: 'meridiem' as FieldSectionType, value: 'AM' };

        const result = fieldValueManager.transferDateSectionValue(
          mockUtilsForFieldManager,
          section,
          sourceDate,
          targetDate,
        );

        expect(result).toBe(targetDate);
      });
    });
  });

  describe('reliableSectionModificationOrder', () => {
    it('should have correct modification order', () => {
      const order = fieldValueManager.reliableSectionModificationOrder;

      expect(order.year).toBe(1);
      expect(order.month).toBe(2);
      expect(order.day).toBe(3);
      expect(order.weekDay).toBe(4);
      expect(order.hours).toBe(5);
      expect(order.minutes).toBe(6);
      expect(order.seconds).toBe(7);
      expect(order.meridiem).toBe(8);
      expect(order.empty).toBe(9);
    });
  });
});
