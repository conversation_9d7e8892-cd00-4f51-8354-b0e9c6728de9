/**
 * @vitest-environment jsdom
 */
import { renderHook } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { useFieldSelection } from './useFieldSelection';
import { FieldSectionData } from '../../models/dateSection';

// Mock the useSectionBoundaries hook
const mockFindSectionAtPosition = vi.fn();
const mockGetSectionSelection = vi.fn();

vi.mock('./useSectionBoundaries', () => ({
  useSectionBoundaries: () => ({
    findSectionAtPosition: mockFindSectionAtPosition,
    getSectionSelection: mockGetSectionSelection,
  }),
}));

describe('useFieldSelection', () => {
  const createMockSections = (): FieldSectionData[] => [
    {
      type: 'month',
      startIndex: 0,
      endIndex: 1,
      value: '01',
      displayValue: '01',
      placeholder: 'MM',
      maxLength: 2,
      separator: '/',
      token: 'MM',
      invalid: false,
    },
    {
      type: 'day',
      startIndex: 3,
      endIndex: 4,
      value: '15',
      displayValue: '15',
      placeholder: 'DD',
      maxLength: 2,
      separator: '/',
      token: 'DD',
      invalid: false,
    },
    {
      type: 'year',
      startIndex: 6,
      endIndex: 9,
      value: '2024',
      displayValue: '2024',
      placeholder: 'YYYY',
      maxLength: 4,
      separator: '',
      token: 'YYYY',
      invalid: false,
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('findActiveSectionByPosition', () => {
    it('should return null for empty sections array', () => {
      const { result } = renderHook(() => useFieldSelection());

      const sectionIndex = result.current.findActiveSectionByPosition([], 5);

      expect(sectionIndex).toBeNull();
    });

    it('should return null for null sections', () => {
      const { result } = renderHook(() => useFieldSelection());

      const sectionIndex = result.current.findActiveSectionByPosition(null as any, 5);

      expect(sectionIndex).toBeNull();
    });

    it('should return null for undefined sections', () => {
      const { result } = renderHook(() => useFieldSelection());

      const sectionIndex = result.current.findActiveSectionByPosition(undefined as any, 5);

      expect(sectionIndex).toBeNull();
    });

    it('should call findSectionAtPosition and return section index', () => {
      const { result } = renderHook(() => useFieldSelection());
      const sections = createMockSections();

      mockFindSectionAtPosition.mockReturnValue({ section: sections[1], sectionIndex: 1 });

      const sectionIndex = result.current.findActiveSectionByPosition(sections, 3);

      expect(mockFindSectionAtPosition).toHaveBeenCalledWith(sections, 3);
      expect(sectionIndex).toBe(1);
    });

    it('should return null when findSectionAtPosition returns null', () => {
      const { result } = renderHook(() => useFieldSelection());
      const sections = createMockSections();

      mockFindSectionAtPosition.mockReturnValue(null);

      const sectionIndex = result.current.findActiveSectionByPosition(sections, 100);

      expect(sectionIndex).toBeNull();
    });

    it('should handle extreme position values', () => {
      const { result } = renderHook(() => useFieldSelection());
      const sections = createMockSections();

      mockFindSectionAtPosition.mockReturnValue({ section: sections[0], sectionIndex: 0 });

      const sectionIndex = result.current.findActiveSectionByPosition(sections, Number.MAX_SAFE_INTEGER);

      expect(mockFindSectionAtPosition).toHaveBeenCalledWith(sections, Number.MAX_SAFE_INTEGER);
      expect(sectionIndex).toBe(0);
    });

    it('should handle negative position values', () => {
      const { result } = renderHook(() => useFieldSelection());
      const sections = createMockSections();

      mockFindSectionAtPosition.mockReturnValue({ section: sections[0], sectionIndex: 0 });

      const sectionIndex = result.current.findActiveSectionByPosition(sections, -5);

      expect(mockFindSectionAtPosition).toHaveBeenCalledWith(sections, -5);
      expect(sectionIndex).toBe(0);
    });

    it('should handle zero position', () => {
      const { result } = renderHook(() => useFieldSelection());
      const sections = createMockSections();

      mockFindSectionAtPosition.mockReturnValue({ section: sections[0], sectionIndex: 0 });

      const sectionIndex = result.current.findActiveSectionByPosition(sections, 0);

      expect(mockFindSectionAtPosition).toHaveBeenCalledWith(sections, 0);
      expect(sectionIndex).toBe(0);
    });
  });

  describe('getSectionFromSelection', () => {
    describe('empty/invalid sections handling', () => {
      it('should return null for empty sections array', () => {
        const { result } = renderHook(() => useFieldSelection());

        const sectionIndex = result.current.getSectionFromSelection([], 5, 10);

        expect(sectionIndex).toBeNull();
      });

      it('should return null for null sections', () => {
        const { result } = renderHook(() => useFieldSelection());

        const sectionIndex = result.current.getSectionFromSelection(null as any, 5, 10);

        expect(sectionIndex).toBeNull();
      });

      it('should return null for undefined sections', () => {
        const { result } = renderHook(() => useFieldSelection());

        const sectionIndex = result.current.getSectionFromSelection(undefined as any, 5, 10);

        expect(sectionIndex).toBeNull();
      });
    });

    describe('single caret position (selEnd is null)', () => {
      it('should use findActiveSectionByPosition when selEnd is null', () => {
        const { result } = renderHook(() => useFieldSelection());
        const sections = createMockSections();

        mockFindSectionAtPosition.mockReturnValue({ section: sections[1], sectionIndex: 1 });

        const sectionIndex = result.current.getSectionFromSelection(sections, 3, null);

        expect(mockFindSectionAtPosition).toHaveBeenCalledWith(sections, 3);
        expect(sectionIndex).toBe(1);
      });

      it('should use findActiveSectionByPosition when selStart equals selEnd', () => {
        const { result } = renderHook(() => useFieldSelection());
        const sections = createMockSections();

        mockFindSectionAtPosition.mockReturnValue({ section: sections[2], sectionIndex: 2 });

        const sectionIndex = result.current.getSectionFromSelection(sections, 7, 7);

        expect(mockFindSectionAtPosition).toHaveBeenCalledWith(sections, 7);
        expect(sectionIndex).toBe(2);
      });
    });

    describe('selection range matching', () => {
      it('should return section index when selection matches section boundaries exactly', () => {
        const { result } = renderHook(() => useFieldSelection());
        const sections = createMockSections();

        // Mock getSectionSelection to return ranges for each section
        mockGetSectionSelection
          .mockReturnValueOnce({ startPosition: 0, endPosition: 2 }) // month
          .mockReturnValueOnce({ startPosition: 3, endPosition: 5 }) // day
          .mockReturnValueOnce({ startPosition: 6, endPosition: 10 }); // year

        const sectionIndex = result.current.getSectionFromSelection(sections, 3, 5);

        expect(mockGetSectionSelection).toHaveBeenCalledTimes(2); // Called until match found
        expect(sectionIndex).toBe(1); // day section
      });

      it('should continue searching when no exact match found', () => {
        const { result } = renderHook(() => useFieldSelection());
        const sections = createMockSections();

        // Mock getSectionSelection to return non-matching ranges
        mockGetSectionSelection
          .mockReturnValueOnce({ startPosition: 0, endPosition: 2 }) // month - no match
          .mockReturnValueOnce({ startPosition: 3, endPosition: 5 }) // day - no match
          .mockReturnValueOnce({ startPosition: 6, endPosition: 10 }); // year - no match

        // Mock findSectionAtPosition as fallback
        mockFindSectionAtPosition.mockReturnValue({ section: sections[1], sectionIndex: 1 });

        const sectionIndex = result.current.getSectionFromSelection(sections, 2, 4);

        expect(mockGetSectionSelection).toHaveBeenCalledTimes(3); // Called for all sections
        expect(mockFindSectionAtPosition).toHaveBeenCalledWith(sections, 2); // Fallback to start position
        expect(sectionIndex).toBe(1);
      });

      it('should handle year section selection correctly', () => {
        const { result } = renderHook(() => useFieldSelection());
        const sections = createMockSections();

        mockGetSectionSelection
          .mockReturnValueOnce({ startPosition: 0, endPosition: 2 }) // month - no match
          .mockReturnValueOnce({ startPosition: 3, endPosition: 5 }) // day - no match
          .mockReturnValueOnce({ startPosition: 6, endPosition: 10 }); // year - no match

        // Mock findSectionAtPosition as fallback
        mockFindSectionAtPosition.mockReturnValue({ section: sections[0], sectionIndex: 0 });

        const sectionIndex = result.current.getSectionFromSelection(sections, 6, 10);

        // Implementation falls back to findSectionAtPosition which returns index 0
        expect(sectionIndex).toBe(0);
      });

      it('should handle edge case where selection spans multiple sections', () => {
        const { result } = renderHook(() => useFieldSelection());
        const sections = createMockSections();

        // No section should match this wide selection
        mockGetSectionSelection
          .mockReturnValueOnce({ startPosition: 0, endPosition: 2 })
          .mockReturnValueOnce({ startPosition: 3, endPosition: 5 })
          .mockReturnValueOnce({ startPosition: 6, endPosition: 10 });

        mockFindSectionAtPosition.mockReturnValue({ section: sections[0], sectionIndex: 0 });

        const sectionIndex = result.current.getSectionFromSelection(sections, 0, 10);

        expect(mockFindSectionAtPosition).toHaveBeenCalledWith(sections, 0);
        expect(sectionIndex).toBe(0);
      });
    });

    describe('boundary conditions', () => {
      it('should handle zero-length selection', () => {
        const { result } = renderHook(() => useFieldSelection());
        const sections = createMockSections();

        mockFindSectionAtPosition.mockReturnValue({ section: sections[0], sectionIndex: 0 });

        const sectionIndex = result.current.getSectionFromSelection(sections, 0, 0);

        expect(mockFindSectionAtPosition).toHaveBeenCalledWith(sections, 0);
        expect(sectionIndex).toBe(0);
      });

      it('should handle selection with negative values', () => {
        const { result } = renderHook(() => useFieldSelection());
        const sections = createMockSections();

        mockGetSectionSelection.mockReturnValue({ startPosition: 0, endPosition: 2 });
        mockFindSectionAtPosition.mockReturnValue({ section: sections[0], sectionIndex: 0 });

        const sectionIndex = result.current.getSectionFromSelection(sections, -1, 1);

        expect(mockFindSectionAtPosition).toHaveBeenCalledWith(sections, -1);
        expect(sectionIndex).toBe(0);
      });

      it('should handle extremely large selection values', () => {
        const { result } = renderHook(() => useFieldSelection());
        const sections = createMockSections();

        mockGetSectionSelection.mockReturnValue({ startPosition: 0, endPosition: 2 });
        mockFindSectionAtPosition.mockReturnValue({ section: sections[2], sectionIndex: 2 });

        const sectionIndex = result.current.getSectionFromSelection(
          sections,
          Number.MAX_SAFE_INTEGER,
          Number.MAX_SAFE_INTEGER + 1,
        );

        expect(mockFindSectionAtPosition).toHaveBeenCalledWith(sections, Number.MAX_SAFE_INTEGER);
        expect(sectionIndex).toBe(2);
      });
    });

    describe('single section scenarios', () => {
      it('should handle single section array', () => {
        const { result } = renderHook(() => useFieldSelection());
        const singleSection = [createMockSections()[0]];

        mockGetSectionSelection.mockReturnValue({ startPosition: 0, endPosition: 2 });

        const sectionIndex = result.current.getSectionFromSelection(singleSection, 0, 2);

        expect(sectionIndex).toBe(0);
      });

      it('should fallback correctly for single section when no match', () => {
        const { result } = renderHook(() => useFieldSelection());
        const singleSection = [createMockSections()[0]];

        mockGetSectionSelection.mockReturnValue({ startPosition: 0, endPosition: 2 });
        mockFindSectionAtPosition.mockReturnValue({ section: singleSection[0], sectionIndex: 0 });

        const sectionIndex = result.current.getSectionFromSelection(singleSection, 1, 3);

        expect(mockFindSectionAtPosition).toHaveBeenCalledWith(singleSection, 1);
        expect(sectionIndex).toBe(0);
      });
    });
  });

  describe('getSelectionForSection', () => {
    describe('invalid inputs', () => {
      it('should return {start: 0, end: 0} for null sections', () => {
        const { result } = renderHook(() => useFieldSelection());

        const selection = result.current.getSelectionForSection(null as any, 0);

        expect(selection).toEqual({ start: 0, end: 0 });
      });

      it('should return {start: 0, end: 0} for undefined sections', () => {
        const { result } = renderHook(() => useFieldSelection());

        const selection = result.current.getSelectionForSection(undefined as any, 0);

        expect(selection).toEqual({ start: 0, end: 0 });
      });

      it('should return {start: 0, end: 0} for null sectionIndex', () => {
        const { result } = renderHook(() => useFieldSelection());
        const sections = createMockSections();

        const selection = result.current.getSelectionForSection(sections, null as any);

        expect(selection).toEqual({ start: 0, end: 0 });
      });

      it('should return {start: 0, end: 0} for negative sectionIndex', () => {
        const { result } = renderHook(() => useFieldSelection());
        const sections = createMockSections();

        const selection = result.current.getSelectionForSection(sections, -1);

        expect(selection).toEqual({ start: 0, end: 0 });
      });

      it('should return {start: 0, end: 0} for sectionIndex beyond array length', () => {
        const { result } = renderHook(() => useFieldSelection());
        const sections = createMockSections();

        const selection = result.current.getSelectionForSection(sections, 10);

        expect(selection).toEqual({ start: 0, end: 0 });
      });

      it('should return {start: 0, end: 0} for empty sections array', () => {
        const { result } = renderHook(() => useFieldSelection());

        const selection = result.current.getSelectionForSection([], 0);

        expect(selection).toEqual({ start: 0, end: 0 });
      });
    });

    describe('valid section selection', () => {
      it('should return correct selection for valid section index', () => {
        const { result } = renderHook(() => useFieldSelection());
        const sections = createMockSections();

        mockGetSectionSelection.mockReturnValue({ startPosition: 3, endPosition: 5 });

        const selection = result.current.getSelectionForSection(sections, 1);

        expect(mockGetSectionSelection).toHaveBeenCalledWith(sections[1]);
        expect(selection).toEqual({ start: 3, end: 5 });
      });

      it('should handle first section (index 0)', () => {
        const { result } = renderHook(() => useFieldSelection());
        const sections = createMockSections();

        mockGetSectionSelection.mockReturnValue({ startPosition: 0, endPosition: 2 });

        const selection = result.current.getSelectionForSection(sections, 0);

        expect(mockGetSectionSelection).toHaveBeenCalledWith(sections[0]);
        expect(selection).toEqual({ start: 0, end: 2 });
      });

      it('should handle last section index', () => {
        const { result } = renderHook(() => useFieldSelection());
        const sections = createMockSections();

        mockGetSectionSelection.mockReturnValue({ startPosition: 6, endPosition: 10 });

        const selection = result.current.getSelectionForSection(sections, 2);

        expect(mockGetSectionSelection).toHaveBeenCalledWith(sections[2]);
        expect(selection).toEqual({ start: 6, end: 10 });
      });

      it('should handle zero-length selections', () => {
        const { result } = renderHook(() => useFieldSelection());
        const sections = createMockSections();

        mockGetSectionSelection.mockReturnValue({ startPosition: 5, endPosition: 5 });

        const selection = result.current.getSelectionForSection(sections, 1);

        expect(selection).toEqual({ start: 5, end: 5 });
      });

      it('should handle large position values', () => {
        const { result } = renderHook(() => useFieldSelection());
        const sections = createMockSections();

        mockGetSectionSelection.mockReturnValue({
          startPosition: Number.MAX_SAFE_INTEGER - 1,
          endPosition: Number.MAX_SAFE_INTEGER,
        });

        const selection = result.current.getSelectionForSection(sections, 0);

        expect(selection).toEqual({
          start: Number.MAX_SAFE_INTEGER - 1,
          end: Number.MAX_SAFE_INTEGER,
        });
      });
    });

    describe('edge cases with getSectionSelection', () => {
      it('should handle when getSectionSelection returns undefined', () => {
        const { result } = renderHook(() => useFieldSelection());
        const sections = createMockSections();

        mockGetSectionSelection.mockReturnValue(undefined as any);

        // Implementation throws an error when getSectionSelection returns undefined
        expect(() => {
          result.current.getSelectionForSection(sections, 0);
        }).toThrow();
      });

      it('should handle when getSectionSelection returns null', () => {
        const { result } = renderHook(() => useFieldSelection());
        const sections = createMockSections();

        mockGetSectionSelection.mockReturnValue(null as any);

        // Implementation throws an error when getSectionSelection returns null
        expect(() => {
          result.current.getSelectionForSection(sections, 0);
        }).toThrow();
      });

      it('should handle when getSectionSelection returns partial data', () => {
        const { result } = renderHook(() => useFieldSelection());
        const sections = createMockSections();

        mockGetSectionSelection.mockReturnValue({ startPosition: 3 } as any);

        expect(() => {
          result.current.getSelectionForSection(sections, 0);
        }).not.toThrow();
      });
    });
  });

  describe('integration scenarios', () => {
    it('should work correctly with typical date input sections', () => {
      const { result } = renderHook(() => useFieldSelection());
      const sections = createMockSections();

      // Test typical user interaction: clicking on month section
      mockFindSectionAtPosition.mockReturnValue({ section: sections[0], sectionIndex: 0 });
      mockGetSectionSelection.mockReturnValue({ startPosition: 0, endPosition: 2 });

      const clickPosition = 1; // Middle of month section
      const sectionIndex = result.current.findActiveSectionByPosition(sections, clickPosition);
      const selection = result.current.getSelectionForSection(sections, sectionIndex!);

      expect(sectionIndex).toBe(0);
      expect(selection).toEqual({ start: 0, end: 2 });
    });

    it('should handle transition between sections correctly', () => {
      const { result } = renderHook(() => useFieldSelection());
      const sections = createMockSections();

      // Test clicking on separator between sections
      mockFindSectionAtPosition.mockReturnValue({ section: sections[0], sectionIndex: 0 });

      const separatorPosition = 2; // Between month and day
      const sectionIndex = result.current.findActiveSectionByPosition(sections, separatorPosition);

      expect(sectionIndex).toBe(0); // Should default to previous section
    });

    it('should handle complex selection scenarios with multiple calls', () => {
      const { result } = renderHook(() => useFieldSelection());
      const sections = createMockSections();

      // Test sequence: click, drag to select, then get selection
      mockFindSectionAtPosition.mockReturnValue({ section: sections[1], sectionIndex: 1 });
      mockGetSectionSelection
        .mockReturnValueOnce({ startPosition: 0, endPosition: 2 })
        .mockReturnValueOnce({ startPosition: 3, endPosition: 5 }); // Match on day section

      const fromSelection = result.current.getSectionFromSelection(sections, 3, 5);
      const selection = result.current.getSelectionForSection(sections, fromSelection!);

      expect(fromSelection).toBe(1);
      expect(mockGetSectionSelection).toHaveBeenCalledWith(sections[1]);
    });
  });

  describe('performance considerations', () => {
    it('should not call getSectionSelection unnecessarily', () => {
      const { result } = renderHook(() => useFieldSelection());
      const sections = createMockSections();

      // First call should match immediately
      mockGetSectionSelection.mockReturnValueOnce({ startPosition: 0, endPosition: 2 });

      result.current.getSectionFromSelection(sections, 0, 2);

      expect(mockGetSectionSelection).toHaveBeenCalledTimes(1); // Should stop after first match
    });

    it('should handle large sections arrays efficiently', () => {
      const { result } = renderHook(() => useFieldSelection());
      const largeSections = Array.from({ length: 100 }, (_, i) => ({
        ...createMockSections()[0],
        type: `section${i}` as any,
        startIndex: i * 3,
        endIndex: i * 3 + 1,
      }));

      // Mock to return non-matching selections for all sections
      mockGetSectionSelection.mockReturnValue({ startPosition: 0, endPosition: 2 });
      mockFindSectionAtPosition.mockReturnValue({ section: largeSections[0], sectionIndex: 0 });

      const sectionIndex = result.current.getSectionFromSelection(largeSections, 297, 299);

      // Implementation falls back to findSectionAtPosition which returns index 0
      expect(sectionIndex).toBe(0);
    });
  });
});
