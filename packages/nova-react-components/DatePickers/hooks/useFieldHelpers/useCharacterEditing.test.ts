/**
 * @vitest-environment jsdom
 */
import { renderHook } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { useCharacterEditing } from './useCharacterEditing';
import { FieldSectionData, FieldSectionType } from '../../models/dateSection';

// Mock dependencies
vi.mock('../useUtils', () => ({
  useUtils: () => ({
    date: () => new Date(2023, 0, 1),
    setMonth: (date: Date, month: number) => new Date(date.getFullYear(), month, 1),
    setDate: (date: Date, day: number) => new Date(date.getFullYear(), date.getMonth(), day),
    startOfDay: (date: Date) => new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0),
    endOfDay: (date: Date) => new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59),
    formatByString: (date: Date, format: string) => {
      if (format.includes('MMMM')) return ['January', 'February', 'March', 'April'][date.getMonth()];
      if (format.includes('EEEE')) return ['Sunday', 'Monday', 'Tuesday', 'Wednesday'][date.getDay()];
      if (format.includes('a')) return date.getHours() < 12 ? 'AM' : 'PM';
      return date.toString();
    },
  }),
}));

vi.mock('./useSectionBoundaries', () => ({
  useSectionBoundaries: () => ({
    getSectionBoundaries: (type: FieldSectionType) => {
      const boundaries: Record<FieldSectionType, { minimum: number; maximum: number }> = {
        year: { minimum: 1900, maximum: 2100 },
        month: { minimum: 1, maximum: 12 },
        day: { minimum: 1, maximum: 31 },
        hours: { minimum: 0, maximum: 23 },
        minutes: { minimum: 0, maximum: 59 },
        seconds: { minimum: 0, maximum: 59 },
        meridiem: { minimum: 0, maximum: 1 },
        weekDay: { minimum: 0, maximum: 6 },
        empty: {
          minimum: 0,
          maximum: 0,
        },
      };
      return boundaries[type] || { minimum: 0, maximum: 9999 };
    },
  }),
}));

vi.mock('../../utils/sectionUtils', () => ({
  cleanDigitValue: (type: FieldSectionType, value: string) => {
    const num = parseInt(value, 10);
    if (isNaN(num)) return null;

    const boundaries = (
      {
        year: { min: 1900, max: 2100 },
        month: { min: 1, max: 12 },
        day: { min: 1, max: 31 },
        hours: { min: 0, max: 23 },
        minutes: { min: 0, max: 59 },
        seconds: { min: 0, max: 59 },
      } as any
    )[type];

    if (!boundaries) return value;

    if (num < boundaries.min) return null;
    if (num > boundaries.max) return null;
    return value.padStart(type === 'year' ? 4 : 2, '0');
  },
}));

describe('useCharacterEditing', () => {
  const mockSections: FieldSectionData[] = [
    {
      type: 'month',
      value: '01',
      contentType: 'digit',
      format: 'MM',
      maxLength: 2,
      displayValue: '',
      placeholder: '',
      startIndex: 0,
      endIndex: 0,
      separator: '',
      token: '',
      invalid: false,
    },
    {
      type: 'day',
      value: '01',
      contentType: 'digit',
      format: 'DD',
      maxLength: 2,
      displayValue: '',
      placeholder: '',
      startIndex: 0,
      endIndex: 0,
      separator: '',
      token: '',
      invalid: false,
    },
    {
      type: 'year',
      value: '2023',
      contentType: 'digit',
      format: 'YYYY',
      maxLength: 4,
      displayValue: '',
      placeholder: '',
      startIndex: 0,
      endIndex: 0,
      separator: '',
      token: '',
      invalid: false,
    },
    {
      type: 'month',
      value: 'January',
      contentType: 'letter',
      format: 'MMMM',
      displayValue: '',
      placeholder: '',
      maxLength: 0,
      startIndex: 0,
      endIndex: 0,
      separator: '',
      token: '',
      invalid: false,
    },
    {
      type: 'meridiem',
      value: 'AM',
      contentType: 'digit-with-letter',
      format: 'a',
      displayValue: '',
      placeholder: '',
      maxLength: 0,
      startIndex: 0,
      endIndex: 0,
      separator: '',
      token: '',
      invalid: false,
    },
  ];

  const mockUpdateSectionValue = vi.fn();
  const mockSetCharacterQuery = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should initialize with default functions', () => {
    const { result } = renderHook(() =>
      useCharacterEditing({
        activeSection: null,
        sections: mockSections,
        characterQuery: null,
        setCharacterQuery: mockSetCharacterQuery,
        updateSectionValue: mockUpdateSectionValue,
      }),
    );

    expect(result.current.processCharacterInput).toBeDefined();
    expect(result.current.handleSectionPaste).toBeDefined();
  });

  describe('processCharacterInput', () => {
    it('should return false when no active section', () => {
      const { result } = renderHook(() =>
        useCharacterEditing({
          activeSection: null,
          sections: mockSections,
          characterQuery: null,
          setCharacterQuery: mockSetCharacterQuery,
          updateSectionValue: mockUpdateSectionValue,
        }),
      );

      expect(result.current.processCharacterInput('1')).toBe(false);
    });

    it('should process digit input for month section', () => {
      const { result } = renderHook(() =>
        useCharacterEditing({
          activeSection: 0,
          sections: mockSections,
          characterQuery: null,
          setCharacterQuery: mockSetCharacterQuery,
          updateSectionValue: mockUpdateSectionValue,
        }),
      );

      expect(result.current.processCharacterInput('2')).toBe(true);
      expect(mockUpdateSectionValue).toHaveBeenCalledWith(0, '02', true);
    });

    it('should process digit input for day section', () => {
      const { result } = renderHook(() =>
        useCharacterEditing({
          activeSection: 1,
          sections: mockSections,
          characterQuery: null,
          setCharacterQuery: mockSetCharacterQuery,
          updateSectionValue: mockUpdateSectionValue,
        }),
      );

      expect(result.current.processCharacterInput('3')).toBe(true);
      expect(mockUpdateSectionValue).toHaveBeenCalledWith(1, '03', false);
    });

    it('should process digit input for year section', () => {
      const { result } = renderHook(() =>
        useCharacterEditing({
          activeSection: 2,
          sections: mockSections,
          characterQuery: null,
          setCharacterQuery: mockSetCharacterQuery,
          updateSectionValue: mockUpdateSectionValue,
        }),
      );

      expect(result.current.processCharacterInput('2')).toBe(true);
      expect(mockUpdateSectionValue).toHaveBeenCalledWith(2, '0002', false);
    });

    it('should process letter input for meridiem section', () => {
      const { result } = renderHook(() =>
        useCharacterEditing({
          activeSection: 4,
          sections: mockSections,
          characterQuery: null,
          setCharacterQuery: mockSetCharacterQuery,
          updateSectionValue: mockUpdateSectionValue,
        }),
      );

      expect(result.current.processCharacterInput('P')).toBe(true);
      expect(mockUpdateSectionValue).toHaveBeenCalledWith(4, 'PM', false);
    });

    it('should reject invalid digit input', () => {
      const { result } = renderHook(() =>
        useCharacterEditing({
          activeSection: 0,
          sections: mockSections,
          characterQuery: null,
          setCharacterQuery: mockSetCharacterQuery,
          updateSectionValue: mockUpdateSectionValue,
        }),
      );

      expect(result.current.processCharacterInput('a')).toBe(false);
      expect(mockUpdateSectionValue).not.toHaveBeenCalled();
    });
  });

  describe('handleSectionPaste', () => {
    it('should handle digit paste for month section', () => {
      const { result } = renderHook(() =>
        useCharacterEditing({
          activeSection: 0,
          sections: mockSections,
          characterQuery: null,
          setCharacterQuery: mockSetCharacterQuery,
          updateSectionValue: mockUpdateSectionValue,
        }),
      );

      const { newValue, shouldGoToNextSection } = result.current.handleSectionPaste(mockSections[0], '12');
      expect(newValue).toBe('12');
      expect(shouldGoToNextSection).toBe(true);
    });

    it('should handle partial digit paste for day section', () => {
      const { result } = renderHook(() =>
        useCharacterEditing({
          activeSection: 1,
          sections: mockSections,
          characterQuery: null,
          setCharacterQuery: mockSetCharacterQuery,
          updateSectionValue: mockUpdateSectionValue,
        }),
      );

      const { newValue, shouldGoToNextSection } = result.current.handleSectionPaste(mockSections[1], '3');
      expect(newValue).toBe('03');
      expect(shouldGoToNextSection).toBe(true);
    });

    it('should handle letter paste for month name section', () => {
      const { result } = renderHook(() =>
        useCharacterEditing({
          activeSection: 3,
          sections: mockSections,
          characterQuery: null,
          setCharacterQuery: mockSetCharacterQuery,
          updateSectionValue: mockUpdateSectionValue,
        }),
      );

      const { newValue, shouldGoToNextSection } = result.current.handleSectionPaste(mockSections[3], 'Feb');
      expect(newValue).toBe('Feb');
      expect(shouldGoToNextSection).toBe(true);
    });

    it('should handle AM/PM paste for meridiem section', () => {
      const { result } = renderHook(() =>
        useCharacterEditing({
          activeSection: 4,
          sections: mockSections,
          characterQuery: null,
          setCharacterQuery: mockSetCharacterQuery,
          updateSectionValue: mockUpdateSectionValue,
        }),
      );

      const { newValue, shouldGoToNextSection } = result.current.handleSectionPaste(mockSections[4], 'PM');
      expect(newValue).toBe('PM');
      expect(shouldGoToNextSection).toBe(true);
    });

    it('should reject invalid digit paste', () => {
      const { result } = renderHook(() =>
        useCharacterEditing({
          activeSection: 0,
          sections: mockSections,
          characterQuery: null,
          setCharacterQuery: mockSetCharacterQuery,
          updateSectionValue: mockUpdateSectionValue,
        }),
      );

      const { newValue, shouldGoToNextSection } = result.current.handleSectionPaste(mockSections[0], '13');
      expect(newValue).toBeNull();
      expect(shouldGoToNextSection).toBe(false);
    });

    it('should reject invalid letter paste', () => {
      const { result } = renderHook(() =>
        useCharacterEditing({
          activeSection: 3,
          sections: mockSections,
          characterQuery: null,
          setCharacterQuery: mockSetCharacterQuery,
          updateSectionValue: mockUpdateSectionValue,
        }),
      );

      const { newValue, shouldGoToNextSection } = result.current.handleSectionPaste(mockSections[3], 'Xyz');
      expect(newValue).toBe('Xyz');
      expect(shouldGoToNextSection).toBe(true);
    });
  });
});
