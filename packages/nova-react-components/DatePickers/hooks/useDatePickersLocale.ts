import { useLocale } from '../../Locale';
import { DatePickersLocale } from '../types';

/**
 * Default locale text for DatePickers components.
 * Only includes locale keys that are actually used in the codebase.
 */
/**
 * Default locale text for DatePickers components.
 * Matches the DatePickersLocale interface structure.
 */
const DEFAULT_DATE_PICKERS_LOCALE: Required<DatePickersLocale> = {
  // Action Bar - Button labels
  clearText: 'Clear',
  cancelText: 'Cancel',
  okText: 'OK',

  // Date input Labels
  fieldLabel: 'Date',
  rangeFieldLabelStart: 'Start',
  rangeFieldLabelEnd: 'End',

  // input title
  inputTitle: 'Enter date',
  rangeInputTitle: 'Enter dates',

  // Toolbar Titles
  headerLabel: 'Select date',
  rangeHeaderLabel: 'Select dates',
  rangePickerLabel: 'Depart - return dates',
};

/**
 * Hook to get DatePickers locale text with fallback defaults.
 * Follows MUI's pattern of providing complete locale object with proper fallbacks.
 * @returns Complete DatePickersLocale object with all required strings and functions
 */
export function useDatePickersLocale(): Required<DatePickersLocale> {
  const locale = useLocale('DatePickers') as Partial<DatePickersLocale>;

  // Merge with defaults, allowing partial overrides like MUI
  return {
    ...DEFAULT_DATE_PICKERS_LOCALE,
    ...locale,
  } as Required<DatePickersLocale>;
}
