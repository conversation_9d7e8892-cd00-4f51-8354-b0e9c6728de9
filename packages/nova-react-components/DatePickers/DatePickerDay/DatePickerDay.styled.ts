import { styled } from '@pigment-css/react';
import { datePickerDayClasses } from './datePickerDayClasses';
import { ButtonBase } from '../../ButtonBase';

// Create a basic styled button that applies the core styles
export const DatePickerDayRoot = styled(ButtonBase)(({ theme }) => ({
  width: 'auto',
  height: 'auto',
  aspectRatio: '1/1',
  display: 'flex',
  flex: 1,
  justifyContent: 'center',
  alignItems: 'center',
  border: 'none',
  borderRadius: '50%',
  cursor: 'pointer',
  backgroundColor: 'transparent',
  position: 'relative',
  padding: 0,
  margin: 0,
  color: theme.vars.palette.onSurface,
  ...theme.typography.bodySmall,

  // Hover state
  '&:hover:not(:disabled)': {
    backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundStates}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.hoverOnSurface})`,
  },

  // Active state
  '&:active:not(:disabled)': {
    backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundStates}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.pressOnSurface})`,
  },

  // Focus state
  '&:focus:not(:disabled)': {
    backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundStates}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.pressOnSurface})`,
  },

  // Selected state
  [`&.${datePickerDayClasses.selected}`]: {
    backgroundColor: theme.vars.palette.primary,
    color: theme.vars.palette.onPrimary,
    '&:hover:not(:disabled)': {
      backgroundColor: `color-mix(in srgb, ${theme.vars.palette.primary}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.hoverOnPrimary})`,
    },
    '&:active:not(:disabled)': {
      backgroundColor: `color-mix(in srgb, ${theme.vars.palette.primary}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.pressOnPrimary})`,
    },
    '&:focus:not(:disabled)': {
      backgroundColor: `color-mix(in srgb, ${theme.vars.palette.primary}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.pressOnPrimary})`,
    },
  },

  // Today state
  [`&.${datePickerDayClasses.today}`]: {
    border: `1px solid ${theme.vars.palette.primary}`,
    '&:hover': {
      backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundStates}, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.hoverOnPrimary})`,
    },
    '&:active': {
      backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundStates}, ${theme.vars.palette.primary} ${theme.vars.palette.stateLayers.pressOnSurface})`,
    },
    '&:focus': {
      border: 'none',
      backgroundColor: `color-mix(in srgb, ${theme.vars.palette.backgroundStates}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.pressOnSurface})`,
    },
  },

  // Outside month state
  [`&.${datePickerDayClasses.outsideCurrentMonth}`]: {
    color: theme.vars.palette.onSurfaceVariant,
    opacity: 0.6,
  },

  // Disabled state
  '&:disabled': {
    color: theme.vars.palette.onSurfaceVariant,
    cursor: 'default',
    opacity: 0.38,
  },

  // Focus visible
  '&:focus-visible': {
    outlineOffset: '2px',
    outline: `1px solid ${theme.vars.palette.secondary}`,
  },

  // No margin
  [`&.${datePickerDayClasses.noMargin}`]: {
    margin: 0,
  },
}));
