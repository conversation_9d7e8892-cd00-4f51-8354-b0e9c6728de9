import { styled } from '@pigment-css/react';
import { SideSheetOwnerState } from './SideSheet.types';
import { Modal } from '../Modal';

const transitionDuration = {
  enter: 225,
  exit: 195,
};

export const SideSheetBackdrop = styled('div', {
  name: 'NovaSideSheet',
  slot: 'Backdrop',
})<SideSheetOwnerState>(({ theme }) => ({
  position: 'fixed',
  inset: 0,
  right: 0,
  bottom: 0,
  top: 0,
  left: 0,
  zIndex: -1,
  opacity: 0,
  backgroundColor: 'rgba(0, 0, 0, 0.5)',
  WebkitTapHighlightColor: 'transparent',
  transitionProperty: 'opacity',
  transitionDuration: 'var(--nova-sideSheet-transitionDuration-enter)',
  transitionTimingFunction: 'cubic-bezier(0.4, 0, 0.6, 1)',
  variants: [
    {
      props: { open: true },
      style: {
        opacity: 1,
        transitionDuration: 'var(--nova-sideSheet-transitionDuration-exit)',
      },
    },
  ],
}));

export const SideSheetContainer = styled('div', {
  name: 'NovaSideSheet',
  slot: 'Container',
})<SideSheetOwnerState>(({ theme }) => ({
  '--nova-sideSheet-background': theme.vars.palette.surface,
  position: 'fixed',
  display: 'flex',
  flexDirection: 'column',
  height: '100%',
  zIndex: 1300,
  top: 0,
  outline: 0,
  boxShadow: theme.vars.shadows[3],
  backgroundColor: 'var(--nova-sideSheet-background)',
  width: 'var(--nova-sideSheet-width)',
  transform: 'translateX(-100%)',
  transitionProperty: 'transform',
  transitionDuration: 'var(--nova-sideSheet-transitionDuration-enter)',
  transitionTimingFunction: 'cubic-bezier(0.4, 0, 0.6, 1)',
  variants: [
    {
      props: { open: false },
      style: {
        transitionDuration: 'var(--nova-sideSheet-transitionDuration-exit)',
      },
    },
    {
      props: { anchor: 'left' },
      style: {
        left: 0,
        borderRightWidth: '1px',
        borderRightStyle: 'solid',
        borderRightColor: theme.vars.palette?.outlineVariant,
      },
    },
    {
      props: { anchor: 'left', open: false },
      style: {
        transform: 'translateX(-100%)',
      },
    },
    {
      props: { anchor: 'left', open: true },
      style: {
        transform: 'translateX(0)',
      },
    },
    {
      props: { anchor: 'right' },
      style: {
        right: 0,
        borderLeftWidth: '1px',
        borderLeftStyle: 'solid',
        borderLeftColor: theme.vars.palette?.outlineVariant,
      },
    },
    {
      props: { anchor: 'right', open: false },
      style: {
        transform: 'translateX(100%)',
      },
    },
    {
      props: { anchor: 'right', open: true },
      style: {
        transform: 'translateX(0)',
      },
    },
  ],
}));

export const SideSheetModalRoot = styled(Modal, {
  name: 'NovaSideSheet',
  slot: 'ModalRoot',
})(({ theme }) => ({
  position: 'fixed',
  right: 0,
  bottom: 0,
  top: 0,
  left: 0,
  zIndex: 1300,
  transitionProperty: 'visibility',
  transitionDelay: '0s',
  '--nova-sideSheet-modal-transition-delay': `${transitionDuration.exit}ms`,
  '--nova-sideSheet-transitionDuration-enter': `${transitionDuration.enter}ms`,
  '--nova-sideSheet-transitionDuration-exit': `${transitionDuration.exit}ms`,
  variants: [
    {
      props: { open: false },
      style: {
        visibility: 'hidden',
        transitionDelay: 'var(--nova-sideSheet-modal-transition-delay)',
      },
    },
  ],
}));

export const SideSheetHeader = styled('div')(() => ({
  padding: '24px 24px 16px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  color: 'var(--palette-onSurface)',
  gap: '8px',
  // Add styles to handle Android-style header layout
  '& > :first-of-type': {
    display: 'flex',
    alignItems: 'center',
  },
  // Style for the right side (typically close button)
  '& > :last-child:not(:first-child)': {
    marginLeft: 'auto',
  },
}));

export const SideSheetFooter = styled('div')(() => ({
  padding: '24px',
  display: 'flex',
  justifyContent: 'flex-end',
  gap: '28px',
}));

export const SideSheetContent = styled('div')(() => ({
  display: 'flex',
  flexDirection: 'column',
  flexGrow: '1',
  overflow: 'auto',
  padding: '24px',
  boxSizing: 'border-box',
}));
