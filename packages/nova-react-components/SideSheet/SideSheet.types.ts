import * as React from 'react';
import { OverridableStringUnion, OverrideProps } from '@mui/types';
import { CreateSlotsAndSlotProps, SlotCommonProps, SlotProps } from '../types/slot';
import { ModalProps } from '../Modal';

export interface SideSheetPropsAnchorOverrides {}

export type SideSheetAnchor = 'left' | 'right';

export type SideSheetSlot = 'root' | 'container';

export interface SideSheetSlots {
  /**
   * The component that renders the root of the sideSheet.
   * @default div
   */
  root?: React.ElementType;

  /**
   * The component that renders the content of the sideSheet.
   * @default 'div'
   */
  container?: React.ElementType;
}

export type SideSheetSlotsAndSlotProps = CreateSlotsAndSlotProps<
  SideSheetSlots,
  {
    root: SlotProps<'div', object, SideSheetOwnerState>;
    container: SlotProps<'div', object, SideSheetOwnerState>;
  }
>;

export interface SideSheetBaseProps {
  /**
   * Side from which the side sheet will appear.
   * @default 'right'
   */
  anchor?: OverridableStringUnion<SideSheetAnchor, SideSheetPropsAnchorOverrides>;

  /**
   * The content of the component.
   */
  children?: React.ReactNode;

  /**
   * Callback fired when the component requests to be closed.
   * The `reason` parameter can optionally be used to control the response to `onClose`.
   *
   * @param {object} event The event source of the callback.
   */
  onClose?: (event: React.SyntheticEvent) => void;

  /**
   * Width of the side sheet.
   * @default 300
   */
  width?: number | string;
}

export interface SideSheetTypeMap<P = object, D extends React.ElementType = 'div'> {
  props: P & Omit<ModalProps, 'onClose' | 'keepMounted' | 'children'> & SideSheetSlotsAndSlotProps & SideSheetBaseProps;
  defaultComponent: D;
}

export type SideSheetProps<
  D extends React.ElementType = SideSheetTypeMap['defaultComponent'],
  P = SlotCommonProps,
> = OverrideProps<SideSheetTypeMap<P, D>, D>;

export interface SideSheetOwnerState extends SideSheetProps {}
