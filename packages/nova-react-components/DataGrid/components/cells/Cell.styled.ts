import { styled } from '@pigment-css/react';
import { DataGridProps } from '../../DataGrid.types';
import { IconButton } from '../../../IconButton';

type SizeOwnerState = {
  size?: 'small' | 'medium' | 'large';
};
export const CheckboxCellItemRoot = styled('div')<SizeOwnerState>(() => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  flex: '0 0 auto',
  height: 'var(--nova-datagrid-row-height)',
  variants: [
    {
      props: { size: 'small' },
      style: {
        minWidth: '32px',
      },
    },
    {
      props: { size: 'medium' },
      style: {
        minWidth: '40px',
      },
    },
    {
      props: { size: 'large' },
      style: {
        minWidth: '40px',
      },
    },
  ],
}));

export const EditCellInputRoot = styled('div')(() => ({
  display: 'flex',
  alignItems: 'center',
  height: '100%',
}));

export const EmptyCellItemRoot = styled('div')(() => ({
  flex: 1,
  padding: 0,
  display: 'flex',
  border: 0,
}));

export const ExpandCellItemRoot = styled('div')<SizeOwnerState>(() => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  flex: '0 0 auto',
  height: 'var(--nova-datagrid-row-height)',
  variants: [
    {
      props: { size: 'small' },
      style: {
        minWidth: '32px',
      },
    },
    {
      props: { size: 'medium' },
      style: {
        minWidth: '40px',
      },
    },
    {
      props: { size: 'large' },
      style: {
        minWidth: '40px',
      },
    },
  ],
}));

export type HeaderCellOwnerState = {
  density: DataGridProps['density'];
  fixed?: 'start' | 'end';
};

export const HeaderCellRoot = styled('div')<HeaderCellOwnerState>(({ theme }) => ({
  position: 'relative',
  display: 'flex',
  alignItems: 'center',
  flex: '0 0 auto',
  padding: '0px 24px',
  color: theme.vars.palette.onSurface,
  fontWeight: 700,
  boxSizing: 'border-box',
  overflow: 'hidden',
  minWidth: '50px',
  height: 'var(--nova-datagrid-header-height)',
  lineHeight: 'var(--nova-datagrid-header-height)',
  maxHeight: 'max-content',
  '--nova-datagrid-unSortCell-visibility': 'hidden',
  '&:hover': {
    '--nova-datagrid-unSortCell-visibility': 'visible',
  },
  variants: [
    {
      props: { density: 'compact' },
      style: {
        padding: '0px 16px',
      },
    },
    {
      props: { fixed: 'start' },
      style: {
        position: 'sticky',
        left: 'var(--nova-datagrid-fixed-startOffset)',
        background: theme.vars.palette.secondaryContainer,
        zIndex: 3,
        height: 'calc(var(--nova-datagrid-header-height) - 2px)',
        minHeight: 'calc(var(--nova-datagrid-header-height) - 2px)',
      },
    },
    {
      props: { fixed: 'end' },
      style: {
        position: 'sticky',
        right: 'var(--nova-datagrid-fixed-endOffset)',
        background: theme.vars.palette.secondaryContainer,
        zIndex: 3,
        height: 'calc(var(--nova-datagrid-header-height) - 2px)',
        minHeight: 'calc(var(--nova-datagrid-header-height) - 2px)',
      },
    },
  ],
}));

export const HeaderCellText = styled('div')(() => ({
  overflow: 'hidden',
  whiteSpace: 'nowrap',
  textOverflow: 'ellipsis',
}));

type HeaderCellResizeOwnerState = {
  fixed?: 'left' | 'right';
};

export const HeaderCellResizeHandleRoot = styled('div')<HeaderCellResizeOwnerState>(({ theme }) => ({
  position: 'absolute',
  overflow: 'hidden',
  display: 'flex',
  alignItems: 'center',
  width: '10px',
  cursor: 'col-resize',
  touchAction: 'none',
  zIndex: 2,
  '--nova-datagrid-resize-color': theme.vars.palette.onBackgroundDisabled,
  ':hover': {
    cursor: 'col-resize',
  },
  ':active': {
    cursor: 'col-resize',
  },
  variants: [
    {
      props: { fixed: 'left' },
      style: {
        left: '1px',
      },
    },
    {
      props: { fixed: 'right' },
      style: {
        right: '-9px',
        left: 'unset',
      },
    },
  ],
}));

export const HeaderCellResizeHandleInner = styled('div')(({ theme }) => ({
  width: '1px',
  background: 'var(--nova-datagrid-resize-color)',
  height: '20px',
}));

type HeaderCellIconButtonOwnerState = {
  hasSort: boolean;
};

export const HeaderCellIconButtonRoot = styled(IconButton)<HeaderCellIconButtonOwnerState>(({ theme }) => ({
  marginLeft: '2px',
  visibility: 'visible',
  overflow: 'visible',
  variants: [
    {
      props: { hasSort: false },
      style: {
        visibility: 'var(--nova-datagrid-unSortCell-visibility)' as 'visible' | 'hidden',
        '& svg': {
          color: theme.vars.palette.onBackgroundDisabled,
        },
      },
    },
  ],
}));

type RadioCellItemRootOwnerState = {
  invisible?: boolean;
} & SizeOwnerState;

export const RadioCellItemRoot = styled('div')<RadioCellItemRootOwnerState>(() => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  flex: '0 0 auto',
  height: 'var(--nova-datagrid-row-height)',
  variants: [
    {
      props: { invisible: true },
      style: {
        visibility: 'hidden',
      },
    },
    {
      props: { size: 'small' },
      style: {
        minWidth: '32px',
      },
    },
    {
      props: { size: 'medium' },
      style: {
        minWidth: '40px',
      },
    },
    {
      props: { size: 'large' },
      style: {
        minWidth: '40px',
      },
    },
  ],
}));

export type RowCellItemRootOwnerState = {
  density: DataGridProps['density'];
  selected: boolean;
  cellSelection?: boolean;
  isEditing: boolean;
  fixed?: 'start' | 'end';
  showPinBorder?: boolean;
};

export const RowCellItemRoot = styled('div')<RowCellItemRootOwnerState>(({ theme }) => ({
  flex: '0 0 auto',
  padding: '0px 24px',
  boxSizing: 'border-box',
  overflow: 'hidden',
  whiteSpace: 'nowrap',
  textOverflow: 'ellipsis',
  color: theme.vars.palette.onSurface,
  minWidth: '50px',
  height: 'var(--nova-datagrid-row-height)',
  lineHeight: 'var(--nova-datagrid-row-height)',
  '&:focus-visible': {
    outline: `2px solid ${theme.vars.palette.secondary}`,
    outlineOffset: -2,
  },
  variants: [
    {
      props: { density: 'compact' },
      style: {
        padding: '0px 16px',
      },
    },
    {
      props: { cellSelection: true, selected: false },
      style: {
        '&:focus': {
          outline: `2px solid ${theme.vars.palette.secondary}`,
          outlineOffset: -2,
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.focusOnSurface})`,
        },
        '&:active': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.surfaceContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.pressOnSurface})`,
        },
      },
    },
    {
      props: { cellSelection: true, selected: true },
      style: {
        background: theme.vars.palette.secondaryContainer,
        '&:focus': {
          outline: `2px solid ${theme.vars.palette.secondary}`,
          outlineOffset: -2,
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.secondaryContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.focusOnSurface})`,
        },
        '&:active': {
          backgroundColor: `color-mix(in srgb, ${theme.vars.palette.secondaryContainer}, ${theme.vars.palette.onSurface} ${theme.vars.palette.stateLayers.pressOnSurface})`,
        },
      },
    },
    {
      props: { fixed: 'start' },
      style: {
        position: 'sticky',
        zIndex: 3,
        left: 'var(--nova-datagrid-fixed-startOffset)',
        boxSizing: 'border-box',
        backgroundColor: theme.vars.palette.surfaceContainer,
        height: 'calc(var(--nova-datagrid-row-height) - 2px)',
      },
    },
    {
      props: { fixed: 'start', showPinBorder: true },
      style: {
        borderRight: `1px solid ${theme.vars.palette.outlineVariant}`,
        borderLeft: 'unset',
      },
    },
    {
      props: { fixed: 'end' },
      style: {
        position: 'sticky',
        zIndex: 3,
        right: 'var(--nova-datagrid-fixed-endOffset)',
        boxSizing: 'border-box',
        backgroundColor: theme.vars.palette.surfaceContainer,
        height: 'calc(var(--nova-datagrid-row-height) - 2px)',
      },
    },
    {
      props: { fixed: 'end', showPinBorder: true },
      style: {
        borderLeft: `1px solid ${theme.vars.palette.outlineVariant}`,
        borderRight: 'unset',
      },
    },
  ],
}));
