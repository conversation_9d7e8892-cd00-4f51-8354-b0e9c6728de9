import { describe, expect, it } from 'vitest';
import {
  listReducer,
  moveHighlight,
  toggleSelection,
  findValidItemToHighlight,
  handleItemSelection,
} from './listReducer';
import { ListActionTypes } from './listActions.types';
import { ListState, ListActionContext } from './useList.types';

describe('listReducer', () => {
  const defaultContext: ListActionContext<number> = {
    items: [1, 2, 3, 4, 5],
    isItemDisabled: (item) => item === 2, // item 2 is disabled
    disabledItemsFocusable: false,
    disableListWrap: false,
    focusManagement: 'activeDescendant',
    itemComparer: (a, b) => a === b,
    selectionMode: 'multiple',
    orientation: 'vertical',
    pageSize: 3,
    getItemAsString: (item) => item.toString(),
  };

  const initialState: ListState<number> = {
    highlightedValue: null,
    selectedValues: [],
  };

  describe('findValidItemToHighlight', () => {
    it('returns -1 when items array is empty', () => {
      const result = findValidItemToHighlight(0, 'next', [], false, () => false, false);
      expect(result).toBe(-1);
    });

    it('returns -1 when all items are disabled and includeDisabledItems is false', () => {
      const result = findValidItemToHighlight(
        0,
        'next',
        [1, 2, 3],
        false,
        () => true, // all items disabled
        false,
      );
      expect(result).toBe(-1);
    });

    it('returns next valid item when searching forward', () => {
      const result = findValidItemToHighlight(
        0,
        'next',
        [1, 2, 3, 4],
        false,
        (item) => item === 1, // item 1 is disabled
        false,
      );
      expect(result).toBe(1);
    });

    it('returns previous valid item when searching backward', () => {
      const result = findValidItemToHighlight(
        3,
        'previous',
        [1, 2, 3, 4],
        false,
        (item) => item === 3, // item 3 is disabled
        false,
      );
      expect(result).toBe(3);
    });

    it('wraps around when wrapAround is true', () => {
      const result = findValidItemToHighlight(
        3, // start at index 3
        'next',
        [1, 2, 3, 4],
        false,
        (item) => item === 4, // item 4 is disabled
        true,
      );
      expect(result).toBe(0); // should wrap to index 0
    });

    it('returns -1 when no valid items found and wrapAround is false', () => {
      const result = findValidItemToHighlight(
        3,
        'next',
        [1, 2, 3, 4],
        false,
        (item) => item === 4, // item 4 is disabled
        false,
      );
      expect(result).toBe(-1);
    });
  });

  describe('moveHighlight', () => {
    it('returns first item when offset is "start"', () => {
      const result = moveHighlight(null, 'start', defaultContext);
      expect(result).toBe(1);
    });

    it('returns last item when offset is "end"', () => {
      const result = moveHighlight(null, 'end', defaultContext);
      expect(result).toBe(5);
    });

    it('returns first item when offset is "reset" and focusManagement is DOM', () => {
      const result = moveHighlight(null, 'reset', { ...defaultContext, focusManagement: 'DOM' });
      expect(result).toBe(1);
    });

    it('returns null when offset is "reset" and focusManagement is not DOM', () => {
      const result = moveHighlight(null, 'reset', defaultContext);
      expect(result).toBe(null);
    });

    it('moves highlight by positive offset', () => {
      const result = moveHighlight(1, 2, defaultContext);
      expect(result).toBe(3);
    });

    it('moves highlight by negative offset', () => {
      const result = moveHighlight(4, -2, defaultContext);
      expect(result).toBe(1);
    });

    it('wraps around when reaching end of list', () => {
      const result = moveHighlight(4, 1, defaultContext);
      expect(result).toBe(5);
    });

    it('wraps around when reaching start of list', () => {
      const result = moveHighlight(1, -1, defaultContext);
      expect(result).toBe(5);
    });

    it('does not wrap around when disableListWrap is true', () => {
      const result = moveHighlight(4, 1, { ...defaultContext, disableListWrap: true });
      expect(result).toBe(5); // stays at end
    });

    it('returns previously highlighted value if no valid items found', () => {
      const context = {
        ...defaultContext,
        items: [1, 2, 3],
        isItemDisabled: () => true, // all items disabled
      };
      const result = moveHighlight(1, 1, context);
      expect(result).toBe(null);
    });

    it('handles horizontal-ltr orientation for arrow keys', () => {
      const context: any = { ...defaultContext, orientation: 'horizontal-ltr' };
      const resultRight = moveHighlight(1, 1, context);
      const resultLeft = moveHighlight(3, -1, context);
      expect(resultRight).toBe(3); // skips disabled 2
      expect(resultLeft).toBe(1);
    });

    it('handles horizontal-rtl orientation for arrow keys', () => {
      const context: any = { ...defaultContext, orientation: 'horizontal-rtl' };
      const resultRight = moveHighlight(3, 1, context);
      const resultLeft = moveHighlight(1, -1, context);
      expect(resultRight).toBe(4);
      expect(resultLeft).toBe(5);
    });
  });

  describe('toggleSelection', () => {
    it('returns empty array when selectionMode is "none"', () => {
      const result = toggleSelection(1, [1, 2, 3], 'none', (a, b) => a === b);
      expect(result).toEqual([]);
    });

    it('returns single item when selectionMode is "single"', () => {
      const result = toggleSelection(2, [1], 'single', (a, b) => a === b);
      expect(result).toEqual([2]);
    });

    it('returns same array when item already selected in single mode', () => {
      const result = toggleSelection(1, [1], 'single', (a, b) => a === b);
      expect(result).toEqual([1]);
    });

    it('adds item to selection when not selected in multiple mode', () => {
      const result = toggleSelection(3, [1, 2], 'multiple', (a, b) => a === b);
      expect(result).toEqual([1, 2, 3]);
    });

    it('removes item from selection when already selected in multiple mode', () => {
      const result = toggleSelection(2, [1, 2, 3], 'multiple', (a, b) => a === b);
      expect(result).toEqual([1, 3]);
    });
  });

  describe('handleItemSelection', () => {
    it('returns unchanged state when item is disabled', () => {
      const state = { ...initialState, selectedValues: [1] };
      const result = handleItemSelection(2, state, defaultContext);
      expect(result).toEqual(state);
    });

    it('toggles item selection and highlights it', () => {
      const state = { ...initialState, selectedValues: [1] };
      const result = handleItemSelection(3, state, defaultContext);
      expect(result).toEqual({
        highlightedValue: 3,
        selectedValues: [1, 3],
      });
    });

    it('handles single selection mode', () => {
      const context: any = { ...defaultContext, selectionMode: 'single' };
      const state = { ...initialState, selectedValues: [1] };
      const result = handleItemSelection(3, state, context);
      expect(result).toEqual({
        highlightedValue: 3,
        selectedValues: [3],
      });
    });
  });

  describe('handleKeyDown', () => {
    it('handles Home key', () => {
      const state = { ...initialState, highlightedValue: 3 };
      const action: any = {
        type: ListActionTypes.keyDown,
        key: 'Home',
        context: defaultContext,
      };
      const result = listReducer(state, action);
      expect(result.highlightedValue).toBe(1);
    });

    it('handles End key', () => {
      const state = { ...initialState, highlightedValue: 3 };
      const action: any = {
        type: ListActionTypes.keyDown,
        key: 'End',
        context: defaultContext,
      };
      const result = listReducer(state, action);
      expect(result.highlightedValue).toBe(5);
    });

    it('handles PageUp key', () => {
      const state = { ...initialState, highlightedValue: 5 };
      const action: any = {
        type: ListActionTypes.keyDown,
        key: 'PageUp',
        context: defaultContext,
      };
      const result = listReducer(state, action);
      expect(result.highlightedValue).toBe(1); // 5 - 3 (pageSize) = 2 (disabled) -> 1
    });

    it('handles PageDown key', () => {
      const state = { ...initialState, highlightedValue: 1 };
      const action: any = {
        type: ListActionTypes.keyDown,
        key: 'PageDown',
        context: defaultContext,
      };
      const result = listReducer(state, action);
      expect(result.highlightedValue).toBe(4);
    });

    it('handles ArrowUp key', () => {
      const state = { ...initialState, highlightedValue: 3 };
      const action: any = {
        type: ListActionTypes.keyDown,
        key: 'ArrowUp',
        context: defaultContext,
      };
      const result = listReducer(state, action);
      expect(result.highlightedValue).toBe(1); // skips disabled 2
    });

    it('handles ArrowDown key', () => {
      const state = { ...initialState, highlightedValue: 1 };
      const action: any = {
        type: ListActionTypes.keyDown,
        key: 'ArrowDown',
        context: defaultContext,
      };
      const result = listReducer(state, action);
      expect(result.highlightedValue).toBe(3); // skips disabled 2
    });

    it('handles ArrowLeft key in horizontal-ltr', () => {
      const context = { ...defaultContext, orientation: 'horizontal-ltr' };
      const state = { ...initialState, highlightedValue: 3 };
      const action: any = {
        type: ListActionTypes.keyDown,
        key: 'ArrowLeft',
        context,
      };
      const result = listReducer(state, action);
      expect(result.highlightedValue).toBe(1); // skips disabled 2
    });

    it('handles ArrowRight key in horizontal-ltr', () => {
      const context = { ...defaultContext, orientation: 'horizontal-ltr' };
      const state = { ...initialState, highlightedValue: 1 };
      const action: any = {
        type: ListActionTypes.keyDown,
        key: 'ArrowRight',
        context,
      };
      const result = listReducer(state, action);
      expect(result.highlightedValue).toBe(3); // skips disabled 2
    });

    it('handles ArrowLeft key in horizontal-rtl', () => {
      const context = { ...defaultContext, orientation: 'horizontal-rtl' };
      const state = { ...initialState, highlightedValue: 1 };
      const action: any = {
        type: ListActionTypes.keyDown,
        key: 'ArrowLeft',
        context,
      };
      const result = listReducer(state, action);
      expect(result.highlightedValue).toBe(3); // skips disabled 2
    });

    it('handles ArrowRight key in horizontal-rtl', () => {
      const context = { ...defaultContext, orientation: 'horizontal-rtl' };
      const state = { ...initialState, highlightedValue: 3 };
      const action: any = {
        type: ListActionTypes.keyDown,
        key: 'ArrowRight',
        context,
      };
      const result = listReducer(state, action);
      expect(result.highlightedValue).toBe(1); // skips disabled 2
    });

    it('handles Enter key to select highlighted item', () => {
      const state = { ...initialState, highlightedValue: 3 };
      const action: any = {
        type: ListActionTypes.keyDown,
        key: 'Enter',
        context: defaultContext,
      };
      const result = listReducer(state, action);
      expect(result.selectedValues).toEqual([3]);
    });

    it('handles Space key to select highlighted item', () => {
      const state = { ...initialState, highlightedValue: 3 };
      const action: any = {
        type: ListActionTypes.keyDown,
        key: ' ',
        context: defaultContext,
      };
      const result = listReducer(state, action);
      expect(result.selectedValues).toEqual([3]);
    });

    it('does nothing when highlightedValue is null and Enter/Space is pressed', () => {
      const state = { ...initialState, highlightedValue: null };
      const action: any = {
        type: ListActionTypes.keyDown,
        key: 'Enter',
        context: defaultContext,
      };
      const result = listReducer(state, action);
      expect(result).toEqual(state);
    });
  });

  describe('handleBlur', () => {
    it('sets highlightedValue to null when focusManagement is not DOM', () => {
      const state = { ...initialState, highlightedValue: 3 };
      const action: any = {
        type: ListActionTypes.blur,
        context: defaultContext,
      };
      const result = listReducer(state, action);
      expect(result.highlightedValue).toBeNull();
    });

    it('does not change state when focusManagement is DOM', () => {
      const state = { ...initialState, highlightedValue: 3 };
      const action: any = {
        type: ListActionTypes.blur,
        context: { ...defaultContext, focusManagement: 'DOM' },
      };
      const result = listReducer(state, action);
      expect(result).toEqual(state);
    });
  });

  describe('handleTextNavigation', () => {
    it('finds item matching text search', () => {
      const state = { ...initialState, highlightedValue: 1 };
      const action: any = {
        type: ListActionTypes.textNavigation,
        searchString: '3',
        context: defaultContext,
      };
      const result = listReducer(state, action);
      expect(result.highlightedValue).toBe(3);
    });

    it('returns unchanged state if no match found', () => {
      const state = { ...initialState, highlightedValue: 1 };
      const action: any = {
        type: ListActionTypes.textNavigation,
        searchString: 'x',
        context: defaultContext,
      };
      const result = listReducer(state, action);
      expect(result).toEqual(state);
    });

    it('skips disabled items', () => {
      const state = { ...initialState, highlightedValue: 1 };
      const action: any = {
        type: ListActionTypes.textNavigation,
        searchString: '2',
        context: defaultContext,
      };
      const result = listReducer(state, action);
      expect(result).toEqual(state); // should skip disabled item 2
    });
  });

  describe('handleItemsChange', () => {
    it('updates highlightedValue when item still exists', () => {
      const state = { ...initialState, highlightedValue: 3, selectedValues: [3] };
      const action: any = {
        type: ListActionTypes.itemsChange,
        items: [3, 4, 5],
        previousItems: [1, 2, 3, 4, 5],
        context: defaultContext,
      };
      const result = listReducer(state, action);
      expect(result.highlightedValue).toBe(3);
      expect(result.selectedValues).toEqual([3]);
    });

    it('resets highlightedValue when item no longer exists', () => {
      const state = { ...initialState, highlightedValue: 1, selectedValues: [1, 2] };
      const action: any = {
        type: ListActionTypes.itemsChange,
        items: [3, 4, 5],
        previousItems: [1, 2, 3, 4, 5],
        context: defaultContext,
      };
      const result = listReducer(state, action);
      expect(result.highlightedValue).toBeNull();
      expect(result.selectedValues).toEqual([]);
    });

    it('sets initial highlight when focusManagement is DOM and previous items were empty', () => {
      const state = { ...initialState, highlightedValue: null };
      const action: any = {
        type: ListActionTypes.itemsChange,
        items: [1, 2, 3],
        previousItems: [],
        context: { ...defaultContext, focusManagement: 'DOM' },
      };
      const result = listReducer(state, action);
      expect(result.highlightedValue).toBe(1);
    });
  });

  describe('other actions', () => {
    it('handles resetHighlight', () => {
      const state = { ...initialState, highlightedValue: 3 };
      const action: any = {
        type: ListActionTypes.resetHighlight,
        context: { ...defaultContext, focusManagement: 'DOM' },
      };
      const result = listReducer(state, action);
      expect(result.highlightedValue).toBe(1);
    });

    it('handles highlightLast', () => {
      const state = { ...initialState, highlightedValue: 1 };
      const action: any = {
        type: ListActionTypes.highlightLast,
        context: defaultContext,
      };
      const result = listReducer(state, action);
      expect(result.highlightedValue).toBe(5);
    });

    it('handles clearSelection', () => {
      const state = { ...initialState, highlightedValue: 3, selectedValues: [1, 3] };
      const action: any = {
        type: ListActionTypes.clearSelection,
        context: defaultContext,
      };
      const result = listReducer(state, action);
      expect(result.highlightedValue).toBeNull();
      expect(result.selectedValues).toEqual([]);
    });

    it('returns unchanged state for unknown action', () => {
      const state = { ...initialState, highlightedValue: 3 };
      const action: any = {
        type: 'UNKNOWN_ACTION',
        context: defaultContext,
      } as any;
      const result = listReducer(state, action);
      expect(result).toEqual(state);
    });
  });
});
