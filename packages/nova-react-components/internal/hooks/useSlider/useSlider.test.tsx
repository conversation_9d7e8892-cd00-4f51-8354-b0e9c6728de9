import * as React from 'react';
import { render } from '@testing-library/react';
import { vi, describe, it, expect } from 'vitest';
import {
  areValuesEqual,
  doesSupportTouchActionNone,
  findClosest,
  focusThumb,
  getDecimalPrecision,
  percentToValue,
  roundValueToStep,
  setValueIndex,
  trackFinger,
  useSlider,
  valueToPercent,
} from './useSlider';

const TestComponent = (props) => {
  const sliderProps = useSlider(props);
  return (
    <div>
      <input {...sliderProps.getHiddenInputProps()} />
      <div {...sliderProps.getRootProps()}>
        {sliderProps.values.map((value, index) => (
          <div key={index} {...sliderProps.getThumbProps({ 'data-index': index })}>
            Thumb {value}
          </div>
        ))}
      </div>
    </div>
  );
};

describe('utility functions', () => {
  describe('findClosest', () => {
    it('should find the closest value in an array', () => {
      const values = [10, 20, 30, 40, 50];
      expect(findClosest(values, 22)).toBe(1); // index of 20
      expect(findClosest(values, 28)).toBe(2); // index of 30
    });
  });

  describe('valueToPercent', () => {
    it('should convert value to percentage', () => {
      expect(valueToPercent(50, 0, 100)).toBe(50);
      expect(valueToPercent(25, 0, 50)).toBe(50);
    });
  });

  describe('percentToValue', () => {
    it('should convert percentage to value', () => {
      expect(percentToValue(50, 0, 100)).toBe(5000);
      expect(percentToValue(50, 0, 50)).toBe(2500);
    });
  });

  describe('roundValueToStep', () => {
    it('should round value to nearest step', () => {
      expect(roundValueToStep(23, 5, 0)).toBe(25);
      expect(roundValueToStep(22, 5, 0)).toBe(20);
    });
  });

  describe('getDecimalPrecision', () => {
    it('should return correct decimal precision', () => {
      expect(getDecimalPrecision(1.23)).toBe(2);
      expect(getDecimalPrecision(0.000123)).toBe(6);
      expect(getDecimalPrecision(123)).toBe(0);
    });
  });

  describe('doesSupportTouchActionNone', () => {
    it('should return true when CSS.supports is available', () => {
      expect(doesSupportTouchActionNone()).toBe(true);
    });

    it('should return true when CSS.supports is not available', () => {
      const originalCSS = global.CSS;
      (global as any).CSS = undefined;

      expect(doesSupportTouchActionNone()).toBe(true);

      global.CSS = originalCSS;
    });
  });

  describe('trackFinger', () => {
    it('should track touch events with identifier', () => {
      const touchId = { current: 123 };
      const touchEvent = {
        changedTouches: [
          { identifier: 123, clientX: 100, clientY: 200 },
          { identifier: 456, clientX: 300, clientY: 400 },
        ],
      } as unknown as TouchEvent;

      const result = trackFinger(touchEvent, touchId);
      expect(result).toEqual({ x: 100, y: 200 });
    });

    it('should return false if touch not found', () => {
      const touchId = { current: 999 };
      const touchEvent = {
        changedTouches: [
          { identifier: 123, clientX: 100, clientY: 200 },
          { identifier: 456, clientX: 300, clientY: 400 },
        ],
      } as unknown as TouchEvent;

      const result = trackFinger(touchEvent, touchId);
      expect(result).toBe(false);
    });

    it('should track mouse events', () => {
      const touchId = { current: undefined };
      const mouseEvent = {
        clientX: 150,
        clientY: 250,
      } as MouseEvent;

      const result = trackFinger(mouseEvent, touchId);
      expect(result).toEqual({ x: 150, y: 250 });
    });
  });

  describe('setValueIndex', () => {
    it('should update value at specified index and sort', () => {
      const values = [10, 20, 30];
      const result = setValueIndex({
        values,
        newValue: 25,
        index: 1,
      });
      expect(result).toEqual([10, 25, 30]);
    });

    it('should maintain order when updating first value', () => {
      const values = [10, 20, 30];
      const result = setValueIndex({
        values,
        newValue: 5,
        index: 0,
      });
      expect(result).toEqual([5, 20, 30]);
    });

    it('should maintain order when updating last value', () => {
      const values = [10, 20, 30];
      const result = setValueIndex({
        values,
        newValue: 35,
        index: 2,
      });
      expect(result).toEqual([10, 20, 35]);
    });
  });

  describe('focusThumb', () => {
    it('should focus thumb at active index', () => {
      const mockFocus = vi.fn();
      const mockSlider = {
        contains: vi.fn().mockReturnValue(false),
        querySelector: vi.fn().mockReturnValue({
          focus: mockFocus,
        }),
      } as unknown as HTMLElement;

      const sliderRef = { current: mockSlider };
      focusThumb({ sliderRef, activeIndex: 0 });

      expect(mockSlider.querySelector).toHaveBeenCalledWith('[type="range"][data-index="0"]');
      expect(mockFocus).toHaveBeenCalled();
    });

    it('should not focus if already focused on correct thumb', () => {
      const mockFocus = vi.fn();
      const mockInput = {
        focus: mockFocus,
        getAttribute: vi.fn().mockReturnValue('0'),
      };
      const mockSlider = {
        contains: vi.fn().mockReturnValue(true),
        ownerDocument: {
          activeElement: mockInput,
        },
        querySelector: vi.fn(),
      } as unknown as HTMLElement;

      const sliderRef = { current: mockSlider };
      focusThumb({ sliderRef, activeIndex: 0 });

      expect(mockFocus).not.toHaveBeenCalled();
    });

    it('should call setActive when provided', () => {
      const setActive = vi.fn();
      const mockSlider = {
        contains: vi.fn().mockReturnValue(false),
        querySelector: vi.fn().mockReturnValue({
          focus: vi.fn(),
        }),
      } as unknown as HTMLElement;

      const sliderRef = { current: mockSlider };
      focusThumb({ sliderRef, activeIndex: 1, setActive });

      expect(setActive).toHaveBeenCalledWith(1);
    });
  });

  describe('areValuesEqual', () => {
    it('should return true for equal numbers', () => {
      expect(areValuesEqual(10, 10)).toBe(true);
    });

    it('should return false for different numbers', () => {
      expect(areValuesEqual(10, 20)).toBe(false);
    });

    it('should return true for equal arrays', () => {
      expect(areValuesEqual([10, 20], [10, 20])).toBe(true);
    });

    it('should return false for different arrays', () => {
      expect(areValuesEqual([10, 20], [10, 30])).toBe(false);
    });

    it('should return false for mixed types', () => {
      expect(areValuesEqual(10, [10])).toBe(false);
    });
  });
});

describe('useSlider', () => {
  it('does not allow values outside of min/max', () => {
    const { container } = render(<TestComponent defaultValue={150} min={0} max={100} />);
    expect(container).toHaveTextContent('Thumb 100');
  });

  it('supports RTL layout', () => {
    const { getByRole } = render(<TestComponent defaultValue={50} min={0} max={100} isRtl />);
    const input = getByRole('slider');
    expect(input).toHaveAttribute('aria-orientation', 'horizontal');
  });

  it('handles marks correctly', () => {
    const marks = [{ value: 0 }, { value: 50 }, { value: 100 }];
    const { getByText } = render(<TestComponent defaultValue={50} min={0} max={100} marks={marks} />);
    expect(getByText('Thumb 50')).toBeInTheDocument();
  });
});
