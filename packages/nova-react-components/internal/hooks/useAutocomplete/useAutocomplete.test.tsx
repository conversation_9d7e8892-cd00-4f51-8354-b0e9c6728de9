import * as React from 'react';
import { render, screen, fireEvent, act } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { useAutocomplete } from './useAutocomplete';

// Test component to render the hook
const AutocompleteTestComponent = (props: any) => {
  const { getRootProps, getInputProps, getListboxProps, getOptionProps, groupedOptions, popupOpen } =
    useAutocomplete(props);

  return (
    <div {...getRootProps()}>
      <input {...getInputProps()} />
      {popupOpen && (
        <ul {...getListboxProps()}>
          {groupedOptions.map((option: any, index: number) => (
            // eslint-disable-next-line react/jsx-key
            <li {...getOptionProps({ option, index })}>{props.getOptionLabel(option)}</li>
          ))}
        </ul>
      )}
    </div>
  );
};

describe('useAutocomplete', () => {
  const options = [
    { label: 'Option 1', value: 1 },
    { label: 'Option 2', value: 2 },
    { label: 'Option 3', value: 3 },
  ];

  const defaultProps = {
    options,
    getOptionLabel: (option: any) => option.label,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should initialize with default values', () => {
    render(<AutocompleteTestComponent {...defaultProps} />);
    const input = screen.getByRole('combobox');
    expect(input).toHaveValue('');
  });

  it('should open the popup when input is focused', () => {
    render(<AutocompleteTestComponent {...defaultProps} openOnFocus />);
    const input = screen.getByRole('combobox');
    fireEvent.focus(input);
    expect(screen.getByRole('listbox')).toBeInTheDocument();
  });

  it('should filter options based on input value', () => {
    render(<AutocompleteTestComponent {...defaultProps} />);
    const input = screen.getByRole('combobox');

    fireEvent.focus(input);
    fireEvent.change(input, { target: { value: 'Option 1' } });

    const listbox = screen.getByRole('listbox');
    expect(listbox.children).toHaveLength(1);
    expect(listbox.children[0]).toHaveTextContent('Option 1');
  });

  it('should select an option when clicked', () => {
    const handleChange = vi.fn();
    render(<AutocompleteTestComponent {...defaultProps} onChange={handleChange} />);
    const input = screen.getByRole('combobox');

    fireEvent.focus(input);
    fireEvent.keyDown(input, { key: 'ArrowDown' });
    fireEvent.click(screen.getByText('Option 1'));

    expect(handleChange).toHaveBeenCalled();
  });

  it('should handle multiple selection', () => {
    const handleChange = vi.fn();
    render(<AutocompleteTestComponent {...defaultProps} multiple onChange={handleChange} />);
    const input = screen.getByRole('combobox');

    fireEvent.focus(input);
    fireEvent.keyDown(input, { key: 'ArrowDown' });
    fireEvent.click(screen.getByText('Option 1'));
    fireEvent.keyDown(input, { key: 'ArrowDown' });
    fireEvent.click(screen.getByText('Option 2'));

    expect(handleChange).toHaveBeenCalledTimes(2);
    expect(handleChange.mock.calls[1][1]).toEqual([
      { label: 'Option 1', value: 1 },
      { label: 'Option 2', value: 2 },
    ]);
  });

  it('should handle freeSolo input', () => {
    const handleChange = vi.fn();
    render(<AutocompleteTestComponent {...defaultProps} freeSolo onChange={handleChange} />);
    const input = screen.getByRole('combobox');

    fireEvent.change(input, { target: { value: 'Custom Option' } });
    fireEvent.keyDown(input, { key: 'Enter' });

    expect(handleChange).toHaveBeenCalled();
  });

  it('should filter selected options when filterSelectedOptions is true', () => {
    render(<AutocompleteTestComponent {...defaultProps} value={options[0]} filterSelectedOptions />);
    const input = screen.getByRole('combobox');

    fireEvent.focus(input);
    fireEvent.keyDown(input, { key: 'ArrowDown' });

    const listbox = screen.getByRole('listbox');
    expect(listbox.children).toHaveLength(2); // Should exclude the selected option
  });

  it('should handle disabled options', () => {
    const optionsWithDisabled = [...options, { label: 'Disabled Option', value: 4, disabled: true }];

    render(
      <AutocompleteTestComponent
        options={optionsWithDisabled}
        getOptionLabel={(option: any) => option.label}
        getOptionDisabled={(option: any) => option.disabled}
      />,
    );
    const input = screen.getByRole('combobox');

    fireEvent.focus(input);
    fireEvent.keyDown(input, { key: 'ArrowDown' });

    const disabledOption = screen.getByText('Disabled Option');
    expect(disabledOption).toHaveAttribute('aria-disabled', 'true');
  });

  it('should handle grouped options', () => {
    const groupedOptions = [
      { group: 'Group 1', options: [options[0]] },
      { group: 'Group 2', options: [options[1], options[2]] },
    ];

    const { container } = render(
      <AutocompleteTestComponent
        options={groupedOptions}
        getOptionLabel={(option: any) => option.label}
        groupBy={(option: any) => option.group}
      />,
    );
    const input = screen.getByRole('combobox');

    fireEvent.focus(input);
    fireEvent.keyDown(input, { key: 'ArrowDown' });

    expect(container.querySelectorAll('li')).toHaveLength(2);
  });

  it('should handle input change', () => {
    const handleInputChange = vi.fn();
    render(<AutocompleteTestComponent {...defaultProps} onInputChange={handleInputChange} />);
    const input = screen.getByRole('combobox');

    fireEvent.change(input, { target: { value: 'test' } });

    expect(handleInputChange).toHaveBeenCalledWith(expect.anything(), 'test', 'input');
  });

  it('should handle controlled inputValue', () => {
    render(<AutocompleteTestComponent {...defaultProps} inputValue="controlled" />);
    const input = screen.getByRole('combobox');
    expect(input).toHaveValue('controlled');
  });

  it('should handle controlled open state', () => {
    render(<AutocompleteTestComponent {...defaultProps} open />);
    expect(screen.getByRole('listbox')).toBeInTheDocument();
  });

  it('should handle disabled state', () => {
    render(<AutocompleteTestComponent {...defaultProps} disabled />);
    const input = screen.getByRole('combobox');
    expect(input).toBeDisabled();
  });

  it('should handle custom filterOptions', () => {
    const customFilter = vi
      .fn()
      .mockImplementation((options, state) => options.filter((option) => option.label.includes(state.inputValue)));

    render(<AutocompleteTestComponent {...defaultProps} filterOptions={customFilter} />);
    const input = screen.getByRole('combobox');

    fireEvent.focus(input);
    fireEvent.keyDown(input, { key: 'ArrowDown' });
    fireEvent.change(input, { target: { value: 'Option 1' } });

    expect(customFilter).toHaveBeenCalled();
    expect(screen.getByRole('listbox').children).toHaveLength(1);
  });

  it('should handle getOptionDisabled', () => {
    const getOptionDisabled = vi.fn().mockImplementation((option) => option.value === 1);

    render(<AutocompleteTestComponent {...defaultProps} getOptionDisabled={getOptionDisabled} />);
    const input = screen.getByRole('combobox');

    fireEvent.focus(input);
    fireEvent.keyDown(input, { key: 'ArrowDown' });

    expect(getOptionDisabled).toHaveBeenCalled();
    expect(screen.getByText('Option 1')).toHaveAttribute('aria-disabled', 'true');
  });

  it('should handle includeInputInList', () => {
    render(<AutocompleteTestComponent {...defaultProps} includeInputInList freeSolo />);
    const input = screen.getByRole('combobox');

    fireEvent.focus(input);
    fireEvent.keyDown(input, { key: 'ArrowDown' });

    // Should include input in the navigation
    fireEvent.keyDown(input, { key: 'ArrowUp' });
  });

  it('should handle onClose and onOpen events', () => {
    const handleOpen = vi.fn();
    const handleClose = vi.fn();

    render(<AutocompleteTestComponent {...defaultProps} onOpen={handleOpen} onClose={handleClose} />);
    const input = screen.getByRole('combobox');

    fireEvent.focus(input);
    fireEvent.keyDown(input, { key: 'ArrowDown' });
    expect(handleOpen).toHaveBeenCalled();

    fireEvent.blur(input);
    expect(handleClose).toHaveBeenCalled();
  });
});
