import * as React from 'react';
import { describe, expect, it, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { FocusTrap } from './FocusTrap';
import { getTabIndex, isNonTabbableRadio, isNodeMatchingSelectorFocusable, defaultGetTabbable } from './FocusTrap';

describe('FocusTrap utilities', () => {
  describe('getTabIndex', () => {
    it('returns tabindex attribute value when set', () => {
      const node = document.createElement('div');
      node.setAttribute('tabindex', '5');
      expect(getTabIndex(node)).toBe(5);
    });

    it('returns 0 for contentEditable elements without tabindex', () => {
      const node = document.createElement('div');
      node.contentEditable = 'true';
      expect(getTabIndex(node)).toBe(0);
    });

    it('returns 0 for audio/video/details elements without tabindex', () => {
      const audio = document.createElement('audio');
      audio.setAttribute('controls', 'true');
      expect(getTabIndex(audio)).toBe(0);

      const video = document.createElement('video');
      video.setAttribute('controls', 'true');
      expect(getTabIndex(video)).toBe(0);

      const details = document.createElement('details');
      expect(getTabIndex(details)).toBe(0);
    });

    it('returns element.tabIndex when no special cases apply', () => {
      const node = document.createElement('div');
      node.tabIndex = 3;
      expect(getTabIndex(node)).toBe(3);
    });
  });

  describe('isNonTabbableRadio', () => {
    it('returns false for non-radio elements', () => {
      const input = document.createElement('input');
      input.type = 'text';
      expect(isNonTabbableRadio(input)).toBe(false);
    });

    it('returns false for radio buttons without name', () => {
      const radio = document.createElement('input');
      radio.type = 'radio';
      expect(isNonTabbableRadio(radio)).toBe(false);
    });

    it('returns false for checked radio in group', () => {
      document.body.innerHTML = `
        <input type="radio" name="test" checked>
        <input type="radio" name="test">
      `;
      const checkedRadio = document.querySelector('input[type="radio"][checked]') as HTMLInputElement;
      expect(isNonTabbableRadio(checkedRadio)).toBe(false);
    });

    it('returns true for non-checked radio in group', () => {
      document.body.innerHTML = `
        <input type="radio" name="test" checked>
        <input type="radio" name="test">
      `;
      const uncheckedRadio = document.querySelectorAll('input[type="radio"]')[1] as HTMLInputElement;
      expect(isNonTabbableRadio(uncheckedRadio)).toBe(true);
    });
  });

  describe('isNodeMatchingSelectorFocusable', () => {
    it('returns false for disabled elements', () => {
      const input = document.createElement('input');
      input.disabled = true;
      expect(isNodeMatchingSelectorFocusable(input)).toBe(false);
    });

    it('returns false for hidden inputs', () => {
      const input = document.createElement('input');
      input.type = 'hidden';
      expect(isNodeMatchingSelectorFocusable(input)).toBe(false);
    });

    it('returns false for non-tabbable radio buttons', () => {
      document.body.innerHTML = '<input type="radio" name="test" checked><input type="radio" name="test">';
      const uncheckedRadio = document.querySelectorAll('input[type="radio"]')[1] as HTMLInputElement;
      expect(isNodeMatchingSelectorFocusable(uncheckedRadio)).toBe(false);
    });

    it('returns true for focusable elements', () => {
      const button = document.createElement('button') as HTMLInputElement;
      expect(isNodeMatchingSelectorFocusable(button)).toBe(true);
    });
  });

  describe('defaultGetTabbable', () => {
    beforeEach(() => {
      document.body.innerHTML = `
        <div id="root">
          <button>Button 1</button>
          <input tabindex="2">
          <div tabindex="1"></div>
          <input type="hidden">
          <button disabled>Disabled Button</button>
          <input type="radio" name="radio" checked>
          <input type="radio" name="radio">
        </div>
      `;
    });

    it('returns tabbable elements in correct order', () => {
      const root = document.getElementById('root') as HTMLElement;
      const tabbable = defaultGetTabbable(root);
      expect(tabbable.length).toBe(4);
      expect(tabbable[0].getAttribute('tabindex')).toBe('1');
      expect(tabbable[1].getAttribute('tabindex')).toBe('2');
      expect(tabbable[2].tagName).toBe('BUTTON');
    });

    it('excludes non-tabbable elements', () => {
      const root = document.getElementById('root') as HTMLElement;
      const tabbable = defaultGetTabbable(root);
      expect(tabbable.some((el: any) => el.disabled)).toBe(false);
      expect(tabbable.some((el) => el.getAttribute('type') === 'hidden')).toBe(false);
      expect(tabbable.some((el: any) => el.getAttribute('type') === 'radio' && !el.checked)).toBe(false);
    });
  });
});

describe('FocusTrap component', () => {
  const TestChild = React.forwardRef<HTMLDivElement>((props, ref) => (
    <div data-testid="childCom" ref={ref} {...props}>
      <button data-testid="button1">Button 1</button>
      <button data-testid="button2">Button 2</button>
    </div>
  ));

  it('renders children and sentinels', () => {
    render(
      <FocusTrap open>
        <TestChild />
      </FocusTrap>,
    );

    expect(screen.getByTestId('sentinelStart')).toBeInTheDocument();
    expect(screen.getByTestId('sentinelEnd')).toBeInTheDocument();
    expect(screen.getByTestId('childCom')).toBeInTheDocument();
    expect(screen.getByTestId('button1')).toBeInTheDocument();
    expect(screen.getByTestId('button2')).toBeInTheDocument();
  });

  it('does not activate when closed', () => {
    render(
      <FocusTrap open={false}>
        <TestChild />
      </FocusTrap>,
    );
    expect(screen.getByTestId('sentinelStart')).toHaveAttribute('tabindex', '-1');
    expect(screen.getByTestId('sentinelEnd')).toHaveAttribute('tabindex', '-1');
  });

  it('restores focus when closed', () => {
    const buttonOutside = document.createElement('button');
    document.body.appendChild(buttonOutside);
    buttonOutside.focus();

    const { unmount } = render(
      <FocusTrap open>
        <TestChild />
      </FocusTrap>,
    );

    unmount();
    expect(buttonOutside).toHaveFocus();
    document.body.removeChild(buttonOutside);
  });

  it('does not restore focus when disableRestoreFocus is true', () => {
    const buttonOutside = document.createElement('button');
    document.body.appendChild(buttonOutside);
    buttonOutside.focus();

    const { unmount } = render(
      <FocusTrap open disableRestoreFocus>
        <TestChild />
      </FocusTrap>,
    );

    unmount();
    expect(buttonOutside).not.toHaveFocus();
    document.body.removeChild(buttonOutside);
  });

  it('does not enforce focus when disableEnforceFocus is true', () => {
    render(
      <FocusTrap open disableEnforceFocus>
        <TestChild />
      </FocusTrap>,
    );

    const buttonOutside = document.createElement('button');
    document.body.appendChild(buttonOutside);
    buttonOutside.focus();

    expect(buttonOutside).toHaveFocus();
    document.body.removeChild(buttonOutside);
  });

  it('handles custom getTabbable function', () => {
    const customGetTabbable = (root: HTMLElement) =>
      Array.from(root.querySelectorAll('button')).filter((btn) => !btn.disabled) as any;

    render(
      <FocusTrap open getTabbable={customGetTabbable}>
        <TestChild />
      </FocusTrap>,
    );

    const childCom = screen.getByTestId('childCom');
    expect(childCom).toHaveFocus();
  });

  it('handles custom isEnabled function', () => {
    const isEnabled = vi.fn().mockReturnValue(false);

    render(
      <FocusTrap open isEnabled={isEnabled}>
        <TestChild />
      </FocusTrap>,
    );

    const buttonOutside = document.createElement('button');
    document.body.appendChild(buttonOutside);
    buttonOutside.focus();

    expect(buttonOutside).toHaveFocus();
    document.body.removeChild(buttonOutside);
  });

  it('sets tabIndex on root when it has none', () => {
    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    render(
      <FocusTrap open>
        <div data-testid="root">
          <button>Button</button>
        </div>
      </FocusTrap>,
    );

    const root = screen.getByTestId('root');
    expect(root).toHaveAttribute('tabindex', '-1');
    expect(consoleErrorSpy).toHaveBeenCalled();

    consoleErrorSpy.mockRestore();
  });
});
