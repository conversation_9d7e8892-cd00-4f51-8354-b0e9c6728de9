{"name": "vite-demo", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "dependencies": {"@fontsource/roboto": "^5.0.12", "@fontsource/roboto-mono": "^5.0.17", "@hxnova/react-components": "workspace:*", "@hxnova/themes": "workspace:*", "@pigment-css/react": "~0.0.30", "@nexusui/branding": "^2.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "@hxnova/icons": "workspace:^", "dayjs": "^1.10.7"}, "devDependencies": {"@babel/plugin-transform-export-namespace-from": "^7.25.9", "@pigment-css/vite-plugin": "~0.0.30", "@types/node": "^22.10.2", "@types/react": "^18.2.67", "@types/react-dom": "^18.2.22", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "@vitejs/plugin-react": "^4.2.1", "react-is": "^18.2.0", "prop-types": "^15.8.1", "typescript": "^5.4.4", "vite": "^6.0.3"}}