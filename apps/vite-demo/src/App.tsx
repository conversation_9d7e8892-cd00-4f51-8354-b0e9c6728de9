import * as React from 'react';
import AvatarKitchen from './components/AvatarKitchen';
import BadgeKitchen from './components/BadgeKitchen';
import SwitchKitchen from './components/SwitchKitchen';
import RadioKitchen from './components/RadioKitchen';
import ButtonKitchen from './components/ButtonKitchen';
import CardKitchen from './components/CardKitchen';
import CheckboxKitchen from './components/CheckboxKitchen';
import DataGridKitchen from './components/DataGridKitchen';
import DropdownKitchen from './components/DropdownKitchen';
import IconButtonKitchen from './components/IconButtonKitchen';
import ToggleIconButtonKitchen from './components/ToggleIconButtonKitchen';
import FabKitchen from './components/FabKitchen';
import SegmentedButtonGroupKitchen from './components/SegmentedButtonKitchen';
import TypographyKitchen from './components/TypographyKitchen';
import LinkKitchen from './components/LinkKitchen';
import MenuKitchen from './components/MenuKitchen';
import ListKitchen from './components/ListKitchen';
import TabsKitchen from './components/TabsKitchen';
import ChipKitchen from './components/ChipKitchen';
import TooltipKitchen from './components/TooltipKitchen';
import TextFieldKitchen from './components/TextFieldKitchen';
import DialogKitchen from './components/DialogKitchen';
import AlertKitchen from './components/AlertKitchen';
import NavigationTopKitchen from './components/NavigationTopKitchen';
import SliderKitchen from './components/SliderKitchen';
import SnackbarKitchen from './components/SnackbarKitchen';
import BreadcrumbsKitchen from './components/BreadcrumbsKitchen';
import DividerKitchen from './components/DividerKitchen';
import AccordionKitchen from './components/AccordionKitchen';
import DrawerKitchen from './components/DrawerKitchen';
import ProgressIndicatorsKitchen from './components/ProgressIndicatorsKitchen';
import { ColorSchemeProvider, useColorScheme } from './components/ColorSchemeProvider';
import { IconButton } from '@hxnova/react-components/IconButton';
import { Drawer } from '@hxnova/react-components';
import TagKitchen from './components/TagKitchen';
import PaginationKitchen from './components/PaginationKitchen';
import IconKitchen from './components/IconKitchen';
import SearchKitchen from './components/SearchKitchen';
import StepperKitchen from './components/StepperKitchen';
import SideSheetKitchen from './components/SideSheetKitchen';
import FloatingActionBarKitchen from './components/FloatingActionBarKitchen';
import AutocompleteKitchen from './components/AutocompleteKitchen';
import PopoverKitchen from './components/PopoverKitchen';
import SkeletonKitchen from './components/SkeletonKitchen';
import DatePickersKitchen from './components/DatePickersKitchen';
import { Icon } from '@hxnova/icons';
import { NovaProvider } from '@hxnova/react-components/NovaProvider';
import { Button } from '@hxnova/react-components/Button';
import { Menu } from '@hxnova/react-components/Menu';
import { MenuItem } from '@hxnova/react-components/MenuItem';
import enUS from '@hxnova/react-components/Locale/en_US';
import zhCN from '@hxnova/react-components/Locale/zh_CN';
import itIT from '@hxnova/react-components/Locale/it_IT';
import frFR from '@hxnova/react-components/Locale/fr_FR';
import deDE from '@hxnova/react-components/Locale/de_DE';
import { Locale } from '@hxnova/react-components/Locale';

const locales = [
  { value: 'en-US', right: '🇺🇸', title: 'English (US)', locale: enUS },
  { value: 'fr-FR', right: '🇫🇷', title: 'Français', locale: frFR },
  { value: 'de-DE', right: '🇩🇪', title: 'Deutsch', locale: deDE },
  { value: 'it-IT', right: '🇮🇹', title: 'Italiano', locale: itIT },
  { value: 'zh-CN', right: '🇨🇳​', title: '中文简体', locale: zhCN },
];

const kitchenComponents = {
  TypographyKitchen,
  ButtonKitchen,
  IconButtonKitchen,
  ToggleIconButtonKitchen,
  FabKitchen,
  SegmentedButtonGroupKitchen,
  BadgeKitchen,
  AvatarKitchen,
  SwitchKitchen,
  CardKitchen,
  CheckboxKitchen,
  DividerKitchen,
  DataGridKitchen,
  DropdownKitchen,
  LinkKitchen,
  RadioKitchen,
  MenuKitchen,
  ListKitchen,
  TabsKitchen,
  ChipKitchen,
  TooltipKitchen,
  TextFieldKitchen,
  DialogKitchen,
  AlertKitchen,
  NavigationTopKitchen,
  SliderKitchen,
  SnackbarKitchen,
  BreadcrumbsKitchen,
  AccordionKitchen,
  DrawerKitchen,
  TagKitchen,
  ProgressIndicatorsKitchen,
  PaginationKitchen,
  IconKitchen,
  SearchKitchen,
  StepperKitchen,
  SideSheetKitchen,
  FloatingActionBarKitchen,
  AutocompleteKitchen,
  PopoverKitchen,
  SkeletonKitchen,
  DatePickersKitchen,
};

function ColorSchemeToggleButton() {
  const { colorScheme, setColorScheme } = useColorScheme();

  const toggleColorScheme = () => {
    setColorScheme(colorScheme === 'dark' ? 'light' : 'dark');
  };

  return (
    <div sx={{ position: 'absolute', top: 20, right: 20 }}>
      <IconButton variant="standard" sx={{ fontSize: 20 }} onClick={toggleColorScheme}>
        {colorScheme === 'light' ? '🌙' : '🔆'}
      </IconButton>
    </div>
  );
}

function LocaleToggleButton({ onLocaleToggle }: { onLocaleToggle: (locale: Locale) => void }) {
  const [title, setTitle] = React.useState('English (US)');
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <div>
      <Button
        variant="text"
        id="basic-button"
        aria-controls={open ? 'basic-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        onClick={handleClick}
        sx={{ position: 'absolute', top: 20, right: 80 }}
        startIcon={<Icon family="material" name="language" />}
      >
        {title}
      </Button>
      <Menu id="basic-menu" anchorEl={anchorEl} open={open} onClose={handleClose}>
        {locales.map((i) => (
          <MenuItem
            key={i.value}
            onClick={() => {
              setTitle(i.title);
              onLocaleToggle(i.locale);
              handleClose();
            }}
          >
            {i.title}
          </MenuItem>
        ))}
      </Menu>
    </div>
  );
}

function Home() {
  const [locale, setLocale] = React.useState(enUS);
  const [selectedKitchen, setSelectedKitchen] = React.useState(() => {
    const saved = localStorage.getItem('selectedKitchen');
    // Verify the saved component exists, otherwise default to a known valid component
    return saved && saved in kitchenComponents ? saved : 'AccordionKitchen';
  });

  const handleKitchenClick = (kitchen: string) => {
    setSelectedKitchen(kitchen);
    localStorage.setItem('selectedKitchen', kitchen);
  };

  const CurrentKitchen = kitchenComponents[selectedKitchen as keyof typeof kitchenComponents];
  if (!CurrentKitchen) {
    console.error(`Kitchen component "${selectedKitchen}" not found`);
    return null;
  }

  return (
    <NovaProvider locale={locale}>
      <div sx={{ display: 'flex' }}>
        <Drawer.Root variant="permanent" width={300} sx={{ width: 300 }}>
          <Drawer.Body>
            <Drawer.NavGroup>
              {Object.keys(kitchenComponents)
                .sort()
                .map((text, index) => (
                  <Drawer.NavItem
                    key={index}
                    onClick={() => handleKitchenClick(text)}
                    selected={selectedKitchen === text}
                    label={text.replace('Kitchen', '')}
                    startDecorator={
                      index % 2 === 0 ? <Icon family="material" name="inbox" /> : <Icon family="material" name="mail" />
                    }
                  />
                ))}
            </Drawer.NavGroup>
          </Drawer.Body>
        </Drawer.Root>
        <LocaleToggleButton
          onLocaleToggle={(lo) => {
            setLocale(lo);
          }}
        />
        <ColorSchemeToggleButton />
        <div
          sx={(theme) => ({
            flexGrow: 1,
            backgroundColor: theme.vars.palette.surfaceContainer,
            padding: '32px',
            justifyContent: 'center',
            alignItems: 'center',
            display: 'flex',
            width: '100%',
            minHeight: '100vh',
          })}
        >
          {CurrentKitchen ? <CurrentKitchen /> : null}
        </div>
      </div>
    </NovaProvider>
  );
}

const defaultColorScheme = localStorage.getItem('colorScheme') ?? 'light';

export default function App() {
  return (
    <ColorSchemeProvider colorScheme={defaultColorScheme}>
      <Home />
    </ColorSchemeProvider>
  );
}
