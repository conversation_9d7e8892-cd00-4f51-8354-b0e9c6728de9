import { Avatar, AvatarProps } from '@hxnova/react-components/Avatar';
import { AvatarGroup } from '@hxnova/react-components/AvatarGroup';
import { Icon } from '@hxnova/icons';
import avatarSrc from '../assets/avatar.jpeg';

export default function AvatarKitchen() {
  const sizes = ['small', 'medium', 'large'];
  const avatarTypes = ['image', 'icon', 'text'];
  const colors = ['error', 'primary', 'warning', 'info', 'success'];
  const iconSize = { small: 20, medium: 24, large: 28 };
  return (
    <div>
      <div sx={{ marginLeft: '30px' }}>
        {sizes.map((size) => (
          <div key={size} sx={{ display: 'flex', flexDirection: 'column' }}>
            {avatarTypes.map((type) => (
              <div key={`${size}-${type}`} sx={{ display: 'flex', flexDirection: 'row' }}>
                {colors.map((color) => (
                  <div
                    key={`${size}-${type}-color:${color}`}
                    sx={{ position: 'relative', height: '100px', width: '220px', gap: '16px' }}
                  >
                    <Avatar
                      src={type === 'image' ? avatarSrc : undefined}
                      size={size as AvatarProps['size']}
                      color={color as AvatarProps['color']}
                    >
                      {type === 'text' && 'JB'}
                      {type === 'icon' && (
                        <Icon
                          family="material"
                          name={'person'}
                          size={iconSize[(size as AvatarProps['size']) ?? 'medium']}
                        />
                      )}
                    </Avatar>

                    <div
                      sx={{
                        position: 'absolute',
                        top: '50px',
                        fontSize: '14px',
                        left: '-30px',
                        whiteSpace: 'pre-wrap',
                      }}
                    >
                      {`${size}-${type}\ncolor:${color}-status:${color}`}
                    </div>
                  </div>
                ))}
                <div
                  key={`${size}-${type}-disabled`}
                  sx={{ position: 'relative', height: '100px', width: '220px', gap: '16px' }}
                >
                  <Avatar src={type === 'image' ? avatarSrc : undefined} color={'primary'} disabled>
                    {type === 'text' && 'JB'}
                    {type === 'icon' && (
                      <Icon
                        family="material"
                        name={'person'}
                        size={iconSize[(size as AvatarProps['size']) ?? 'medium']}
                      />
                    )}
                  </Avatar>
                  <div
                    sx={{
                      position: 'absolute',
                      top: '50px',
                      fontSize: '14px',
                      whiteSpace: 'pre-wrap',
                    }}
                  >
                    {`${size}-${type}\ndisabled`}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ))}
      </div>

      <div sx={{ marginTop: '30px' }}>
        {sizes.map((size) => (
          <div key={size} sx={{ display: 'flex', flexDirection: 'row' }}>
            {avatarTypes.map((type) => (
              <div
                key={`${size}-${type}`}
                sx={{ display: 'flex', flexDirection: 'column', height: '180px', width: '220px', gap: '16px' }}
              >
                <div sx={{ display: 'flex', flexDirection: 'column' }}>
                  <AvatarGroup>
                    {Array.from(new Array(3).keys()).map((i) => (
                      <Avatar
                        key={i}
                        src={type === 'image' ? avatarSrc : undefined}
                        size={size as AvatarProps['size']}
                        color="error"
                      >
                        {type === 'text' && 'JB'}
                        {type === 'icon' && (
                          <Icon
                            family="material"
                            name={'person'}
                            size={iconSize[(size as AvatarProps['size']) ?? 'medium']}
                          />
                        )}
                      </Avatar>
                    ))}
                  </AvatarGroup>
                  <div
                    sx={{
                      fontSize: '14px',
                    }}
                  >{`${size}-${type}-color:error`}</div>
                </div>

                <div sx={{ display: 'flex', flexDirection: 'column' }}>
                  <AvatarGroup disabled>
                    {Array.from(new Array(3).keys()).map((i) => (
                      <Avatar
                        key={i}
                        src={type === 'image' ? avatarSrc : undefined}
                        size={size as AvatarProps['size']}
                        color="error"
                      >
                        {type === 'text' && 'JB'}
                        {type === 'icon' && (
                          <Icon
                            family="material"
                            name={'person'}
                            size={iconSize[(size as AvatarProps['size']) ?? 'medium']}
                          />
                        )}
                      </Avatar>
                    ))}
                  </AvatarGroup>
                  <div
                    sx={{
                      fontSize: '14px',
                      marginTop: '16px',
                      whiteSpace: 'pre-wrap',
                    }}
                  >{`${size}-${type}\ncolor:error-status:error-disabled`}</div>
                </div>
              </div>
            ))}
          </div>
        ))}
      </div>
    </div>
  );
}
