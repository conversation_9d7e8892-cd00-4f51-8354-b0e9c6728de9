'use client';
import * as React from 'react';
import {
  DatePicker,
  DockedDatePicker,
  ModalDatePicker,
  DateRangePicker,
  DockedDateRangePicker,
  ModalDateRangePicker,
  PickerProvider,
} from '@hxnova/react-components/DatePickers';
import dayjs from 'dayjs/esm';

const headingStyle = {
  fontWeight: 600,
  marginBottom: '16px',
};

const subtitleStyle = {
  fontWeight: 500,
  marginBottom: '8px',
  fontSize: '16px',
};

const sectionStyle = {
  marginBottom: '48px',
};

const exampleContainerStyle = {
  display: 'flex',
  flexDirection: 'column',
  gap: '24px',
  marginBottom: '32px',
};

const rowStyle = {
  display: 'flex',
  flexDirection: 'column',
  gap: '32px',
  flexWrap: 'wrap',
};

export default function DatePickersKitchen() {
  return (
    <PickerProvider>
      <div sx={{ height: '100%', width: '100%' }}>
        <div sx={{ padding: '32px' }}>
          <div sx={{ fontSize: '24px', ...headingStyle }}>DatePicker Components</div>

          {/* Basic DatePickers */}
          <div sx={sectionStyle}>
            <div sx={{ fontSize: '20px', ...headingStyle }}>Basic DatePickers</div>

            <div sx={exampleContainerStyle}>
              <div sx={rowStyle}>
                <div>
                  <div sx={subtitleStyle}>Responsive DatePicker</div>

                  <DatePicker format="DD/MM/YYYY" defaultValue={dayjs('2022-04-17')} />

                  <div sx={{ fontSize: '12px', color: 'text.secondary', marginTop: '4px' }}>
                    Adapts to screen size automatically
                  </div>
                </div>

                <div>
                  <div sx={subtitleStyle}>Docked DatePicker</div>

                  <DockedDatePicker label="Desktop Date" format="DD/MM/YYYY" />

                  <div sx={{ fontSize: '12px', color: 'text.secondary', marginTop: '4px' }}>
                    Always opens in popover
                  </div>
                </div>

                <div>
                  <div sx={subtitleStyle}>Modal DatePicker</div>

                  <ModalDatePicker label="Mobile Date" format="DD/MM/YYYY" />

                  <div sx={{ fontSize: '12px', color: 'text.secondary', marginTop: '4px' }}>Always opens in modal</div>
                </div>
              </div>
            </div>
          </div>

          {/* Date Range Pickers */}
          <div sx={sectionStyle}>
            <div sx={{ fontSize: '20px', ...headingStyle }}>Date Range Pickers</div>

            <div sx={exampleContainerStyle}>
              <div sx={rowStyle}>
                <div>
                  <div sx={subtitleStyle}>Responsive DateRangePicker</div>

                  <DateRangePicker format="DD/MM/YYYY" defaultValue={[dayjs('2020-04-17'), dayjs('2025-04-17')]} />

                  <div sx={{ fontSize: '12px', color: 'text.secondary', marginTop: '4px' }}>
                    Dual calendar range selection
                  </div>
                </div>

                <div>
                  <div sx={subtitleStyle}>Docked DateRangePicker</div>

                  <DockedDateRangePicker format="DD/MM/YYYY" calendars={2} />

                  <div sx={{ fontSize: '12px', color: 'text.secondary', marginTop: '4px' }}>Desktop range picker</div>
                </div>

                <div>
                  <div sx={subtitleStyle}>Modal DateRangePicker</div>

                  <ModalDateRangePicker format="DD/MM/YYYY" calendars={1} />

                  <div sx={{ fontSize: '12px', color: 'text.secondary', marginTop: '4px' }}>Mobile range picker</div>
                </div>
              </div>
            </div>
          </div>

          {/* Validation Examples */}
          <div sx={sectionStyle}>
            <div sx={{ fontSize: '20px', ...headingStyle }}>Validation & States</div>

            <div sx={exampleContainerStyle}>
              <div sx={rowStyle}>
                <div>
                  <div sx={subtitleStyle}>Future Dates Disabled</div>

                  <DatePicker label="Past & Today Only" format="DD/MM/YYYY" disableFuture={true} />
                </div>

                <div>
                  <div sx={subtitleStyle}>Disabled State</div>

                  <DatePicker label="Disabled Picker" format="DD/MM/YYYY" disabled={true} />
                </div>

                <div>
                  <div sx={subtitleStyle}>Read Only</div>

                  <DatePicker label="Read Only Picker" format="DD/MM/YYYY" readOnly={true} />
                </div>
              </div>
            </div>
          </div>

          {/* Format Examples */}
          <div sx={sectionStyle}>
            <div sx={{ fontSize: '20px', ...headingStyle }}>Date Formats</div>

            <div sx={exampleContainerStyle}>
              <div sx={rowStyle}>
                <div>
                  <div sx={subtitleStyle}>DD/MM/YYYY (European)</div>

                  <DatePicker label="European Format" format="DD/MM/YYYY" />
                </div>

                <div>
                  <div sx={subtitleStyle}>MM/DD/YYYY (US)</div>

                  <DatePicker label="US Format" format="MM/DD/YYYY" />
                </div>

                <div>
                  <div sx={subtitleStyle}>YYYY-MM-DD (ISO)</div>

                  <DatePicker label="ISO Format" format="YYYY-MM-DD" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PickerProvider>
  );
}
