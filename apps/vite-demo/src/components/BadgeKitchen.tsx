import { Badge } from '@hxnova/react-components/Badge';
import { Icon } from '@hxnova/icons';

export default function BadgeKitchen() {
  return (
    <div>
      <div sx={{ marginLeft: '30px' }}>
        <div sx={(th) => ({ display: 'flex', color: th.vars.palette.onSurface })}>
          <div>
            {(['primary', 'error', 'warning', 'info', 'success'] as const).map((k) => (
              <div key={k} sx={{ display: 'flex', flexDirection: 'column' }}>
                <div key={`${k}`} sx={{ display: 'flex', flexDirection: 'row' }}>
                  {(['small', 'large'] as const).map((i) => (
                    <div key={`${i}-${k}`} sx={{ position: 'relative', height: '100px', width: '150px' }}>
                      <Badge size={i} color={k} badgeContent={10} />
                      <div
                        sx={{ position: 'absolute', top: '30px', fontSize: '10px', left: '-10px' }}
                      >{`${i}-${k}`}</div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
            <div sx={{ display: 'flex' }}>
              {(['small', 'large'] as const).map((i) => (
                <div key={i}>
                  <div key={`${i}-disabled`} sx={{ position: 'relative', height: '100px', width: '150px' }}>
                    <Badge size={i} badgeContent={10} disabled />
                    <div
                      sx={{ position: 'absolute', top: '30px', fontSize: '10px', left: '-10px' }}
                    >{`${i}-disabled`}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div>
            {(['small', 'large'] as const).map((i) => (
              <div key={i} sx={{ display: 'flex', flexDirection: 'column' }}>
                {(['top', 'bottom'] as const).map((j) => (
                  <div key={`${i}-${j}`} sx={{ display: 'flex', flexDirection: 'row' }}>
                    {(['left', 'right'] as const).map((k) => (
                      <div key={`${i}-${j}-${k}`} sx={{ position: 'relative', height: '100px', width: '150px' }}>
                        <Badge size={i} badgeContent={10} anchorOrigin={{ vertical: j, horizontal: k }}>
                          <Icon family="material" name="mail" size={24} />
                        </Badge>
                        <div
                          sx={{ position: 'absolute', top: '30px', fontSize: '10px', left: '-30px' }}
                        >{`${i}-${j}-${k}`}</div>
                      </div>
                    ))}
                  </div>
                ))}
              </div>
            ))}
          </div>

          <div>
            <div sx={{ display: 'flex', flexDirection: 'column' }}>
              {([3, 10, 99, 999] as const).map((j) => (
                <div key={`large-${j}`} sx={{ position: 'relative', height: '100px', width: '150px' }}>
                  <Badge size={'large'} badgeContent={j}>
                    <Icon family="material" name="mail" size={24} />
                  </Badge>
                  <div
                    sx={{ position: 'absolute', top: '30px', fontSize: '10px', left: '-30px' }}
                  >{`badge-number-${j}`}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
