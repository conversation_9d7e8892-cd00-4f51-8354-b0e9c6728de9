import { Chip } from '@hxnova/react-components/Chip';
import { Icon } from '@hxnova/icons';

export default function <PERSON><PERSON><PERSON>en() {
  const HomeIcon = <Icon family="material" name="home" size={24} />;
  const DeleteIcon = <Icon family="material" name="close" size={24} />;
  return (
    <div>
      <Chip label={'My Chip'} startIcon={HomeIcon} endIcon={DeleteIcon} sx={{ margin: 8 }} />
      <Chip label={'My Chip'} startIcon={HomeIcon} endIcon={DeleteIcon} sx={{ margin: 8 }} selected />
      <Chip label={'My Chip'} startIcon={HomeIcon} endIcon={DeleteIcon} sx={{ margin: 8 }} disabled />
      <Chip label={'My Chip'} startIcon={HomeIcon} endIcon={DeleteIcon} sx={{ margin: 8 }} selected disabled />
      <br />
      <br />
      <Chip label={'My Chip'} startIcon={HomeIcon} endIcon={DeleteIcon} sx={{ margin: 8 }} onClick={() => {}} />
      <Chip
        label={'My Chip'}
        startIcon={HomeIcon}
        endIcon={DeleteIcon}
        sx={{ margin: 8 }}
        onClick={() => {}}
        selected
      />
      <Chip
        label={'My Chip'}
        startIcon={HomeIcon}
        endIcon={DeleteIcon}
        sx={{ margin: 8 }}
        onClick={() => {}}
        disabled
      />
      <Chip
        label={'My Chip'}
        startIcon={HomeIcon}
        endIcon={DeleteIcon}
        sx={{ margin: 8 }}
        onClick={() => {}}
        selected
        disabled
      />
      <br />
      <br />
      <Chip label={'My Chip'} startIcon={HomeIcon} endIcon={DeleteIcon} sx={{ margin: 8 }} size={'small'} />
      <Chip label={'My Chip'} startIcon={HomeIcon} endIcon={DeleteIcon} sx={{ margin: 8 }} size={'small'} selected />
      <Chip label={'My Chip'} startIcon={HomeIcon} endIcon={DeleteIcon} sx={{ margin: 8 }} size={'small'} disabled />
      <Chip
        label={'My Chip'}
        startIcon={HomeIcon}
        endIcon={DeleteIcon}
        sx={{ margin: 8 }}
        size={'small'}
        selected
        disabled
      />
      <br />
      <br />
      <Chip
        label={'My Chip'}
        startIcon={HomeIcon}
        endIcon={DeleteIcon}
        size={'small'}
        onClick={() => {}}
        sx={{ margin: 8 }}
      />
      <Chip
        label={'My Chip'}
        startIcon={HomeIcon}
        endIcon={DeleteIcon}
        size={'small'}
        onClick={() => {}}
        selected
        sx={{ margin: 8 }}
      />
      <Chip
        label={'My Chip'}
        startIcon={HomeIcon}
        endIcon={DeleteIcon}
        size={'small'}
        onClick={() => {}}
        disabled
        sx={{ margin: 8 }}
      />
      <Chip
        label={'My Chip'}
        startIcon={HomeIcon}
        endIcon={DeleteIcon}
        size={'small'}
        onClick={() => {}}
        selected
        disabled
        sx={{ margin: 8 }}
      />
      <br />
      <br />
      <Chip label={'My Chip'} startIcon={HomeIcon} endIcon={DeleteIcon} size={'large'} sx={{ margin: 8 }} />
      <Chip label={'My Chip'} startIcon={HomeIcon} endIcon={DeleteIcon} size={'large'} sx={{ margin: 8 }} selected />
      <Chip label={'My Chip'} startIcon={HomeIcon} endIcon={DeleteIcon} size={'large'} sx={{ margin: 8 }} disabled />
      <Chip
        label={'My Chip'}
        startIcon={HomeIcon}
        endIcon={DeleteIcon}
        size={'large'}
        selected
        disabled
        sx={{ margin: 8 }}
      />
      <br />
      <br />
      <Chip
        label={'My Chip'}
        startIcon={HomeIcon}
        endIcon={DeleteIcon}
        size={'large'}
        onClick={() => {}}
        sx={{ margin: 8 }}
      />
      <Chip
        label={'My Chip'}
        startIcon={HomeIcon}
        endIcon={DeleteIcon}
        size={'large'}
        onClick={() => {}}
        selected
        sx={{ margin: 8 }}
      />
      <Chip
        label={'My Chip'}
        startIcon={HomeIcon}
        endIcon={DeleteIcon}
        size={'large'}
        onClick={() => {}}
        disabled
        sx={{ margin: 8 }}
      />
      <Chip
        label={'My Chip'}
        startIcon={HomeIcon}
        endIcon={DeleteIcon}
        size={'large'}
        onClick={() => {}}
        sx={{ margin: 8 }}
        selected
        disabled
      />
      <br />
      <br />
    </div>
  );
}
