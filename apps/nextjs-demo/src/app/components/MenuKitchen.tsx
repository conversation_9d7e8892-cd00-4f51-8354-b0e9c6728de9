import React from 'react';
import { Menu, MenuProps } from '@hxnova/react-components/Menu';
import { MenuItem } from '@hxnova/react-components/MenuItem';
import { MenuList } from '@hxnova/react-components/MenuList';
import { Button, Typography, Checkbox, Avatar, AvatarProps } from '@hxnova/react-components';
import { ListItemContent } from '@hxnova/react-components/ListItemContent';
import { ListItemDecorator } from '@hxnova/react-components/ListItemDecorator';
import { Icon } from '@hxnova/icons';

const densities = ['compact', 'standard', 'comfortable'];
const sizeMap: Record<string, string> = {
  compact: 'small',
  standard: 'medium',
  comfortable: 'large',
};
export default function MenuKitchen() {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(!anchorEl ? event.currentTarget : null);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <div>
      <div sx={{ margin: '20px', display: 'flex', flexDirection: 'row', gap: '30px' }}>
        {densities.map((density) => (
          <div key={density}>
            <div
              sx={{
                fontSize: '14px',
                whiteSpace: 'pre-wrap',
              }}
            >
              {`${density} density with text and avatar`}
            </div>
            <div
              sx={{
                display: 'flex',
                flexDirection: 'row',
                gap: '20px',
                padding: '10px',
              }}
            >
              <MenuList density={density as MenuProps['density']}>
                <MenuItem selected>
                  <ListItemDecorator>
                    <Avatar size={sizeMap[density] as AvatarProps['size']} color="error">
                      AA
                    </Avatar>
                  </ListItemDecorator>
                  <ListItemContent>Menu item selected</ListItemContent>
                  <ListItemDecorator>
                    <Typography variant="labelMedium">⌘C</Typography>
                    <Checkbox />
                    <Icon family="material" name="keyboard_arrow_right" size={24} />
                  </ListItemDecorator>
                </MenuItem>
                <MenuItem disabled>
                  <ListItemDecorator>
                    <Avatar size={sizeMap[density] as AvatarProps['size']} color="error">
                      AA
                    </Avatar>
                  </ListItemDecorator>
                  <ListItemContent>Menu item disabled</ListItemContent>
                  <ListItemDecorator>
                    <Typography variant="labelMedium">⌘C</Typography>
                    <Checkbox />
                    <Icon family="material" name="keyboard_arrow_right" size={24} />
                  </ListItemDecorator>
                </MenuItem>
                <MenuItem selected disabled>
                  <ListItemDecorator>
                    <Avatar size={sizeMap[density] as AvatarProps['size']} color="error">
                      AA
                    </Avatar>
                  </ListItemDecorator>
                  <ListItemContent>Menu item selected && disabled</ListItemContent>
                  <ListItemDecorator>
                    <Typography variant="labelMedium">⌘C</Typography>
                    <Checkbox />
                    <Icon family="material" name="keyboard_arrow_right" size={24} />
                  </ListItemDecorator>
                </MenuItem>
                <MenuItem>
                  <ListItemDecorator>
                    <Avatar size={sizeMap[density] as AvatarProps['size']} color="error">
                      AA
                    </Avatar>
                  </ListItemDecorator>
                  <ListItemContent>Menu item</ListItemContent>
                  <ListItemDecorator>
                    <Typography variant="labelMedium">⌘C</Typography>
                    <Checkbox />
                    <Icon family="material" name="keyboard_arrow_right" size={24} />
                  </ListItemDecorator>
                </MenuItem>
              </MenuList>
            </div>
          </div>
        ))}
      </div>
      <div sx={{ margin: '20px', display: 'flex', flexDirection: 'row', gap: '30px' }}>
        {densities.map((density) => (
          <div key={density}>
            <div
              sx={{
                fontSize: '14px',
                whiteSpace: 'pre-wrap',
              }}
            >
              {`${density} density with text and icon `}
            </div>
            <div
              sx={{
                display: 'flex',
                flexDirection: 'row',
                gap: '20px',
                padding: '10px',
              }}
            >
              <MenuList density={density as MenuProps['density']}>
                <MenuItem selected>
                  <ListItemDecorator>
                    <Icon family="material" name="person" size={24} />
                  </ListItemDecorator>
                  <ListItemContent>Menu item selected</ListItemContent>
                  <ListItemDecorator>
                    <Typography variant="labelMedium">⌘C</Typography>
                    <Checkbox />
                    <Icon family="material" name="keyboard_arrow_right" size={24} />
                  </ListItemDecorator>
                </MenuItem>
                <MenuItem disabled>
                  <ListItemDecorator>
                    <Icon family="material" name="person" size={24} />
                  </ListItemDecorator>
                  <ListItemContent>Menu item disabled</ListItemContent>
                  <ListItemDecorator>
                    <Typography variant="labelMedium">⌘C</Typography>
                    <Checkbox />
                    <Icon family="material" name="keyboard_arrow_right" size={24} />
                  </ListItemDecorator>
                </MenuItem>
                <MenuItem selected disabled>
                  <ListItemDecorator>
                    <Icon family="material" name="person" size={24} />
                  </ListItemDecorator>
                  <ListItemContent>Menu item selected && disabled</ListItemContent>
                  <ListItemDecorator>
                    <Typography variant="labelMedium">⌘C</Typography>
                    <Checkbox />
                    <Icon family="material" name="keyboard_arrow_right" size={24} />
                  </ListItemDecorator>
                </MenuItem>
                <MenuItem>
                  <ListItemDecorator>
                    <Icon family="material" name="person" size={24} />
                  </ListItemDecorator>
                  <ListItemContent>Menu item</ListItemContent>
                  <ListItemDecorator>
                    <Typography variant="labelMedium">⌘C</Typography>
                    <Checkbox />
                    <Icon family="material" name="keyboard_arrow_right" size={24} />
                  </ListItemDecorator>
                </MenuItem>
              </MenuList>
            </div>
          </div>
        ))}
      </div>

      <Button onClick={handleClick}>Open Menu</Button>
      <Menu anchorEl={anchorEl} open={open} onClose={handleClose}>
        <MenuItem selected onClick={handleClose}>
          <ListItemDecorator>
            <Icon family="material" name="person" size={24} />
          </ListItemDecorator>
          <ListItemContent>Selected</ListItemContent>
          <ListItemDecorator>
            <Typography variant="labelMedium">⌘C</Typography>
            <Checkbox />
            <Icon family="material" name="keyboard_arrow_right" size={24} />
          </ListItemDecorator>
        </MenuItem>
        <MenuItem disabled>
          <ListItemDecorator>
            <Icon family="material" name="person" size={24} />
          </ListItemDecorator>
          <ListItemContent>Disabled</ListItemContent>
          <ListItemDecorator>
            <Typography variant="labelMedium">⌘C</Typography>
            <Checkbox />
            <Icon family="material" name="keyboard_arrow_right" size={24} />
          </ListItemDecorator>
        </MenuItem>
        <MenuItem selected disabled>
          <ListItemDecorator>
            <Icon family="material" name="person" size={24} />
          </ListItemDecorator>
          <ListItemContent>Selected & Disabled</ListItemContent>
          <ListItemDecorator>
            <Typography variant="labelMedium">⌘C</Typography>
            <Checkbox />
            <Icon family="material" name="keyboard_arrow_right" size={24} />
          </ListItemDecorator>
        </MenuItem>
        <MenuItem onClick={handleClose}>
          <ListItemDecorator>
            <Icon family="material" name="person" size={24} />
          </ListItemDecorator>
          <ListItemContent>Normal</ListItemContent>
          <ListItemDecorator>
            <Typography variant="labelMedium">⌘C</Typography>
            <Checkbox />
            <Icon family="material" name="keyboard_arrow_right" size={24} />
          </ListItemDecorator>
        </MenuItem>
      </Menu>
    </div>
  );
}
