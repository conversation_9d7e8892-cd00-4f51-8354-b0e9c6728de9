import React from 'react';
import { Search, SearchProps } from '@hxnova/react-components/Search';
import { IconButton } from '@hxnova/react-components/IconButton';
import { Icon } from '@hxnova/icons';

const iconSize = { small: 20, medium: 24, large: 28 };

export default function SearchKitchen() {
  const startDecorator = (size: 'medium' | 'small' | 'large' = 'medium') => (
    <IconButton variant="standard" color="inherit" size={size}>
      <Icon family="material" name="search" size={iconSize[size ?? 'medium']} />
    </IconButton>
  );
  const endDecorator = (size?: 'medium' | 'small' | 'large') => (
    <IconButton variant="standard" color="inherit" size={size}>
      <Icon family="material" name="mic_none" size={iconSize[size ?? 'medium']} />
    </IconButton>
  );
  return (
    <div sx={{ display: 'flex', flexDirection: 'column', gap: 50, margin: 8 }}>
      {(['small', 'medium', 'large'] as Array<SearchProps['size']>).map((size) => (
        <div key={size} sx={{ display: 'flex', alignItems: 'center' }}>
          <div sx={{ fontWeight: 700, width: '120px' }}>{size}</div>
          <div sx={{ display: 'flex', flexDirection: 'row', gap: 16 }}>
            <div sx={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
              <Search
                size={size}
                placeholder="Hinted search text"
                startDecorator={startDecorator(size)}
                endDecorator={endDecorator(size)}
              />
            </div>
            <div sx={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
              <Search
                size={size}
                defaultValue={'hello'}
                placeholder="Hinted search text"
                startDecorator={startDecorator(size)}
                endDecorator={endDecorator(size)}
              />
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
