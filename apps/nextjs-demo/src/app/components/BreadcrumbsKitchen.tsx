import { useState } from 'react';
import { Breadcrumbs } from '@hxnova/react-components/Breadcrumbs';
import { Link } from '@hxnova/react-components/Link';
import { Typography } from '@hxnova/react-components/Typography';
import { Button } from '@hxnova/react-components/Button';
import { Icon } from '@hxnova/icons';
import { Menu } from '@hxnova/react-components/Menu';
import { MenuItem } from '@hxnova/react-components/MenuItem';

const commonLinkStyles = (theme: any) => ({
  color: theme.vars.palette.onSurfaceVariant,
  '&:hover': { color: theme.vars.palette.onSurfaceVariant },
});

const primaryTextStyles = (theme: any) => ({
  color: theme.vars.palette.primary,
});

interface BreadcrumbItemProps {
  href?: string;
  startDecorator?: React.ReactNode;
  children: React.ReactNode;
}

const BreadcrumbItem = ({ href, startDecorator, children }: BreadcrumbItemProps) => (
  <Link underline="hover" sx={commonLinkStyles} href={href} startDecorator={startDecorator}>
    {children}
  </Link>
);

export default function BreadcrumbsKitchen() {
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <div sx={{ padding: '24px' }}>
      <div sx={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
        {/* Basic Breadcrumb */}
        <Breadcrumbs aria-label="breadcrumbs">
          <BreadcrumbItem href="#">Breadcrumb</BreadcrumbItem>
          <Typography sx={primaryTextStyles}>Breadcrumb 2</Typography>
        </Breadcrumbs>

        {/* Three levels Breadcrumb with Icon */}
        <Breadcrumbs aria-label="breadcrumbs">
          <BreadcrumbItem href="#" startDecorator={<Icon family="material" name="home" size={24} />}>
            Breadcrumb
          </BreadcrumbItem>
          <BreadcrumbItem>Breadcrumb</BreadcrumbItem>
          <Typography sx={primaryTextStyles}>Breadcrumb 3</Typography>
        </Breadcrumbs>

        {/* Four levels Breadcrumb */}
        <Breadcrumbs aria-label="breadcrumbs">
          <BreadcrumbItem>Breadcrumb</BreadcrumbItem>
          <BreadcrumbItem>Breadcrumb</BreadcrumbItem>
          <BreadcrumbItem>Breadcrumb</BreadcrumbItem>
          <Typography sx={primaryTextStyles}>Breadcrumb 4</Typography>
        </Breadcrumbs>

        {/* Multiple levels Breadcrumb */}
        <Menu anchorEl={anchorEl} open={open} onClose={handleClose} aria-labelledby="with-menu-demo-breadcrumbs">
          <MenuItem onClick={handleClose}>Breadcrumb 3</MenuItem>
          <MenuItem onClick={handleClose}>Breadcrumb 4</MenuItem>
        </Menu>
        <Breadcrumbs aria-label="breadcrumbs">
          <BreadcrumbItem href="#condensed-with-menu">Breadcrumb 1</BreadcrumbItem>
          <BreadcrumbItem href="#condensed-with-menu">Breadcrumb 2</BreadcrumbItem>
          <Button onClick={handleClick} variant="text" sx={commonLinkStyles}>
            •••
          </Button>
          <BreadcrumbItem href="#condensed-with-menu">Breadcrumb 5</BreadcrumbItem>
          <Typography sx={primaryTextStyles}>Breadcrumb 6</Typography>
        </Breadcrumbs>
      </div>
    </div>
  );
}
