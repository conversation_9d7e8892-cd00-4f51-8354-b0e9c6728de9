import { Tabs, Divider } from '@hxnova/react-components';
import { Icon } from '@hxnova/icons';

export default function TabsKitchen() {
  return (
    <div sx={{ padding: 16 }}>
      <Tabs.Root defaultValue={0} orientation="horizontal">
        <Tabs.List sx={{ marginBottom: 16 }} divider>
          <Tabs.Tab value={0}>Tab 1</Tabs.Tab>
          <Tabs.Tab value={1}>Tab 2</Tabs.Tab>
          <Tabs.Tab value={2}>Tab 3</Tabs.Tab>
          <Tabs.Tab value={3}>Tab 4</Tabs.Tab>
        </Tabs.List>
      </Tabs.Root>
      <Tabs.Root defaultValue={0} orientation="horizontal">
        <Tabs.List sx={{ marginBottom: 16 }} divider>
          <Tabs.Tab value={0} icon={<Icon family="material" name="text_fields" size={24} />} />
          <Tabs.Tab value={1} icon={<Icon family="material" name="gamepad" size={24} />} />
          <Tabs.Tab value={2} icon={<Icon family="material" name="local_police" size={24} />} />
          <Tabs.Tab value={3} icon={<Icon family="material" name="toggle_off" size={24} />} />
        </Tabs.List>
      </Tabs.Root>
      <Tabs.Root defaultValue={0} orientation="horizontal" sx={{ height: '100%' }}>
        <Tabs.List sx={{ marginBottom: 16 }} divider>
          <Tabs.Tab value={0} icon={<Icon family="material" name="text_fields" size={24} />}>
            Typography
          </Tabs.Tab>
          <Tabs.Tab value={1} icon={<Icon family="material" name="gamepad" size={24} />}>
            Buttons
          </Tabs.Tab>
          <Tabs.Tab value={2} icon={<Icon family="material" name="local_police" size={24} />}>
            Badges
          </Tabs.Tab>
          <Tabs.Tab value={3} icon={<Icon family="material" name="toggle_off" size={24} />}>
            Switches
          </Tabs.Tab>
        </Tabs.List>
      </Tabs.Root>

      <Divider sx={{ marginTop: 16, marginBottom: 16 }} />

      <div sx={{ display: 'flex', justifyContent: 'space-between' }}>
        <Tabs.Root defaultValue={0} orientation="vertical">
          <Tabs.List sx={{ marginBottom: 16 }} divider>
            <Tabs.Tab value={0}>Tab 1</Tabs.Tab>
            <Tabs.Tab value={1}>Tab 2</Tabs.Tab>
            <Tabs.Tab value={2}>Tab 3</Tabs.Tab>
            <Tabs.Tab value={3}>Tab 4</Tabs.Tab>
          </Tabs.List>
        </Tabs.Root>
        <Tabs.Root defaultValue={0} orientation="vertical">
          <Tabs.List sx={{ marginBottom: 16 }} divider>
            <Tabs.Tab value={0} icon={<Icon family="material" name="text_fields" size={24} />} />
            <Tabs.Tab value={1} icon={<Icon family="material" name="gamepad" size={24} />} />
            <Tabs.Tab value={2} icon={<Icon family="material" name="local_police" size={24} />} />
            <Tabs.Tab value={3} icon={<Icon family="material" name="toggle_off" size={24} />} />
          </Tabs.List>
        </Tabs.Root>
        <Tabs.Root defaultValue={0} orientation="vertical" sx={{ height: '100%' }}>
          <Tabs.List sx={{ marginBottom: 16 }} divider>
            <Tabs.Tab value={0} icon={<Icon family="material" name="text_fields" size={24} />}>
              Typography
            </Tabs.Tab>
            <Tabs.Tab value={1} icon={<Icon family="material" name="gamepad" size={24} />}>
              Buttons
            </Tabs.Tab>
            <Tabs.Tab value={2} icon={<Icon family="material" name="local_police" size={24} />}>
              Badges
            </Tabs.Tab>
            <Tabs.Tab value={3} icon={<Icon family="material" name="toggle_off" size={24} />}>
              Switches
            </Tabs.Tab>
          </Tabs.List>
        </Tabs.Root>
      </div>
    </div>
  );
}
