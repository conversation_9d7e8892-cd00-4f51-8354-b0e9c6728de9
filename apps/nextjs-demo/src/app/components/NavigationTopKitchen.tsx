import { NavigationTop } from '@hxnova/react-components/NavigationTop';
import { Icon } from '@hxnova/icons';
import MetrologyReportingIcon from '@nexusui/branding/MetrologyReporting';
import { Avatar } from '@hxnova/react-components/Avatar';

export default function NavigationTopKitchen() {
  return (
    <div sx={{ height: '100%', width: '100%' }}>
      <NavigationTop
        productLogo={<MetrologyReportingIcon height={40} width={40} />}
        pageTitle={'My Product'}
        divider
        userAvatar={<Avatar onClick={() => console.log('avatar clicked')}>AX</Avatar>}
      ></NavigationTop>
      <NavigationTop
        productLogo={<MetrologyReportingIcon height={40} width={40} />}
        pageTitle={'My Product'}
        divider
        iconActions={[
          {
            icon: <Icon family="material" name="invert_colors" size={24} />,
            label: 'Menu',
            onClick: () => console.log('icon action clicked'),
          },
          {
            icon: <Icon family="material" name="translate" size={24} />,
            label: 'Menu2',
            onClick: () => console.log('icon action clicked'),
          },
          {
            icon: <Icon family="material" name="more_vert" size={24} />,
            label: 'Menu3',
            onClick: () => console.log('icon action clicked'),
          },
        ]}
        userAvatar={<Avatar onClick={() => console.log('avatar clicked')}>AX</Avatar>}
      ></NavigationTop>
      <NavigationTop
        onSearchChange={(val) => console.log(val)}
        productLogo={<MetrologyReportingIcon height={40} width={40} />}
        pageTitle={'My Product'}
        divider
        iconActions={[
          {
            icon: <Icon family="material" name="invert_colors" size={24} />,
            label: 'Menu',
            onClick: () => console.log('icon action clicked'),
          },
          {
            icon: <Icon family="material" name="translate" size={24} />,
            label: 'Menu2',
            onClick: () => console.log('icon action clicked'),
          },
          {
            icon: <Icon family="material" name="more_vert" size={24} />,
            label: 'Menu3',
            onClick: () => console.log('icon action clicked'),
          },
        ]}
        primaryActions={[
          {
            children: 'Primary Action',
            onClick: () => console.log('primary action clicked'),
          },
          {
            children: 'Other Action',
            variant: 'outlined',
            onClick: () => console.log('other action clicked'),
          },
        ]}
        userAvatar={<Avatar onClick={() => console.log('avatar clicked')}>AX</Avatar>}
      >
        {`<CUSTOM CONTENT HERE>`}
      </NavigationTop>
    </div>
  );
}
