import { Meta } from '@storybook/blocks';

<Meta title="@hxnova/react-components/Testing" />

## Testing

This section provides guidance for configuring tests in projects that use `@hxnova/react-components`, depending on your testing framework.

### Vitest

Follow the steps below to set up Vitest for testing Nova components:

* Install and configure the Pigment plugin from `@pigment-css/vite-plugin` in your Vitest config:
  * Extend the `NovaTheme`.
  * Add `@hxnova/react-components` to the `transformLibraries` option.
  * Enable the Babel plugin `@babel/plugin-transform-export-namespace-from`.
* Include the setup file from `@hxnova/react-components/setup-vitest` in the [setupFiles](https://vitest.dev/config/#setupfiles) option.
  This setup file configures necessary mocks for PigmentCSS components that will throw errors during tests.

#### Example Configuration

```ts
// vitest.config.ts
import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';
import { extendTheme, pigment } from '@pigment-css/vite-plugin';
import { NovaTheme } from '@hxnova/themes';

export default defineConfig({
  plugins: [
    react(),
    pigment({
      theme: extendTheme(NovaTheme),
      transformLibraries: ['@hxnova/react-components'],
      babelOptions: {
        plugins: [`@babel/plugin-transform-export-namespace-from`],
      },
    }),
  ],
  test: {
    environment: 'jsdom',
    setupFiles: ['@hxnova/react-components/setup-vitest'], // Add your setup files here
  },
});
```