import { useState } from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import { Portal, PortalProps } from '@hxnova/react-components/Portal';
import { Button } from '@hxnova/react-components/Button';
import { Box } from '@hxnova/react-components/Box';
import { Typography } from '@hxnova/react-components/Typography';

const meta = {
  title: '@hxnova/react-components/Utils/Portal',
  component: Portal,
  parameters: {
    layout: 'centered',
  },
  tags: ['!autodocs'],
} satisfies Meta<typeof Portal>;

export default meta;

export const Default: StoryObj<Omit<PortalProps, 'children'>> = {
  render: function PortalStory(args) {
    const [show, setShow] = useState(false);

    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: '16px', alignItems: 'center' }}>
        <Button onClick={() => setShow(!show)}>{show ? 'Unmount children' : 'Mount children'}</Button>
        <Box sx={{ padding: '8px', border: '1px solid var(--palette-divider)', borderRadius: 'var(--radius-xs)' }}>
          It looks like I will render here.
          {show ? (
            <Portal {...args}>
              <Box
                sx={{
                  position: 'absolute',
                  top: '35%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  padding: '16px',
                  backgroundColor: 'var(--palette-surfaceContainer)',
                  border: '1px solid var(--palette-outline)',
                  borderRadius: 'var(--radius-xs)',
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                  zIndex: 1000,
                }}
              >
                <Typography variant="bodyMedium">
                  But I actually render here! (This content is portaled to document.body)
                </Typography>
              </Box>
            </Portal>
          ) : null}
        </Box>
      </Box>
    );
  },
  args: {
    disablePortal: false,
  },
  argTypes: {
    disablePortal: {
      control: 'boolean',
      description: "Disable the portal behavior. The children stay within the component's DOM hierarchy.",
    },
    container: {
      control: false,
      description:
        'An HTML element or function that returns one. The container will have the portal children appended to it.',
    },
  },
  parameters: {
    controls: {
      include: ['disablePortal'],
    },
  },
};
