import { useState } from 'react';
import { Portal } from '@hxnova/react-components/Portal';
import { Button } from '@hxnova/react-components/Button';
import { Box } from '@hxnova/react-components/Box';
import { Typography } from '@hxnova/react-components/Typography';

export default function DisablePortalExample() {
  const [showPortal, setShowPortal] = useState(false);
  const [showDisabled, setShowDisabled] = useState(false);

  return (
    <div sx={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <Typography variant="titleSmall" sx={{ marginBottom: '16px' }}>
        Portal Enabled vs Disabled
      </Typography>
      <div sx={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
        <div sx={{ display: 'flex', gap: '16px', justifyContent: 'center' }}>
          <Button onClick={() => setShowPortal(!showPortal)}>{showPortal ? 'Hide' : 'Show'} Portal (Enabled)</Button>
          <Button onClick={() => setShowDisabled(!showDisabled)}>
            {showDisabled ? 'Hide' : 'Show'} Portal (Disabled)
          </Button>
        </div>

        <div sx={{ display: 'flex', gap: '16px' }}>
          <Box
            sx={{
              flex: 1,
              padding: '16px',
              border: '1px solid var(--palette-divider)',
              borderRadius: 'var(--radius-xs)',
              position: 'relative',
              overflow: 'hidden',
              minHeight: '120px',
            }}
          >
            <Typography variant="bodySmall" sx={{ marginBottom: '8px', fontWeight: 'medium' }}>
              Portal Enabled (default)
            </Typography>
            <Typography variant="bodySmall" sx={{ color: 'text.secondary', marginBottom: '8px' }}>
              Content is portaled to document.body
            </Typography>
            {showPortal ? (
              <Portal>
                <Box
                  sx={{
                    position: 'fixed',
                    top: '20%',
                    left: '20%',
                    padding: '12px',
                    backgroundColor: 'var(--palette-primaryContainer)',
                    border: '1px solid var(--palette-primary)',
                    borderRadius: 'var(--radius-xs)',
                    zIndex: 1000,
                    maxWidth: '200px',
                  }}
                >
                  <Typography variant="bodySmall">I am portaled to document.body!</Typography>
                </Box>
              </Portal>
            ) : null}
          </Box>

          <Box
            sx={{
              flex: 1,
              padding: '16px',
              border: '1px solid var(--palette-divider)',
              borderRadius: 'var(--radius-xs)',
              position: 'relative',
              overflow: 'hidden',
              minHeight: '120px',
            }}
          >
            <Typography variant="bodySmall" sx={{ marginBottom: '8px', fontWeight: 'medium' }}>
              Portal Disabled
            </Typography>
            <Typography variant="bodySmall" sx={{ color: 'text.secondary', marginBottom: '8px' }}>
              Content stays within parent container
            </Typography>
            {showDisabled ? (
              <Portal disablePortal>
                <Box
                  sx={{
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    padding: '12px',
                    backgroundColor: 'var(--palette-secondaryContainer)',
                    border: '1px solid var(--palette-secondary)',
                    borderRadius: 'var(--radius-xs)',
                    maxWidth: '180px',
                  }}
                >
                  <Typography variant="bodySmall">I stay within my parent container!</Typography>
                </Box>
              </Portal>
            ) : null}
          </Box>
        </div>
      </div>
    </div>
  );
}
