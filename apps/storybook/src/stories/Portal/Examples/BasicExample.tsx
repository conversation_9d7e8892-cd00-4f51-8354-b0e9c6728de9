import { useState } from 'react';
import { Portal } from '@hxnova/react-components/Portal';
import { Button } from '@hxnova/react-components/Button';
import { Box } from '@hxnova/react-components/Box';
import { Typography } from '@hxnova/react-components/Typography';

export default function BasicExample() {
  const [show, setShow] = useState(false);

  const handleClick = () => {
    setShow(!show);
  };

  return (
    <div sx={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <Typography variant="titleSmall" sx={{ marginBottom: '16px' }}>
        Basic Portal Usage
      </Typography>
      <div sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '16px' }}>
        <Button onClick={handleClick}>{show ? 'Unmount children' : 'Mount children'}</Button>
        <Box sx={{ padding: '8px', border: '1px solid var(--palette-divider)', borderRadius: 'var(--radius-xs)' }}>
          It looks like I will render here.
          {show ? (
            <Portal>
              <Box
                sx={{
                  position: 'fixed',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  padding: '16px',
                  backgroundColor: 'var(--palette-surfaceContainer)',
                  border: '1px solid var(--palette-outline)',
                  borderRadius: 'var(--radius-xs)',
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                  zIndex: 1000,
                  maxWidth: '300px',
                }}
              >
                <Typography variant="bodyMedium" sx={{ marginBottom: '8px' }}>
                  But I actually render here!
                </Typography>
                <Typography variant="bodySmall" sx={{ color: 'text.secondary' }}>
                  This content is portaled to document.body, so it can break out of any parent container constraints.
                </Typography>
              </Box>
            </Portal>
          ) : null}
        </Box>
      </div>
    </div>
  );
}
