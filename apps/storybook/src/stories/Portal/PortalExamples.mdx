import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';
import BasicExample from './Examples/BasicExample';
import BasicExampleSource from './Examples/BasicExample.tsx?raw';
import DisablePortalExample from './Examples/DisablePortalExample';
import DisablePortalExampleSource from './Examples/DisablePortalExample.tsx?raw';

<Meta title="@hxnova/react-components/Utils/Portal/Examples" />

## Basic Portal

The Portal component renders its children into a DOM node that exists outside of the Portal's own DOM hierarchy. This is useful for components like modals, tooltips, and dropdowns that need to "break out" of their container's styling constraints.

The Portal component uses React's `createPortal()` API under the hood. By default, it renders children into `document.body`, but you can specify a different container using the **`container` prop**.

<div className="sb-unstyled">
  <BasicExample />
</div>
<CodeExpand code={BasicExampleSource} showBorderTop style={{marginTop: 16}}/>

## Disable Portal

When you need to disable the portal behavior (for example, for server-side rendering), you can use the **`disablePortal` prop**. This renders the children as normal React children within the component's DOM hierarchy.

<div className="sb-unstyled">
  <DisablePortalExample />
</div>
<CodeExpand code={DisablePortalExampleSource} showBorderTop style={{marginTop: 16}}/>

## Server-side Portals

The DOM API isn't available on the server, so you need to use the container prop callback. This callback is called during a React layout effect:

```tsx
<Portal container={() => document.getElementById('filter-panel')!}>
  <Child />
</Portal>
```