# API Documentation

- [Portal](#portal)

# Portal

API reference docs for the React Portal component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Portal` component, you can choose to import it directly or through the main entry point.

```jsx
import { Portal } from '@hxnova/react-components/Portal';
// or
import { Portal } from '@hxnova/react-components';
```

## Props

The properties available for the `Portal` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **children** | `ReactNode` | - | The children to render into the `container`. |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **container** | ``null ⏐ Element ⏐ () => Element ⏐ null`` | - | An HTML element or function that returns one.<br>The `container` will have the portal children appended to it.<br>You can also provide a callback, which is called in a React layout effect.<br>This lets you set the container from a ref, and also makes server-side rendering possible.<br>By default, it uses the body of the top-level document object,<br>so it's simply `document.body` most of the time. |
| **disablePortal** | ``false ⏐ true`` | `false` | The `children` will be under the DOM hierarchy of the parent component. |
| **ref** | ``((((instance: HTMLDivElement ⏐ null) => void) ⏐ RefObject<HTMLDivElement>) & (string ⏐ ((instance: Element ⏐ null) => void) ⏐ RefObject<...>)) ⏐ null`` | - | Allows getting a ref to the component instance.<br>Once the component unmounts, React will set `ref.current` to `null`<br>(or call the ref with `null` if you passed a callback ref).<br>@see {@link https://react.dev/learn/referencing-values-with-refs#refs-and-the-dom React Docs} |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

