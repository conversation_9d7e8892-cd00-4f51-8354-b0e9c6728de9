import { Tag } from '@hxnova/react-components/Tag';
import { Icon } from '@hxnova/icons';

export default function IconsExample() {
  return (
    <div sx={{ display: 'flex', flexDirection: 'row', flexWrap: 'wrap', gap: '1rem' }}>
      <Tag label="Start Icon" startIcon={<Icon family="material" name="download" size={16} />} />
      <Tag label="End Icon" endIcon={<Icon family="material" name="clear" size={16} />} />
      <Tag
        label="Both Icons"
        startIcon={<Icon family="material" name="download" size={16} />}
        endIcon={<Icon family="material" name="clear" size={16} />}
      />
    </div>
  );
}
