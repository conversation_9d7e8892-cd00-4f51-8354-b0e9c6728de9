import type { Meta, StoryFn } from '@storybook/react';
import { TagProps, Tag as NovaTag } from '@hxnova/react-components/Tag';
import { Icon } from '@hxnova/icons';

const meta = {
  title: '@hxnova/react-components/Tag',
  component: NovaTag,
  parameters: {
    layout: 'centered',
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/tZgUauJZLgk0pX7rHCfxKt/NOVA-Core-Components?node-id=9835-10663&p=f&t=S6lRRclYr11fMtFX-0',
    },
  },
  tags: ['!autodocs'],
} satisfies Meta<typeof NovaTag>;

export default meta;

const TagTemplate: StoryFn<
  (
    props: Omit<TagProps, 'startIcon' | 'endIcon'> & {
      showStartIcon: boolean;
      showEndIcon: boolean;
      clickable: boolean;
    },
  ) => JSX.Element
> = ({ showStartIcon, showEndIcon, clickable, ...other }) => {
  return (
    <NovaTag
      {...other}
      onClick={clickable ? () => {} : undefined}
      endIcon={showEndIcon ? <Icon family="material" name="clear" size={16} /> : undefined}
      startIcon={showStartIcon ? <Icon family="material" name="download" size={16} /> : undefined}
    />
  );
};

export const Tag = {
  render: TagTemplate,
  args: {
    label: 'Tag Label',
    variant: 'neutral',
    intensity: 'bold',
    disabled: false,
    showStartIcon: false,
    showEndIcon: false,
    clickable: false,
  },
  argTypes: {
    variant: {
      control: { type: 'radio' },
      options: ['neutral', 'error', 'warning', 'info', 'success'],
    },
    intensity: {
      control: { type: 'radio' },
      options: ['bold', 'subtle'],
    },
    disabled: {
      control: { type: 'boolean' },
    },
  },
  parameters: {
    controls: {
      include: ['label', 'variant', 'intensity', 'disabled', 'showStartIcon', 'showEndIcon', 'clickable'],
    },
  },
};
