import { useState } from 'react';
import type { Meta, StoryFn, StoryObj } from '@storybook/react';
import { Collapse, CollapseProps } from '@hxnova/react-components/Collapse';
import { Switch } from '@hxnova/react-components/Switch';
import { Box } from '@hxnova/react-components/Box';
import { Typography } from '@hxnova/react-components/Typography';

const meta = {
  title: '@hxnova/react-components/Transitions/Collapse',
  component: Collapse,
  parameters: {
    layout: 'centered',
  },
  tags: ['!autodocs'],
} satisfies Meta<typeof Collapse>;

export default meta;
type Story = StoryObj<typeof meta>;

const Template: StoryFn<(props: CollapseProps) => JSX.Element> = ({ ...args }) => {
  const [checked, setChecked] = useState(false);

  return (
    <Box sx={{ position: 'relative', minWidth: '400px', minHeight: '200px' }}>
      <Box sx={{ position: 'absolute', top: 0, left: 0, zIndex: 1 }}>
        <Switch
          checked={checked}
          onChange={() => setChecked((prev: boolean) => !prev)}
          endDecorator="Toggle Collapse"
        />
      </Box>
      <Box sx={{ paddingTop: '48px' }}>
        <Collapse key={args.orientation} {...args} in={checked}>
          <Box
            style={{
              padding: '16px',
              border: '1px solid var(--palette-outlineVariant)',
              borderRadius: 'var(--radius-2xs)',
              backgroundColor: 'var(--palette-surfaceContainer)',
              width: args.orientation === 'horizontal' ? 300 : 320,
              height: args.orientation === 'vertical' ? 'auto' : 100,
            }}
          >
            <Typography>
              This is the content that will be collapsed. The Collapse component smoothly animates the{' '}
              {args.orientation === 'horizontal' ? 'width' : 'height'} changes.
            </Typography>
          </Box>
        </Collapse>
      </Box>
    </Box>
  );
};

export const Default: Story = {
  render: Template,
  args: {
    orientation: 'vertical',
    collapsedSize: '0px',
    timeout: 'auto',
  },
  argTypes: {
    orientation: {
      control: { type: 'radio' },
      options: ['vertical', 'horizontal'],
    },
    collapsedSize: {
      control: 'text',
    },
    timeout: {
      control: 'select',
      options: ['auto', 100, 500, 1000],
    },
  },
  parameters: {
    controls: {
      include: ['orientation', 'collapsedSize', 'timeout'],
    },
  },
};
