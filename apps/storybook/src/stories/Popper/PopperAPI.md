# Popper API

<p class="description">The Popper component is a utility for creating floating elements that are positioned relative to an anchor element.</p>

## Import

```js
import { Popper } from '@hxnova/react-components/Popper';
```

## Props

| Name | Type | Default | Description |
|:-----|:-----|:--------|:------------|
| anchorEl | Element<br>&#124;&nbsp;func<br>&#124;&nbsp;object<br>&#124;&nbsp;null | | An HTML element, [virtualElement](https://popper.js.org/docs/v2/virtual-elements/), or a function that returns either. It's used to set the position of the popper. The return value will passed as the reference object of the Popper instance. |
| children | node<br>&#124;&nbsp;func | | Popper render function or node. |
| container | Element<br>&#124;&nbsp;func<br>&#124;&nbsp;null | document.body | An HTML element or function that returns one. The `container` will have the portal children appended to it.<br>You can also provide a callback, which is called in a React layout effect. This lets you set the container from a ref, and also makes server-side rendering possible.<br>By default, it uses the body of the top-level document object, so it's simply `document.body` most of the time. |
| disablePortal | bool | false | The `children` will be under the DOM hierarchy of the parent component. |
| keepMounted | bool | false | Always keep the children in the DOM. This prop can be useful in SEO situation or when you want to maximize the responsiveness of the Popper. |
| modifiers | arrayOf(object) | | Popper.js is based on a "plugin-like" architecture, most of its features are fully encapsulated "modifiers".<br>A modifier is a function that is called each time Popper.js needs to compute the position of the popper. For this reason, modifiers should be very performant to avoid bottlenecks. To learn how to create a modifier, [read the modifiers documentation](https://popper.js.org/docs/v2/modifiers/). |
| open* | bool | | If `true`, the component is shown. |
| placement | 'auto-end'<br>&#124;&nbsp;'auto-start'<br>&#124;&nbsp;'auto'<br>&#124;&nbsp;'bottom-end'<br>&#124;&nbsp;'bottom-start'<br>&#124;&nbsp;'bottom'<br>&#124;&nbsp;'left-end'<br>&#124;&nbsp;'left-start'<br>&#124;&nbsp;'left'<br>&#124;&nbsp;'right-end'<br>&#124;&nbsp;'right-start'<br>&#124;&nbsp;'right'<br>&#124;&nbsp;'top-end'<br>&#124;&nbsp;'top-start'<br>&#124;&nbsp;'top' | 'bottom' | Popper placement. |
| popperOptions | object | {} | Options provided to the [`Popper.js`](https://popper.js.org/docs/v2/constructors/#options) instance. |
| popperRef | ref | | A ref that points to the used popper instance. |
| transition | bool | false | Help supporting a react-transition-group/Transition component. |

The `ref` is forwarded to the root element.

Any other props supplied will be provided to the root element.