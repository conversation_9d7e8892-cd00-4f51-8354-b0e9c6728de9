import { useState, useRef } from 'react';
import { Popper, PopperPlacementType } from '@hxnova/react-components/Popper';
import { Button } from '@hxnova/react-components/Button';
import { Box } from '@hxnova/react-components/Box';
import { Typography } from '@hxnova/react-components/Typography';
import { Dropdown } from '@hxnova/react-components/Dropdown';
import { Option } from '@hxnova/react-components/Option';

const placements: PopperPlacementType[] = [
  'top',
  'top-start',
  'top-end',
  'bottom',
  'bottom-start',
  'bottom-end',
  'left',
  'left-start',
  'left-end',
  'right',
  'right-start',
  'right-end',
  'auto',
];

export default function PlacementExample() {
  const [open, setOpen] = useState(false);
  const [placement, setPlacement] = useState<PopperPlacementType>('bottom');
  const anchorRef = useRef<HTMLButtonElement>(null);

  return (
    <div sx={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <Typography variant="titleSmall" sx={{ marginBottom: '16px' }}>
        Placement Options
      </Typography>
      <div sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '16px' }}>
        <div sx={{ display: 'flex', gap: '16px', alignItems: 'center' }}>
          <Typography variant="bodyMedium">Placement:</Typography>
          <Dropdown
            value={placement}
            onChange={(_, value) => setPlacement(value as PopperPlacementType)}
            sx={{ minWidth: '140px' }}
          >
            {placements.map((placementOption) => (
              <Option key={placementOption} value={placementOption}>
                {placementOption}
              </Option>
            ))}
          </Dropdown>
        </div>

        <Button ref={anchorRef} onClick={() => setOpen(!open)}>
          {open ? 'Close' : 'Open'} Popper ({placement})
        </Button>

        <Popper open={open} anchorEl={anchorRef.current} placement={placement}>
          <Box
            sx={{
              padding: '12px 16px',
              backgroundColor: 'var(--palette-surfaceContainer)',
              border: '1px solid var(--palette-outline)',
              borderRadius: 'var(--radius-xs)',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
              maxWidth: '200px',
            }}
          >
            <Typography variant="bodyMedium">Placement: {placement}</Typography>
          </Box>
        </Popper>
      </div>
    </div>
  );
}
