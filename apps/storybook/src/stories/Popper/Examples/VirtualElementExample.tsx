import { useState, useRef, useCallback } from 'react';
import { Popper } from '@hxnova/react-components/Popper';
import { Button } from '@hxnova/react-components/Button';
import { Box } from '@hxnova/react-components/Box';
import { Typography } from '@hxnova/react-components/Typography';

export default function VirtualElementExample() {
  const [contextMenuOpen, setContextMenuOpen] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [followMouseOpen, setFollowMouseOpen] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // Virtual element for context menu
  const contextMenuVirtualElement = {
    getBoundingClientRect: () => ({
      width: 0,
      height: 0,
      top: mousePosition.y,
      right: mousePosition.x,
      bottom: mousePosition.y,
      left: mousePosition.x,
      x: mousePosition.x,
      y: mousePosition.y,
      toJSON: () => ({}),
    }),
  };

  // Virtual element that follows mouse
  const followMouseVirtualElement = {
    getBoundingClientRect: () => ({
      width: 0,
      height: 0,
      top: mousePosition.y,
      right: mousePosition.x,
      bottom: mousePosition.y,
      left: mousePosition.x,
      x: mousePosition.x,
      y: mousePosition.y,
      toJSON: () => ({}),
    }),
  };

  const handleContextMenu = useCallback((event: React.MouseEvent) => {
    event.preventDefault();
    setMousePosition({ x: event.clientX, y: event.clientY });
    setContextMenuOpen(true);
  }, []);

  const handleMouseMove = useCallback(
    (event: React.MouseEvent) => {
      if (followMouseOpen) {
        setMousePosition({ x: event.clientX, y: event.clientY });
      }
    },
    [followMouseOpen],
  );

  const handleClick = useCallback(() => {
    setContextMenuOpen(false);
  }, []);

  return (
    <div sx={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <div>
        <Typography variant="titleSmall" sx={{ marginBottom: '16px' }}>
          Context Menu with Virtual Element
        </Typography>
        <div sx={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
          <Typography variant="bodySmall" sx={{ color: 'text.secondary' }}>
            Right-click anywhere in the blue area to open a context menu positioned at the cursor location.
          </Typography>
          <Box
            ref={containerRef}
            onContextMenu={handleContextMenu}
            onClick={handleClick}
            sx={{
              width: '400px',
              height: '200px',
              backgroundColor: 'var(--palette-primaryContainer)',
              border: '2px dashed var(--palette-primary)',
              borderRadius: 'var(--radius-md)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'context-menu',
              userSelect: 'none',
            }}
          >
            <Typography variant="bodyMedium">Right-click me for context menu</Typography>
          </Box>
          <Popper open={contextMenuOpen} anchorEl={contextMenuVirtualElement} placement="bottom-start">
            <Box
              sx={{
                padding: '8px',
                backgroundColor: 'var(--palette-surface)',
                border: '1px solid var(--palette-outline)',
                borderRadius: 'var(--radius-xs)',
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                minWidth: '150px',
              }}
            >
              <Box sx={{ padding: '8px 12px', cursor: 'pointer', borderRadius: 'var(--radius-2xs)' }}>
                <Typography variant="bodySmall">Copy</Typography>
              </Box>
              <Box sx={{ padding: '8px 12px', cursor: 'pointer', borderRadius: 'var(--radius-2xs)' }}>
                <Typography variant="bodySmall">Paste</Typography>
              </Box>
              <Box sx={{ padding: '8px 12px', cursor: 'pointer', borderRadius: 'var(--radius-2xs)' }}>
                <Typography variant="bodySmall">Delete</Typography>
              </Box>
            </Box>
          </Popper>
        </div>
      </div>

      <div>
        <Typography variant="titleSmall" sx={{ marginBottom: '16px' }}>
          Mouse Follower
        </Typography>
        <div sx={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
          <Button onClick={() => setFollowMouseOpen(!followMouseOpen)}>
            {followMouseOpen ? 'Stop' : 'Start'} Mouse Follower
          </Button>
          <Box
            onMouseMove={handleMouseMove}
            sx={{
              width: '400px',
              height: '200px',
              backgroundColor: 'var(--palette-secondaryContainer)',
              border: '2px dashed var(--palette-secondary)',
              borderRadius: 'var(--radius-md)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              position: 'relative',
            }}
          >
            <Typography variant="bodyMedium">
              {followMouseOpen ? 'Move your mouse around!' : 'Click the button above to start'}
            </Typography>
          </Box>
          <Popper open={followMouseOpen} anchorEl={followMouseVirtualElement} placement="bottom-start">
            <Box
              sx={{
                padding: '8px 12px',
                backgroundColor: 'var(--palette-surface)',
                border: '1px solid var(--palette-outline)',
                borderRadius: 'var(--radius-xs)',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                pointerEvents: 'none',
              }}
            >
              <Typography variant="bodySmall">
                Following mouse: ({mousePosition.x}, {mousePosition.y})
              </Typography>
            </Box>
          </Popper>
        </div>
      </div>
    </div>
  );
}
