import { useState, useRef } from 'react';
import { Popper } from '@hxnova/react-components/Popper';
import { Button } from '@hxnova/react-components/Button';
import { Box } from '@hxnova/react-components/Box';
import { Typography } from '@hxnova/react-components/Typography';

export default function BasicExample() {
  const [basicOpen, setBasicOpen] = useState(false);
  const basicAnchorRef = useRef<HTMLButtonElement>(null);

  return (
    <div sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '16px' }}>
      <Button ref={basicAnchorRef} onClick={() => setBasicOpen(!basicOpen)}>
        {basicOpen ? 'Close' : 'Open'} Basic Popper
      </Button>
      <Popper open={basicOpen} anchorEl={basicAnchorRef.current} placement="bottom">
        <Box
          sx={{
            padding: '12px 16px',
            backgroundColor: 'var(--palette-surfaceContainer)',
            border: '1px solid var(--palette-outline)',
            borderRadius: 'var(--radius-xs)',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
            maxWidth: '200px',
          }}
        >
          <Typography variant="bodyMedium">I am a basic popper!</Typography>
        </Box>
      </Popper>
    </div>
  );
}
