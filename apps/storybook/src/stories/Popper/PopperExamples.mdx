import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';
import BasicExample from './Examples/BasicExample';
import BasicExampleSource from './Examples/BasicExample.tsx?raw';
import PlacementExample from './Examples/PlacementExample';
import PlacementExampleSource from './Examples/PlacementExample.tsx?raw';
import VirtualElementExample from './Examples/VirtualElementExample';
import VirtualElementExampleSource from './Examples/VirtualElementExample.tsx?raw';

<Meta title="@hxnova/react-components/Utils/Popper/Examples" />

## Basic Popper

The Popper component is a utility for creating floating elements that are positioned relative to an anchor element. It's built on top of [Popper.js](https://popper.js.org/) and provides automatic positioning, collision detection, and viewport boundary handling.

The Popper requires an **`anchorEl` prop** to define the reference element and an **`open` prop** to control visibility. The **`placement` prop** determines where the popper appears relative to the anchor.

<div className="sb-unstyled">
  <BasicExample />
</div>
<CodeExpand code={BasicExampleSource} showBorderTop style={{marginTop: 16}}/>

## Placement Options

The Popper supports various placement options to position the content relative to the anchor element. You can use basic placements like `top`, `bottom`, `left`, `right`, or more specific ones like `top-start`, `bottom-end`, etc.

<div className="sb-unstyled">
  <PlacementExample />
</div>
<CodeExpand code={PlacementExampleSource} showBorderTop style={{marginTop: 16}}/>

## Virtual Element

Popper.js supports virtual elements, which are useful when you need to position a popper relative to a custom position rather than a DOM element. This is particularly useful for context menus or custom positioning scenarios.

<div className="sb-unstyled">
  <VirtualElementExample />
</div>
<CodeExpand code={VirtualElementExampleSource} showBorderTop style={{marginTop: 16}}/>