import { Meta } from '@storybook/blocks';

<Meta title="@hxnova/react-components/Utils/Popper/API" />

# Popper API

<p className="description">
  The Popper component is a utility for creating floating elements that are positioned relative to an anchor element. It's built on top of Popper.js and provides automatic positioning, collision detection, and viewport boundary handling.
</p>

## Import

```tsx
import { Popper } from '@hxnova/react-components/Popper';
```

## Props

| Name | Type | Default | Description |
|------|------|---------|-------------|
| `anchorEl` | `null \| VirtualElement \| HTMLElement \| (() => HTMLElement) \| (() => VirtualElement)` | - | An HTML element, virtualElement, or a function that returns either. It's used to set the position of the popper. |
| `children` | `React.ReactNode \| ((props: PopperChildrenProps) => React.ReactNode)` | - | Popper render function or node. |
| `container` | `Element \| (() => Element \| null) \| null` | `document.body` | An HTML element or function that returns one. The container will have the portal children appended to it. |
| `disablePortal` | `boolean` | `false` | The children will be under the DOM hierarchy of the parent component. |
| `keepMounted` | `boolean` | `false` | Always keep the children in the DOM. This prop can be useful in SEO situation or when you want to maximize the responsiveness of the Popper. |
| `modifiers` | `Options['modifiers']` | - | Popper.js modifiers. A modifier is a function that is called each time Popper.js needs to compute the position of the popper. |
| `open` | `boolean` | - | **Required.** If `true`, the component is shown. |
| `placement` | `PopperPlacementType` | `'bottom'` | Popper placement relative to the anchor element. |
| `popperOptions` | `Partial<OptionsGeneric<any>>` | `{}` | Options provided to the Popper.js instance. |
| `popperRef` | `React.Ref<Instance>` | - | A ref that points to the used popper instance. |
| `transition` | `boolean` | `false` | Help supporting a react-transition-group/Transition component. |

## Placement Options

The `placement` prop accepts the following values:

- `'auto'`, `'auto-start'`, `'auto-end'`
- `'top'`, `'top-start'`, `'top-end'`
- `'bottom'`, `'bottom-start'`, `'bottom-end'`
- `'right'`, `'right-start'`, `'right-end'`
- `'left'`, `'left-start'`, `'left-end'`

## Usage

### Basic Popper

```tsx
import { Popper } from '@hxnova/react-components/Popper';
import { useState, useRef } from 'react';

function MyComponent() {
  const [open, setOpen] = useState(false);
  const anchorRef = useRef<HTMLButtonElement>(null);

  return (
    <div>
      <button ref={anchorRef} onClick={() => setOpen(!open)}>
        Toggle Popper
      </button>
      <Popper open={open} anchorEl={anchorRef.current} placement="bottom">
        <div>Popper content</div>
      </Popper>
    </div>
  );
}
```

### With Virtual Element

```tsx
import { Popper } from '@hxnova/react-components/Popper';
import { useState } from 'react';

function MyComponent() {
  const [open, setOpen] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  const virtualElement = {
    getBoundingClientRect: () => ({
      width: 0,
      height: 0,
      top: mousePosition.y,
      right: mousePosition.x,
      bottom: mousePosition.y,
      left: mousePosition.x,
      x: mousePosition.x,
      y: mousePosition.y,
      toJSON: () => ({}),
    }),
  };

  return (
    <div
      onContextMenu={(e) => {
        e.preventDefault();
        setMousePosition({ x: e.clientX, y: e.clientY });
        setOpen(true);
      }}
    >
      Right-click me
      <Popper open={open} anchorEl={virtualElement} placement="bottom-start">
        <div>Context menu</div>
      </Popper>
    </div>
  );
}
```

### With Render Function

```tsx
import { Popper } from '@hxnova/react-components/Popper';

function MyComponent() {
  return (
    <Popper open={true} anchorEl={anchorRef.current}>
      {({ placement }) => (
        <div>Current placement: {placement}</div>
      )}
    </Popper>
  );
}
```

## Notes

- The Popper component is built on top of [Popper.js](https://popper.js.org/)
- It automatically handles positioning, collision detection, and viewport boundaries
- Virtual elements are useful for context menus and custom positioning scenarios
- The `keepMounted` prop is useful for SEO or when you want to maximize responsiveness
- Use `disablePortal` for server-side rendering compatibility