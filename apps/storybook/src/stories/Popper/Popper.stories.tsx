import { useState, useRef } from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import { Popper, PopperProps } from '@hxnova/react-components/Popper';
import { Button } from '@hxnova/react-components/Button';
import { Box } from '@hxnova/react-components/Box';
import { Typography } from '@hxnova/react-components/Typography';
import { Card } from '@hxnova/react-components/Card';

const meta = {
  title: '@hxnova/react-components/Utils/Popper',
  component: Popper,
  parameters: {
    layout: 'centered',
  },
  tags: ['!autodocs'],
} satisfies Meta<typeof Popper>;

export default meta;

export const Default: StoryObj<Omit<PopperProps, 'children' | 'anchorEl' | 'open'>> = {
  render: function PopperStory(args) {
    const [open, setOpen] = useState(false);
    const anchorRef = useRef<HTMLButtonElement>(null);

    const handleClick = () => {
      setOpen(!open);
    };

    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: '16px', alignItems: 'center' }}>
        <Button ref={anchorRef} onClick={handleClick}>
          {open ? 'Close Popper' : 'Open Popper'}
        </Button>
        <Popper {...args} open={open} anchorEl={anchorRef.current}>
          <Card.Root
            sx={{
              padding: '16px',
              maxWidth: '300px',
              backgroundColor: 'var(--palette-surface)',
              border: '1px solid var(--palette-outline)',
              borderRadius: 'var(--radius-md)',
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            }}
          >
            <Typography variant="titleSmall" sx={{ marginBottom: '8px' }}>
              Popper Content
            </Typography>
            <Typography variant="bodyMedium">
              This content is positioned relative to the anchor element using Popper.js. It automatically adjusts its
              position to stay within the viewport.
            </Typography>
          </Card.Root>
        </Popper>
      </Box>
    );
  },
  args: {
    placement: 'bottom',
    disablePortal: false,
    keepMounted: false,
    transition: false,
  },
  argTypes: {
    placement: {
      control: 'select',
      options: [
        'auto',
        'auto-start',
        'auto-end',
        'top',
        'top-start',
        'top-end',
        'bottom',
        'bottom-start',
        'bottom-end',
        'right',
        'right-start',
        'right-end',
        'left',
        'left-start',
        'left-end',
      ],
      description: 'Popper placement relative to the anchor element',
    },
  },
  parameters: {
    controls: {
      include: ['placement'],
    },
  },
};
