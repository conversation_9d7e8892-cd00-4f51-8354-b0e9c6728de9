import React, { useState } from 'react';
import { TreeView, TreeItem } from '@hxnova/react-components/TreeView';
import { Badge } from '@hxnova/react-components/Badge';
import { IconButton } from '@hxnova/react-components/IconButton';
import { Icon } from '@hxnova/icons';

const FolderIcon = <Icon family="material" size="24px" name="folder" />;
const FileIcon = <Icon family="material" size="24px" name="description" />;
const EndDecorator = (
  <div sx={{ display: 'flex', alignItems: 'center', gap: 4 }}>
    <Badge badgeContent={3} color="primary" />
    <IconButton size="small" variant="neutral">
      <Icon family="material" name="more_vert" />
    </IconButton>
  </div>
);

export default function Example() {
  const [disabled, setDisabled] = useState(true);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    setDisabled(!disabled);
  };
  return (
    <div sx={{ width: 400 }}>
      <TreeView startDecorator={FolderIcon}>
        <TreeItem itemId="themes" label="Themes">
          <TreeItem itemId="themes-readme" label="Readme" startDecorator={FileIcon} endDecorator={EndDecorator} />
          <TreeItem itemId="themes-changelog" label="Changelog" startDecorator={FileIcon} />
        </TreeItem>
        <TreeItem
          itemId="components"
          label="React Components"
          disabled={disabled}
          endDecorator={
            <div sx={{ display: 'flex', alignItems: 'center' }}>
              <IconButton size="small" variant="neutral" onClick={handleClick}>
                {disabled ? (
                  <Icon family="material" name="visibility_off" />
                ) : (
                  <Icon family="material" name="visibility" />
                )}
              </IconButton>
            </div>
          }
        >
          <TreeItem itemId="components-changelog" label="Changelog" startDecorator={FileIcon} />
          <TreeItem itemId="components-readme" label="Readme" startDecorator={FileIcon} />
          <TreeItem itemId="avatar-component" label="Avatar">
            <TreeItem itemId="avatar-component-api" label="API" startDecorator={FileIcon} />
            <TreeItem itemId="avatar-component-examples" label="Examples" startDecorator={FileIcon} />
            <TreeItem itemId="avatar-component-avatar" label="Avatar" startDecorator={FileIcon} />
          </TreeItem>
        </TreeItem>
        <TreeItem itemId="icons" label="Icons">
          <TreeItem itemId="icons-readme" label="Readme" startDecorator={FileIcon} />
          <TreeItem itemId="icons-changelog" label="Changelog" startDecorator={FileIcon} />
        </TreeItem>
      </TreeView>
    </div>
  );
}
