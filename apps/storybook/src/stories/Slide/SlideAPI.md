# API Documentation

- [Slide](#slide)

# Slide

API reference docs for the React Slide component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Slide` component, you can choose to import it directly or through the main entry point.

```jsx
import { Slide } from '@hxnova/react-components/Slide';
// or
import { Slide } from '@hxnova/react-components';
```

## Props

The properties available for the `Slide` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **children*** | ``ReactElement<unknown, any>`` | - | A single child content element. |
| **appear** | ``false ⏐ true`` | `true` | Perform the enter transition when it first mounts if `in` is also `true`.<br>Set this to `false` to disable this behavior. |
| **container** | ``null ⏐ Element ⏐ (element: Element) => Element`` | - | An HTML element, or a function that returns one.<br>It's used to set the container the Slide is transitioning from. |
| **direction** | ``"left" ⏐ "right" ⏐ "up" ⏐ "down"`` | `'down'` | Direction the child node will enter from. |
| **easing** | ``string ⏐ { enter?: string ⏐ undefined; exit?: string ⏐ undefined; }`` | `{ enter: 'cubic-bezier(0.0, 0, 0.2, 1)', exit: 'cubic-bezier(0.4, 0, 0.6, 1)', }` | The transition timing function.<br>You may specify a single easing or a object containing enter and exit values. |
| **in** | ``false ⏐ true`` | - | If `true`, the component will transition in. |
| **timeout** | ``number ⏐ { appear?: number ⏐ undefined; enter?: number ⏐ undefined; exit?: number ⏐ undefined; } ⏐ { appear?: number ⏐ undefined; enter?: number ⏐ undefined; exit?: number ⏐ undefined; }`` | `{ enter: 225, exit: 195, }` | The duration for the transition, in milliseconds.<br>You may specify a single timeout for all transitions, or individually with an object. |
| **TransitionComponent** | ``ComponentClass<any, any> ⏐ FunctionComponent<any>`` | `Transition` | The component used for the transition. |

