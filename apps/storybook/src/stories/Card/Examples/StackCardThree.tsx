import React from 'react';
import { Card } from '@hxnova/react-components/Card';
import { IconButton } from '@hxnova/react-components/IconButton';
import { Icon } from '@hxnova/icons';

export default function Example() {
  return (
    <Card.Root sx={{ maxWidth: '360px' }}>
      <Card.Media
        component="img"
        image={'https://cdn.sanity.io/images/eqlh3dcx/dev/c2ab4812a5485ba4a15aa8f4517b94edb5728c1d-1142x643.png'}
      />
      <Card.Content
        title={'Designing Great Software'}
        supportingText={'120MB'}
        action={
          <IconButton variant="neutral" aria-label="settings">
            <Icon family="material" name="more_vert" size={24} />
          </IconButton>
        }
      />
    </Card.Root>
  );
}
