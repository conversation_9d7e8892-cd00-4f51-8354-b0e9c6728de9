import React from 'react';
import { Card } from '@hxnova/react-components/Card';
import { Avatar } from '@hxnova/react-components/Avatar';
import { IconButton } from '@hxnova/react-components/IconButton';
import { Icon } from '@hxnova/icons';
import CardCover from '../../../assets/card_cover.svg';
import avatarFirst from '../../Avatar/Examples/images/avatar_first.png';
import avatarSecond from '../../Avatar/Examples/images/avatar_second.png';
import avatarThird from '../../Avatar/Examples/images/avatar_third.png';
import { Button } from '@hxnova/react-components/Button';
import { Tag } from '@hxnova/react-components/Tag';
import { Checkbox } from '@hxnova/react-components/Checkbox';

export default function Example() {
  return (
    <Card.Root sx={{ width: '360px' }}>
      <Card.Header
        avatar={<Avatar color={'error'}>AA</Avatar>}
        action={
          <div sx={{ display: 'flex', gap: '4px' }}>
            <IconButton variant="neutral" aria-label="settings">
              <Icon family="material" name="more_vert" size={24} />
            </IconButton>
            <Checkbox />
          </div>
        }
        heading={'Heading'}
        subheading={'Subheading'}
      />
      <Card.Media component="img" image={CardCover} sx={{ height: '156px' }} />
      <Card.Content
        startDecorator={
          <div sx={{ display: 'flex', gap: '8px' }}>
            <Tag label="Tag" variant="success" intensity="subtle" />
            <Tag label="Tag" variant="error" intensity="subtle" />
            <Tag label="Tag" variant="info" intensity="subtle" />
          </div>
        }
        title={'Title'}
        subtitle={'Subtitle'}
        supportingText={'Supporting text'}
        action={
          <IconButton variant="neutral" aria-label="settings">
            <Icon family="material" name="more_vert" size={24} />
          </IconButton>
        }
        endDecorator={
          <div sx={{ display: 'flex', gap: '8px' }}>
            <Avatar src={avatarFirst} />
            <Avatar src={avatarSecond} />
            <Avatar src={avatarThird} />
          </div>
        }
      />
      <Card.Actions>
        <Button variant="text">Button</Button>
        <Button>Button</Button>
      </Card.Actions>
    </Card.Root>
  );
}
