import React from 'react';
import { Card } from '@hxnova/react-components/Card';
import { Button } from '@hxnova/react-components/Button';
import { Tag } from '@hxnova/react-components/Tag';
import { Icon } from '@hxnova/icons';

export default function Example() {
  return (
    <Card.Root sx={{ maxWidth: '360px' }}>
      <Card.Media
        component="img"
        image={'https://cdn.sanity.io/images/eqlh3dcx/dev/c2ab4812a5485ba4a15aa8f4517b94edb5728c1d-1142x643.png'}
      />
      <Card.Content
        startDecorator={
          <div sx={{ display: 'flex', gap: '8px' }}>
            <Tag label="Tag" variant="success" intensity="subtle" />
            <Tag label="Tag" variant="error" intensity="subtle" />
            <Tag label="Tag" variant="info" intensity="subtle" />
          </div>
        }
        title={'Designing Great Software'}
        subtitle={'10 useful tips to consider when designing a new application'}
        supportingText={'1 day ago'}
      />
      <Card.Actions>
        <Button endIcon={<Icon family="material" name="arrow_forward" size={24} />}>Read more</Button>
      </Card.Actions>
    </Card.Root>
  );
}
