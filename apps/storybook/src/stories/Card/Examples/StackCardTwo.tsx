import { Card } from '@hxnova/react-components/Card';
import { Avatar } from '@hxnova/react-components/Avatar';
import { IconButton } from '@hxnova/react-components/IconButton';
import avatarFirst from '../../Avatar/Examples/images/avatar_first.png';
import { Icon } from '@hxnova/icons';

export default function Example() {
  return (
    <Card.Root sx={{ width: '360px' }}>
      <Card.Header
        avatar={<Avatar src={avatarFirst} />}
        action={
          <IconButton variant="neutral">
            <Icon family="material" name="star" size={24} />
          </IconButton>
        }
        heading={'Daniela Maas'}
        subheading={'Yesterday'}
      />
      <Card.Content
        title={'Bug reported'}
        supportingText={'I found a bug where the image stretches when inspect mode is turned on'}
      />
    </Card.Root>
  );
}
