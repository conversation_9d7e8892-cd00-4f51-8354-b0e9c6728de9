import { Divider } from '@hxnova/react-components/Divider';
import { Slider } from '@hxnova/react-components/Slider';
import React from 'react';

export default function DividerChildPosition() {
  const [position, setPosition] = React.useState<number | Array<number>>(50);
  return (
    <div sx={{ width: '100%', display: 'flex', flexDirection: 'column' }}>
      <div sx={{ gap: '8px' }}>
        <div sx={{ height: 40, borderRadius: '8px' }} />
        <Divider style={{ '--nova-divider-childPosition': `${position}%` }} orientation="horizontal">
          Visual indicator
        </Divider>
        <div sx={{ height: 40, borderRadius: '8px' }} />
      </div>
      <Slider
        value={position}
        min={0}
        max={100}
        step={1}
        valueLabelDisplay="on"
        valueLabelFormat={(value) => `${value}%`}
        onChange={(event, value) => setPosition(value)}
      />
    </div>
  );
}
