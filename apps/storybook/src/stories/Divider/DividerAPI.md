# API Documentation

- [Divider](#divider)

# Divider

API reference docs for the React Divider component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Divider` component, you can choose to import it directly or through the main entry point.

```jsx
import { Divider } from '@hxnova/react-components/Divider';
// or
import { Divider } from '@hxnova/react-components';
```

## Props

The properties available for the `Divider` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **children** | `ReactNode` | - | The content of the component. |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **orientation** | ``"horizontal" ⏐ "vertical"`` | `'horizontal'` | The component orientation. |
| **slotProps** | ``{ root?: SlotProps<"hr", object, DividerOwnerState> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `DividerSlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **variant** | ``"fullWidth" ⏐ "inset"`` | `'fullWidth'` | The variant to use. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Divider` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaDivider-root | `'hr'` | The component that renders the root. |

## CSS classes

CSS classes for different states and variations of the `Divider` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaDivider-horizontal | `horizontal` | Class name applied to the root element if `orientation="horizontal"`. |
| .NovaDivider-vertical | `vertical` | Class name applied to the root element if `orientation="vertical"`. |
| .NovaDivider-inset | `inset` | Class name applied to the root element if `variant="inset"`. |
| .NovaDivider-fullWidth | `fullWidth` | Class name applied to the root element if `variant="fullWidth"`. |

