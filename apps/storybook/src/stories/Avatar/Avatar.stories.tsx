import { StoryFn, Meta } from '@storybook/react';
import { Avatar as NovaAvatar, AvatarProps } from '@hxnova/react-components/Avatar';
import { AvatarGroup as NovaAvatarGroup } from '@hxnova/react-components/AvatarGroup';
import { Icon } from '@hxnova/icons';
import avatarSrc from '../../assets/avatar.jpeg';

export default {
  title: '@hxnova/react-components/Avatar',
  component: NovaAvatar,
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/tZgUauJZLgk0pX7rHCfxKt/NOVA-Core-Components?node-id=854-61255&p=f&t=Fe0tjpOuk1VIuC6L-0',
    },
  },
  tags: ['!autodocs'],
} as Meta<typeof NovaAvatar>;

type ExtraControls = { type: 'text' | 'icon' | 'image' };
type GroupAvatarControls = { show: number };
const Template: StoryFn<(props: AvatarProps & ExtraControls) => JSX.Element> = ({ children, type, ...args }) => {
  return (
    <NovaAvatar {...args} src={type === 'image' ? avatarSrc : undefined}>
      {type === 'text' && children}
      {type === 'icon' && <Icon family="material" name={'person'} size={args.size == 'large' ? 32 : 24} />}
    </NovaAvatar>
  );
};

const AvatarGroupTemplate: StoryFn<(props: AvatarProps & ExtraControls & GroupAvatarControls) => JSX.Element> = ({
  children,
  type,
  show,
  ...args
}) => {
  return (
    <div style={{ display: 'flex', flexDirection: 'row' }}>
      <NovaAvatarGroup size={args.size}>
        {Array.from(new Array(show).keys()).map((i) => (
          <NovaAvatar key={i} {...args} src={type === 'image' ? avatarSrc : undefined}>
            {type === 'text' && children}
          </NovaAvatar>
        ))}
      </NovaAvatarGroup>
    </div>
  );
};
export const Avatar = {
  render: Template,
  args: {
    color: 'error',
    type: 'text',
    children: 'JB',
    size: 'medium',
    disabled: false,
  },
  argTypes: {
    type: {
      control: { type: 'select' },
      options: ['text', 'icon', 'image'],
    },
    children: {
      name: 'label',
      control: 'text',
    },
    size: {
      control: { type: 'select' },
      options: ['small', 'medium', 'large'],
    },
    color: {
      control: { type: 'radio' },
      options: ['primary', 'error', 'info', 'warning', 'success', undefined],
    },
    disabled: {
      type: 'boolean',
    },
  },
  parameters: {
    controls: {
      include: ['type', 'children', 'size', 'color', 'disabled'],
    },
  },
};

export const AvatarGroup = {
  render: AvatarGroupTemplate,
  args: {
    type: 'text',
    children: 'JB',
    size: 'medium',
    show: 3,
    disabled: false,
  },
  argTypes: {
    type: {
      control: { type: 'select' },
      options: ['text', 'image'],
    },
    children: {
      name: 'label',
      control: 'text',
    },
    size: {
      control: { type: 'select' },
      options: ['small', 'medium', 'large'],
    },
    color: {
      control: { type: 'radio' },
      options: ['primary', 'error', 'info', 'warning', 'success', undefined],
    },
    disabled: {
      type: 'boolean',
    },
  },
  parameters: {
    controls: {
      include: ['type', 'children', 'size', 'color', 'disabled'],
    },
  },
};
