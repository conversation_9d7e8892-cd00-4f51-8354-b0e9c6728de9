import * as React from 'react';
import { Avatar } from '@hxnova/react-components/Avatar';
import { Icon } from '@hxnova/icons';

export default function IconAvatars() {
  return (
    <div sx={{ display: 'flex', flexDirection: 'row', gap: '8px' }}>
      <Avatar>
        <Icon family="material" name={'folder'} size={24} />
      </Avatar>
      <Avatar>
        <Icon family="material" name={'description'} size={24} />
      </Avatar>
      <Avatar>
        <Icon family="material" name={'assignment'} size={24} />
      </Avatar>
    </div>
  );
}
