# API Documentation

- [List](#list)
- [ListDivider](#listdivider)
- [ListItem](#listitem)
- [ListItemButton](#listitembutton)
- [ListItemContent](#listitemcontent)
- [ListItemDecorator](#listitemdecorator)
- [ListSubheader](#listsubheader)

# List

API reference docs for the React List component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `List` component, you can choose to import it directly or through the main entry point.

```jsx
import { List } from '@hxnova/react-components/List';
// or
import { List } from '@hxnova/react-components';
```

## Props

The properties available for the `List` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **density** | ``"standard" ⏐ "compact" ⏐ "comfortable"`` | `'standard'` | The `density` attribute for the ListItem |
| **orientation** | ``"horizontal" ⏐ "vertical"`` | `'vertical'` | The component orientation. |
| **slotProps** | ``{ root?: SlotProps<"ul", object, ListOwnerState> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `ListSlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **wrap** | ``false ⏐ true`` | `false` | Only for horizontal list.<br>If `true`, the list sets the flex-wrap to "wrap" and adjust margin to have gap-like behavior (will move to `gap` in the future). |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `List` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaList-root | `'ul'` | The component that renders the root. |

## CSS classes

CSS classes for different states and variations of the `List` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaList-densityStandard | `densityStandard` | Class name applied to the root element if `density="standard"`. |
| .NovaList-densityCompact | `densityCompact` | Class name applied to the root element if `density="compact"`. |
| .NovaList-densityComfortable | `densityComfortable` | Class name applied to the root element if `density="comfortable"`. |

<br><br>

# ListDivider

API reference docs for the React ListDivider component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `ListDivider` component, you can choose to import it directly or through the main entry point.

```jsx
import { ListDivider } from '@hxnova/react-components/ListDivider';
// or
import { ListDivider } from '@hxnova/react-components';
```

## Props

The properties available for the `ListDivider` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **orientation** | ``"horizontal" ⏐ "vertical"`` | `'horizontal'` | The component orientation. |
| **slotProps** | ``{ root?: SlotProps<"li", object, ListDividerOwnerState> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `ListDividerSlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **variant** | ``"context" ⏐ "gutter" ⏐ "startDecorator" ⏐ "startContent"`` | `'context'` | The empty space on the side(s) of the divider in a vertical list.<br>For horizontal list (the nearest parent List has `row` prop set to `true`), only `inset="gutter"` affects the list divider. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `ListDivider` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaListDivider-root | `'li'` | The component that renders the root. |

## CSS classes

CSS classes for different states and variations of the `ListDivider` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaListDivider-gutter | `gutter` | Class name applied to the root element if `variant="gutter"`. |
| .NovaListDivider-startDecorator | `startDecorator` | Class name applied to the root element if `variant="startDecorator"`. |
| .NovaListDivider-startContent | `startContent` | Class name applied to the root element if `variant="startContent"`. |
| .NovaListDivider-horizontal | `horizontal` | Class name applied to the root element if `orientation="horizontal"`. |
| .NovaListDivider-vertical | `vertical` | Class name applied to the root element if `orientation="vertical"`. |

<br><br>

# ListItem

API reference docs for the React ListItem component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `ListItem` component, you can choose to import it directly or through the main entry point.

```jsx
import { ListItem } from '@hxnova/react-components/ListItem';
// or
import { ListItem } from '@hxnova/react-components';
```

## Props

The properties available for the `ListItem` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **children** | `ReactNode` | - | The content of the component. |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **disabled** | ``false ⏐ true`` | `false` | Whether the component should ignore user interaction. |
| **nested** | ``false ⏐ true`` | `false` | If `true`, the component can contain NestedList. |
| **slotProps** | ``{ root?: SlotProps<"li", object, ListItemOwnerState> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `ListItemSlots` | `{}` | The components used for each slot inside. |
| **sticky** | ``false ⏐ true`` | `false` | If `true`, the component has sticky position (with top = 0). |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `ListItem` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaListItem-root | `'li'` | The component that renders the root. |

## CSS classes

CSS classes for different states and variations of the `ListItem` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .Nova-disabled | `disabled` | Class name applied to the inner `component` element if `disabled={true}`. |
| .NovaListItem-nested | `nested` | Class name applied to the root element, if `nested={true}`. |
| .NovaListItem-nesting | `nesting` | Class name applied to the root element, if it is under a nested list item. |
| .NovaListItem-sticky | `sticky` | Class name applied to the root element, if `sticky={true}`. |

<br><br>

# ListItemButton

API reference docs for the React ListItemButton component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `ListItemButton` component, you can choose to import it directly or through the main entry point.

```jsx
import { ListItemButton } from '@hxnova/react-components/ListItemButton';
// or
import { ListItemButton } from '@hxnova/react-components';
```

## Props

The properties available for the `ListItemButton` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **action** | ``Ref<{ focusVisible(): void; }>`` | - | A ref for imperative actions. It currently only supports `focusVisible()` action. |
| **autoFocus** | ``false ⏐ true`` | `false` | If `true`, the list item is focused during the first mount.<br>Focus will also be triggered if the value changes from false to true. |
| **children** | `ReactNode` | - | The content of the component. |
| **disabled** | ``false ⏐ true`` | `false` | If `true`, the component is disabled. |
| **focusVisibleClassName** | `string` | - | This prop can help identify which element has keyboard focus.<br>The class name will be applied when the element gains the focus through keyboard interaction. |
| **orientation** | ``"horizontal" ⏐ "vertical"`` | `'horizontal'` | The content direction flow. |
| **ref** | ``((instance: HTMLAnchorElement ⏐ null) => void) ⏐ RefObject<HTMLAnchorElement> ⏐ null`` | - |  |
| **selected** | ``false ⏐ true`` | `false` | If `true`, the component is selected. |
| **slotProps** | ``{ root?: SlotProps<"div", object, ListItemButtonOwnerState> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `ListItemButtonSlots` | `{}` | The components used for each slot inside. |
| **tabIndex** | `number` | `0` |  |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `ListItemButton` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaListItemButton-root | `'div'` | The component that renders the root. |

## CSS classes

CSS classes for different states and variations of the `ListItemButton` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaListItemButton-vertical | `vertical` | Class name applied to the root element, if `orientation="vertical"`. |
| .NovaListItemButton-horizontal | `horizontal` | Class name applied to the root element, if `orientation="horizontal"`. |
| .Nova-focusVisible | `focusVisible` | State class applied to the `component`'s `focusVisibleClassName` prop. |
| .Nova-disabled | `disabled` | State class applied to the inner `component` element if `disabled={true}`. |
| .Nova-selected | `selected` | State class applied to the root element if `selected={true}`. |

<br><br>

# ListItemContent

API reference docs for the React ListItemContent component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `ListItemContent` component, you can choose to import it directly or through the main entry point.

```jsx
import { ListItemContent } from '@hxnova/react-components/ListItemContent';
// or
import { ListItemContent } from '@hxnova/react-components';
```

## Props

The properties available for the `ListItemContent` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **inset** | ``false ⏐ true`` | `false` | If `true`, the children are indented.<br>This should be used if there is no left avatar or left icon. |
| **primary** | `ReactNode` | - | The main content element. |
| **secondary** | `ReactNode` | - | The secondary content element. |
| **slotProps** | ``{ root?: SlotProps<"div", object, ListItemContentOwnerState> ⏐ undefined; primary?: SlotProps<"span", TypographyProps, ListItemContentOwnerState> ⏐ undefined; secondary?: SlotProps<...> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `ListItemContentSlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `ListItemContent` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaListItemContent-root | `'div'` | The component that renders the root. |
| primary | .NovaListItemContent-primary | `Typography` | The component that renders the primary slot. |
| secondary | .NovaListItemContent-secondary | `Typography` | The component that renders the secondary slot. |

## CSS classes

CSS classes for different states and variations of the `ListItemContent` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaListItemContent-dense | `dense` | Class name applied to the Typography component if dense. |

<br><br>

# ListItemDecorator

API reference docs for the React ListItemDecorator component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `ListItemDecorator` component, you can choose to import it directly or through the main entry point.

```jsx
import { ListItemDecorator } from '@hxnova/react-components/ListItemDecorator';
// or
import { ListItemDecorator } from '@hxnova/react-components';
```

## Props

The properties available for the `ListItemDecorator` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **slotProps** | ``{ root?: SlotProps<"span", object, ListItemDecoratorOwnerState> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `ListItemDecoratorSlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `ListItemDecorator` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaListItemDecorator-root | `'span'` | The component that renders the root. |

<br><br>

# ListSubheader

API reference docs for the React ListSubheader component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `ListSubheader` component, you can choose to import it directly or through the main entry point.

```jsx
import { ListSubheader } from '@hxnova/react-components/ListSubheader';
// or
import { ListSubheader } from '@hxnova/react-components';
```

## Props

The properties available for the `ListSubheader` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **slotProps** | ``{ root?: SlotProps<"div", object, ListSubheaderOwnerState> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `ListSubheaderSlots` | `{}` | The components used for each slot inside. |
| **sticky** | ``false ⏐ true`` | `false` | If `true`, the component has sticky position (with top = 0). |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `ListSubheader` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaListSubheader-root | `'div'` | The component that renders the root. |

## CSS classes

CSS classes for different states and variations of the `ListSubheader` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaListSubheader-sticky | `sticky` | Class name applied to the root element, if sticky={true}. |

