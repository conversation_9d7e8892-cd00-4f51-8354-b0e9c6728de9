import React from 'react';
import { List } from '@hxnova/react-components/List';
import { ListItem } from '@hxnova/react-components/ListItem';
import { ListItemButton } from '@hxnova/react-components/ListItemButton';
import { ListItemDecorator } from '@hxnova/react-components/ListItemDecorator';
import { Typography } from '@hxnova/react-components/Typography';
import { Icon } from '@hxnova/icons';

export default function SizesList() {
  return (
    <div
      sx={{
        flexGrow: 1,
        display: 'flex',
        justifyContent: 'center',
        gap: '20px',
        flexWrap: 'wrap',
        // '& > *': { minWidth: 0, flexBasis: 200 },
      }}
    >
      {(['compact', 'standard', 'comfortable'] as const).map((density) => (
        <div key={density}>
          <Typography sx={{ marginBottom: '8px' }}>
            <code>density=&quot;{density}&quot;</code>
          </Typography>
          <List density={density} sx={{ maxWidth: 300, borderRadius: '4px' }}>
            <ListItem>
              <ListItemButton>
                <ListItemDecorator>
                  <Icon family="material" name="home" size={24} />
                </ListItemDecorator>
                Home
              </ListItemButton>
            </ListItem>
            <ListItem>
              <ListItemButton>Projects </ListItemButton>
            </ListItem>
            <ListItem>
              <ListItemButton>Settings </ListItemButton>
            </ListItem>
          </List>
        </div>
      ))}
    </div>
  );
}
