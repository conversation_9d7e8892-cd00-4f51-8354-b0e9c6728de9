import React from 'react';
import { List } from '@hxnova/react-components/List';
import { ListItem } from '@hxnova/react-components/ListItem';
import { ListItemButton } from '@hxnova/react-components/ListItemButton';
import { ListItemDecorator } from '@hxnova/react-components/ListItemDecorator';
import { ListItemContent } from '@hxnova/react-components/ListItemContent';
import { Avatar } from '@hxnova/react-components/Avatar';
import { Icon } from '@hxnova/icons';

export default function FolderList() {
  return (
    <List sx={{ maxWidth: 360 }}>
      <ListItem>
        <ListItemButton>
          <ListItemDecorator>
            <Avatar>
              <Icon family="material" name="image" size={24} />
            </Avatar>
          </ListItemDecorator>
          <ListItemContent primary="Photos" secondary="Jan 9, 2014" />
        </ListItemButton>
      </ListItem>
      <ListItem>
        <ListItemButton>
          <ListItemDecorator>
            <Avatar>
              <Icon family="material" name="work" size={24} />
            </Avatar>
          </ListItemDecorator>
          <ListItemContent primary="Work" secondary="Jan 7, 2014" />
        </ListItemButton>
      </ListItem>
      <ListItem>
        <ListItemButton>
          <ListItemDecorator>
            <Avatar>
              <Icon family="material" name="beach_access" size={24} />
            </Avatar>
          </ListItemDecorator>
          <ListItemContent primary="Vacation" secondary="July 20, 2014" />
        </ListItemButton>
      </ListItem>
    </List>
  );
}
