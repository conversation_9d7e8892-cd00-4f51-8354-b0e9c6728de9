import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { Badge as NovaBadge } from '@hxnova/react-components/Badge';
import { Icon } from '@hxnova/icons';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
  title: '@hxnova/react-components/Badge',
  component: NovaBadge,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'centered',
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/tZgUauJZLgk0pX7rHCfxKt/NOVA-Core-Components?node-id=1194-261&p=f&t=WnVuh2OQIurjyXbe-0',
    },
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['!autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  // argTypes: {
  //   backgroundColor: { control: 'color' },
  // },
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
  // args: { onClick: fn() },
} satisfies Meta<typeof NovaBadge>;

export default meta;
type Story = StoryObj<typeof meta>;

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Badge: Story = {
  args: {
    size: 'large',
    color: 'primary',
    disabled: false,
    badgeContent: 10,
    max: 99,
    invisible: false,
    showZero: false,
    anchorOrigin: {
      horizontal: 'right',
      vertical: 'top',
    },
    children: <Icon family="material" name="mail" size={24} />,
  },
  argTypes: {
    size: {
      control: { type: 'radio' },
      options: ['small', 'large'],
    },
    color: {
      control: { type: 'radio' },
      options: ['primary', 'error', 'info', 'warning', 'success'],
    },
  },
};
