# API Documentation

- [Chip](#chip)

# Chip

API reference docs for the React Chip component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Chip` component, you can choose to import it directly or through the main entry point.

```jsx
import { Chip } from '@hxnova/react-components/Chip';
// or
import { Chip } from '@hxnova/react-components';
```

## Props

The properties available for the `Chip` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **label*** | `string` | - | The text label to show in the chip |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **disabled** | ``false ⏐ true`` | `false` | Whether or not the chip is disabled |
| **endIcon** | ``ReactElement<any, string ⏐ JSXElementConstructor<any>>`` | - | Optional icon to show after the label |
| **selected** | ``false ⏐ true`` | - | Whether or not the chip is selected/active |
| **size** | ``"small" ⏐ "medium" ⏐ "large"`` | `'medium'` | The size of the chip |
| **startIcon** | ``ReactElement<any, string ⏐ JSXElementConstructor<any>>`` | - | Optional icon to show before the label |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## CSS classes

CSS classes for different states and variations of the `Chip` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaChip-root | `root` | Class name applied to the root element. |
| .NovaChip-sizeSmall | `sizeSmall` | Class name applied to the Button if `size="small"`. |
| .NovaChip-sizeMedium | `sizeMedium` | Class name applied to the Button if `size="medium"`. |
| .NovaChip-sizeLarge | `sizeLarge` | Class name applied to the Button if `size="large"`. |
| .NovaChip-iconStart | `iconStart` | Class name applied to the Button startIcon element. |
| .NovaChip-iconEnd | `iconEnd` | Class name applied to the Button endIcon element. |
| .Nova-disabled | `disabled` | Class name applied to the Button if `disabled={true}` |
| .Nova-selected | `selected` | Class name applied to the Button if `selected={true}` |

