import type { Meta, StoryFn } from '@storybook/react';
import { ChipProps, Chip as NovaChip } from '@hxnova/react-components/Chip';
import { Icon } from '@hxnova/icons';

const meta = {
  title: '@hxnova/react-components/Chip',
  component: NovaChip,
  parameters: {
    layout: 'centered',
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/tZgUauJZLgk0pX7rHCfxKt/NOVA-Core-Components?node-id=1142-4326&p=f&t=oBwuvTkaSwgyMutI-0',
    },
  },
  tags: ['!autodocs'],
} satisfies Meta<typeof NovaChip>;

export default meta;

const ChipTemplate: StoryFn<
  (
    props: Omit<ChipProps, 'startIcon' | 'endIcon'> & {
      showStartIcon: boolean;
      showEndIcon: boolean;
      clickable: boolean;
    },
  ) => JSX.Element
> = ({ showStartIcon, showEndIcon, clickable, ...other }) => {
  return (
    <NovaChip
      {...other}
      onClick={clickable ? () => {} : undefined}
      endIcon={showEndIcon ? <Icon family="material" name="clear" size={24} /> : undefined}
      startIcon={showStartIcon ? <Icon family="material" name="download" size={24} /> : undefined}
    />
  );
};

export const Chip = {
  render: ChipTemplate,
  args: {
    label: 'Download',
    selected: false,
    size: 'medium',
    disabled: false,
    showStartIcon: true,
    showEndIcon: false,
    clickable: true,
  },
  parameters: {
    controls: { exclude: ['startIcon', 'endIcon'] },
  },
};
