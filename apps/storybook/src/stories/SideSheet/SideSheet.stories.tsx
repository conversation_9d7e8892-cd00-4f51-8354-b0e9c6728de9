import React, { useState } from 'react';
import { StoryFn, Meta } from '@storybook/react';
import { SideSheet, SideSheetProps } from '@hxnova/react-components/SideSheet';
import { Button } from '@hxnova/react-components/Button';
import { Typography } from '@hxnova/react-components/Typography';
import { Icon } from '@hxnova/icons';
import { IconButton } from '@hxnova/react-components/IconButton';
import { Divider } from '@hxnova/react-components/Divider';

export default {
  title: '@hxnova/react-components/SideSheet',
  component: SideSheet.Root,
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/tZgUauJZLgk0pX7rHCfxKt/NOVA-Core-Components?node-id=4594-6319&t=eR1DZjowLMTEDYxg-0',
    },
  },
  tags: ['!autodocs'],
} as Meta<typeof SideSheet.Root>;

const Template: StoryFn<SideSheetProps> = (args) => {
  const [open, setOpen] = useState(false);

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <div>
      <Button onClick={() => setOpen(true)}>Open Side Sheet</Button>
      <SideSheet.Root {...args} open={open} onClose={handleClose}>
        <SideSheet.Header>
          <IconButton variant="neutral" onClick={handleClose}>
            <Icon family="material" name="arrow_back" size={24} />
          </IconButton>
          <Typography variant="titleLarge">Side Sheet Title</Typography>
          <IconButton variant="neutral" onClick={handleClose}>
            <Icon family="material" name="close" size={24} />
          </IconButton>
        </SideSheet.Header>
        <SideSheet.Content>
          <Typography>
            This is a modal side sheet that appears from the edge of the screen. It contains supplementary content and
            actions. Side sheets are commonly used for form input, filtering content, or displaying details.
          </Typography>
          <br />
          <Typography>Click outside the sheet, press Escape, or use the close button to dismiss it.</Typography>
        </SideSheet.Content>
        <Divider variant="inset" />
        <SideSheet.Footer>
          <Button variant="outlined" onClick={handleClose}>
            Cancel
          </Button>
          <Button onClick={handleClose}>Confirm</Button>
        </SideSheet.Footer>
      </SideSheet.Root>
    </div>
  );
};

export const Basic = {
  render: Template,
  args: {
    anchor: 'right',
    width: 360,
  },
  argTypes: {
    anchor: {
      control: { type: 'radio' },
      options: ['left', 'right'],
      description: 'Side from which the side sheet will appear',
    },
    width: {
      control: { type: 'number' },
      description: 'Width of the side sheet in pixels',
    },
  },
  parameters: {
    controls: {
      include: ['anchor', 'width'],
    },
  },
};
