import React, { useState } from 'react';
import { Button } from '@hxnova/react-components/Button';
import { Dialog } from '@hxnova/react-components/Dialog';
import { Typography, TextField, IconButton } from '@hxnova/react-components';
import { Icon } from '@hxnova/icons';

export default function FullScreenExample() {
  const [open, setOpen] = useState(false);

  const handleClose = () => {
    setOpen(false);
  };
  return (
    <>
      <Button
        variant="outlined"
        onClick={() => {
          setOpen(true);
        }}
      >
        Open a full screen dialog
      </Button>
      <Dialog.Root open={open} onClose={handleClose} fullScreen>
        <Dialog.Header>
          <div sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
            <IconButton variant="standard" onClick={handleClose}>
              <Icon family="material" name="close" size={24} />
            </IconButton>
            <Typography
              variant="titleMedium"
              sx={{
                fontWeight: 400,
                color: 'var(--palette-onSurface)',
              }}
            >
              Create project
            </Typography>
            <Button variant="filled" onClick={handleClose}>
              Save
            </Button>
          </div>
        </Dialog.Header>
        <Dialog.Content sx={{ gap: 16 }}>
          <TextField label="Project name" placeholder="Project name" fullWidth />
          <TextField label="Project description" placeholder="Project description" fullWidth />
          <TextField label="Start date" placeholder="Start date" fullWidth />
          <TextField label="End date" placeholder="End date" fullWidth />
        </Dialog.Content>
      </Dialog.Root>
    </>
  );
}
