import { useState } from 'react';
import { Button } from '@hxnova/react-components/Button';
import { Dialog } from '@hxnova/react-components/Dialog';
import { Avatar, Typography } from '@hxnova/react-components';
import { Icon } from '@hxnova/icons';

export default function ContentExample() {
  const [open, setOpen] = useState(false);

  const handleClose = () => {
    setOpen(false);
  };
  return (
    <>
      <Button
        variant="outlined"
        onClick={() => {
          setOpen(true);
        }}
      >
        Open a dialog with content
      </Button>
      <Dialog.Root open={open} onClose={handleClose}>
        <Dialog.Header
          supportingText={
            'This will reset your app preferences back to their default settings. The following accounts will also be signed out:'
          }
          icon={<Icon family="material" name="restart_alt" size={24} />}
        >
          Reset settings?
        </Dialog.Header>
        <Dialog.Content
          topDivider
          bottomDivider
          sx={{
            gap: 16,
            color: 'var(--palette-onSurfaceVariant)',
          }}
        >
          <div sx={{ display: 'flex', alignItems: 'center', gap: 16 }}>
            <Avatar color="info">JC</Avatar>
            <Typography variant="bodySmall"><EMAIL></Typography>
          </div>
          <div sx={{ display: 'flex', alignItems: 'center', gap: 16 }}>
            <Avatar color="warning">CW</Avatar>
            <Typography variant="bodySmall"><EMAIL></Typography>
          </div>
          <div sx={{ display: 'flex', alignItems: 'center', gap: 16 }}>
            <Avatar color="error">EH</Avatar>
            <Typography variant="bodySmall"><EMAIL></Typography>
          </div>
        </Dialog.Content>
        <Dialog.Actions>
          <Button variant="text" onClick={handleClose}>
            Cancel
          </Button>
          <Button variant="filled" onClick={handleClose}>
            Accept
          </Button>
        </Dialog.Actions>
      </Dialog.Root>
    </>
  );
}
