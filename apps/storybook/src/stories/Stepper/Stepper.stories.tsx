import { StoryFn, Meta } from '@storybook/react';
import { Stepper as NovaStepper } from '@hxnova/react-components/Stepper';
import { Step } from '@hxnova/react-components/Step';
import { StepperProps } from '@hxnova/react-components/Stepper';
import { Icon } from '@hxnova/icons';
import VerticalExample from './Examples/VerticalExample';

export default {
  title: '@hxnova/react-components/Stepper',
  component: NovaStepper,
  parameters: {
    layout: 'centered',
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/tZgUauJZLgk0pX7rHCfxKt/NOVA-Core-Components?m=auto&node-id=3496-10981&t=lwWc1IYu6HkKWLi1-1',
    },
  },
  tags: ['!autodocs'],
} as Meta<typeof NovaStepper>;

const Template: StoryFn<typeof NovaStepper> = (args: StepperProps) => (
  <div style={{ width: '600px' }}>
    <NovaStepper {...args}>
      <Step completed icon={<Icon family="material" name="check" size={24} />}>
        Step 1
      </Step>
      <Step active icon="2">
        Step 2
      </Step>
      <Step icon="3">Step 3</Step>
    </NovaStepper>
  </div>
);

export const Horizontal = {
  render: Template,
  args: {
    orientation: 'horizontal',
  },
  argTypes: {
    orientation: {
      control: { type: 'radio' },
      options: ['horizontal', 'vertical'],
    },
  },
  parameters: {
    controls: {
      include: ['orientation'],
    },
  },
};

export const Vertical = {
  render: Template,
  args: {
    orientation: 'vertical',
  },
  argTypes: {
    orientation: {
      control: { type: 'radio' },
      options: ['horizontal', 'vertical'],
    },
  },
  parameters: {
    controls: {
      include: ['orientation'],
    },
  },
};

export const VerticalComplex = {
  render: () => <VerticalExample />,
  parameters: {
    controls: { disable: true },
  },
};
