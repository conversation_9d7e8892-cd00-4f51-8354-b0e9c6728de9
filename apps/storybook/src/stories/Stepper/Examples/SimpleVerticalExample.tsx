import { Stepper } from '@hxnova/react-components/Stepper';
import { Step } from '@hxnova/react-components/Step';
import { Icon } from '@hxnova/icons';
import { Typography } from '@hxnova/react-components/Typography';

export default function SimpleVerticalExample() {
  return (
    <div sx={{ display: 'flex', gap: '300px', alignItems: 'flex-start', justifyContent: 'flex-start' }}>
      <div>
        <Typography variant="titleSmall" sx={{ marginBottom: '8px' }}>
          Vertical Label Adjacent (Default)
        </Typography>
        <Stepper orientation="vertical">
          <Step completed icon={<Icon family="material" name="check" size={20} />}>
            Step 1
          </Step>
          <Step active icon="2">
            Step 2
          </Step>
          <Step icon="3">Step 3</Step>
        </Stepper>
      </div>

      <div>
        <Typography variant="titleSmall" sx={{ marginBottom: '8px' }}>
          Vertical Label Below
        </Typography>
        <Stepper orientation="vertical">
          <Step orientation="vertical" completed icon={<Icon family="material" name="check" size={20} />}>
            Step 1
          </Step>
          <Step orientation="vertical" active icon="2">
            Step 2
          </Step>
          <Step orientation="vertical" icon="3">
            Step 3
          </Step>
        </Stepper>
      </div>
    </div>
  );
}
