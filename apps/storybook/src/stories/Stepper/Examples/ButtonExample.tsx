import { useState } from 'react';
import { Step } from '@hxnova/react-components/Step';
import { Stepper } from '@hxnova/react-components/Stepper';
import { StepButton } from '@hxnova/react-components/StepButton';
import { Icon } from '@hxnova/icons';

export default function ButtonExample() {
  const [activeStep, setActiveStep] = useState(1);
  const steps = ['Order placed', 'In review', 'Approved'];

  return (
    <Stepper sx={{ width: '100%' }}>
      {steps.map((step, index) => (
        <Step
          key={step}
          active={activeStep === index}
          completed={activeStep > index}
          icon={activeStep > index ? <Icon family="material" name="check" size={20} /> : index + 1}
          style={
            activeStep > index && index !== 2 ? { '--nova-step-connectorBg': 'var(--palette-primary)' } : undefined
          }
        >
          <StepButton onClick={() => setActiveStep(index)}>{step}</StepButton>
        </Step>
      ))}
    </Stepper>
  );
}
