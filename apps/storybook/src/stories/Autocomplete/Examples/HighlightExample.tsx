import { Autocomplete } from '@hxnova/react-components/Autocomplete';
import { TextField } from '@hxnova/react-components/TextField';
import { MenuItem, MenuItemProps } from '@hxnova/react-components/MenuItem';
import parse from 'autosuggest-highlight/parse';
import match from 'autosuggest-highlight/match';
import { styled } from '@pigment-css/react';

const topFilms = [
  { title: 'The Shawshank Redemption', year: 1994 },
  { title: 'The Godfather', year: 1972 },
  { title: 'The Godfather: Part II', year: 1974 },
  { title: 'The Dark Knight', year: 2008 },
  { title: '12 Angry Men', year: 1957 },
  { title: "Schindler's List", year: 1993 },
  { title: 'Pulp Fiction', year: 1994 },
];

const StyledMenuItem = styled(MenuItem)(() => ({
  '&.Nova-focused, &.Nova-focusVisible': {
    backgroundColor:
      'color-mix(in srgb, var(--palette-backgroundStates), var(--palette-onSurface) var(--palette-stateLayers-focusOnSurface))',
  },

  '&[aria-selected="true"]': {
    backgroundColor: 'var(--palette-secondaryContainer)',
    '&.Nova-focused, &.Nova-focusVisible': {
      backgroundColor:
        'color-mix(in srgb, var(--palette-surfaceContainerHighest), var(--palette-onSurface) var(--palette-stateLayers-focusOnSurface))',
    },
  },
}));

export default function Demo() {
  return (
    <Autocomplete
      sx={{ minWidth: '400px' }}
      options={topFilms}
      getOptionLabel={(option) => option.title}
      renderInput={(params) => <TextField {...params} label="Highlights" placeholder="Placeholder" />}
      renderOption={(props, option, { inputValue }) => {
        const { key, ...optionProps } = props;
        const matches = match(option.title, inputValue, { insideWords: true });
        const parts = parse(option.title, matches);
        return (
          <StyledMenuItem component={'li'} key={key} {...(optionProps as MenuItemProps)}>
            <div>
              {parts.map((part, index) => (
                <span
                  key={index}
                  style={{
                    fontWeight: part.highlight ? 700 : 400,
                  }}
                >
                  {part.text}
                </span>
              ))}
            </div>
          </StyledMenuItem>
        );
      }}
    />
  );
}
