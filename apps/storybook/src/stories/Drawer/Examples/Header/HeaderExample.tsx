import React from 'react';
import { Drawer } from '@hxnova/react-components/Drawer';
import { IconButton } from '@hxnova/react-components/IconButton';
import { Icon } from '@hxnova/icons';
import NexusCoreIcon from '@nexusui/branding/NexusCore';

export default function Demo() {
  return (
    <Drawer.Header
      sx={{ width: 360, '& .NovaDrawerHeader-container': { width: '100%' } }}
      productLogo={<NexusCoreIcon height={40} width={40} />}
      pageTitle={'Product name'}
      endDecorator={
        <IconButton
          sx={(theme) => ({
            color: theme.vars.palette.onSurfaceVariant,
          })}
          variant={'standard'}
          color={'primary'}
        >
          {<Icon family="material" name="menu_open" onClick={() => {}} />}
        </IconButton>
      }
    />
  );
}
