import React from 'react';

import { Drawer } from '@hxnova/react-components/Drawer';
import { Button } from '@hxnova/react-components/Button';

export default function Demo() {
  const [openTemporary, setOpenTemporary] = React.useState(false);

  return (
    <div
      sx={{
        display: 'flex',
        flexDirection: 'row',
      }}
    >
      <Button sx={{ margin: 16 }} onClick={() => setOpenTemporary(true)}>
        Open Temporary Drawer
      </Button>
      <Drawer.Root open={openTemporary} onClose={() => setOpenTemporary(false)}>
        <span>Temporary Drawer</span>
      </Drawer.Root>
    </div>
  );
}
