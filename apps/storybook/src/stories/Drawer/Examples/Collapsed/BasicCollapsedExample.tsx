import React from 'react';

import { Drawer } from '@hxnova/react-components/Drawer';
import { ListItem } from '@hxnova/react-components/ListItem';
import { ListItemDecorator } from '@hxnova/react-components/ListItemDecorator';
import { Avatar } from '@hxnova/react-components/Avatar';
import { Typography } from '@hxnova/react-components/Typography';
import { ListItemContent } from '@hxnova/react-components/ListItemContent';
import { List } from '@hxnova/react-components/List';
import { IconButton } from '@hxnova/react-components/IconButton';
import { Divider } from '@hxnova/react-components/Divider';

import NexusCoreIcon from '@nexusui/branding/NexusCore';
import { Icon } from '@hxnova/icons';

import avatarSrc from '../../../../assets/avatar.jpeg';

export default function Demo() {
  const [expanded, setExpanded] = React.useState(false);
  const [activeItem, setActiveItem] = React.useState('1-1');
  const handleItemSelect = (itemId: string) => setActiveItem(itemId);

  return (
    <div sx={{ minHeight: 600, display: 'flex', flexDirection: 'row', position: 'relative', overflow: 'hidden' }}>
      <Drawer.Root
        variant={'permanent'}
        expanded={expanded}
        activeItem={activeItem}
        onItemSelect={handleItemSelect}
        sx={{
          '& > .NovaDrawerRoot-container': { position: 'absolute' },
          '& > .NovaDrawerRoot-subNavContainer': { position: 'absolute' },
        }}
      >
        <Drawer.Header
          productLogo={<NexusCoreIcon height={40} width={40} />}
          pageTitle={'Product name'}
          endDecorator={
            <IconButton
              sx={(theme) => ({
                color: theme.vars.palette.onSurfaceVariant,
              })}
              variant={'standard'}
              color={'primary'}
              onClick={() => {
                setExpanded((e) => !e);
              }}
            >
              {expanded ? (
                <Icon family="material" name="left_panel_close" />
              ) : (
                <Icon family="material" name="left_panel_open" />
              )}
            </IconButton>
          }
        />
        <Drawer.Body>
          <Drawer.NavGroup>
            <Drawer.NavItem itemId="1-1" label="Dashboard" startDecorator={<Icon family="material" name="home" />} />
            <Drawer.NavItem itemId="2-1" label="Label" startDecorator={<Icon family="material" name="settings" />} />
            <Drawer.NavItem
              itemId="3"
              label="Label"
              startDecorator={<Icon family="material" name="person" />}
              trailingLabel={'999+'}
              trailingBadge={{
                badgeContent: '99',
                color: 'error',
              }}
            />
          </Drawer.NavGroup>
          <Divider />
          <Drawer.NavGroup>
            <Drawer.NavItem itemId="4" label="Label" startDecorator={<Icon family="material" name="home" />} />
            <Drawer.NavItem itemId="5" label="Label" startDecorator={<Icon family="material" name="settings" />} />
          </Drawer.NavGroup>
        </Drawer.Body>
        <Drawer.Footer>
          <Divider />
          <Drawer.NavGroup>
            <Drawer.NavItem itemId="6" label="Label" startDecorator={<Icon family="material" name="home" />} />
            <Drawer.NavItem itemId="7" label="Label" startDecorator={<Icon family="material" name="settings" />} />
          </Drawer.NavGroup>
          <List sx={{ height: '72px', justifyContent: 'center' }}>
            <ListItem sx={{ paddingInline: '0.75rem' }}>
              <ListItemDecorator>
                <Avatar src={avatarSrc} color={'error'}></Avatar>
              </ListItemDecorator>
              {expanded && (
                <>
                  <ListItemContent primary="Headline" secondary="Supporting text" />
                  <ListItemDecorator>
                    <Typography variant="bodySmall">100+</Typography>
                    <Icon family="material" name="keyboard_arrow_right" />
                  </ListItemDecorator>
                </>
              )}
            </ListItem>
          </List>
        </Drawer.Footer>
      </Drawer.Root>
    </div>
  );
}
