import React from 'react';

import { Drawer } from '@hxnova/react-components/Drawer';

import { Icon } from '@hxnova/icons';

export default function Demo() {
  return (
    <div>
      <Drawer.Root variant={'permanent'} expanded>
        <Drawer.Body>
          <Drawer.NavGroup>
            <Drawer.NavItem label="Label" startDecorator={<Icon family="material" name="group" />}>
              <Drawer.NavItem itemId="3" label="Label" startDecorator={<Icon family="material" name="star" />} />
              <Drawer.NavItem itemId="4" label="Label" startDecorator={<Icon family="material" name="star" />} />
            </Drawer.NavItem>
          </Drawer.NavGroup>
        </Drawer.Body>
      </Drawer.Root>
    </div>
  );
}
