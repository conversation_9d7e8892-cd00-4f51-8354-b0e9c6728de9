import React from 'react';
import { Drawer } from '@hxnova/react-components/Drawer';
import { Icon } from '@hxnova/icons';

export default function Demo() {
  return (
    <Drawer.NavItem
      sx={{ maxWidth: 360 }}
      label="Label"
      trailingLabel={'999+'}
      trailingBadge={{
        badgeContent: '99',
        color: 'error',
      }}
      trailingAction={<Icon family="material" name="keyboard_arrow_right" size={24} />}
    />
  );
}
