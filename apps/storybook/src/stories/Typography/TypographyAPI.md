# API Documentation

- [Typography](#typography)

# Typography

API reference docs for the React Typography component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Typography` component, you can choose to import it directly or through the main entry point.

```jsx
import { Typography } from '@hxnova/react-components/Typography';
// or
import { Typography } from '@hxnova/react-components';
```

## Props

The properties available for the `Typography` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **noWrap** | ``false ⏐ true`` | - | Prevent the component text from wrapping to multiple lines |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **variant** | ``"displayLarge" ⏐ "displayMedium" ⏐ "displaySmall" ⏐ "headlineLarge" ⏐ "headlineMedium" ⏐ "headlineSmall" ⏐ "titleLarge" ⏐ "titleMedium" ⏐ "titleSmall" ⏐ "bodyLarge" ⏐ "bodyMedium" ⏐ "bodySmall" ⏐ "labelLarge" ⏐ "labelMedium" ⏐ "labelSmall" ⏐ "inherit"`` | `'bodyMedium'` | The typography style to use |
| **variantMapping** | `ElementType` | `{ { displayLarge: 'h1', displayMedium: 'h2', displaySmall: 'h3', headlineLarge: 'h4', headlineMedium: 'h5', headlineSmall: 'h6', titleLarge: 'h6', titleMedium: 'h6', titleSmall: 'h6', bodyLarge: 'p', bodyMedium: 'p', bodySmall: 'p', labelLarge: 'span', labelMedium: 'span', labelSmall: 'span', }; }` | A mapping of variants to HTML elements to use for that variant |

## CSS classes

CSS classes for different states and variations of the `Typography` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaTypography-root | `root` | Class name applied to the root element. |
| .NovaTypography-displayLarge | `displayLarge` | Class name applied to the Button if `variant="displayLarge"`. |
| .NovaTypography-displayMedium | `displayMedium` | Class name applied to the Button if `variant="displayMedium"`. |
| .NovaTypography-displaySmall | `displaySmall` | Class name applied to the Button if `variant="displaySmall"`. |
| .NovaTypography-headlineLarge | `headlineLarge` | Class name applied to the Button if `variant="headlineLarge"`. |
| .NovaTypography-headlineMedium | `headlineMedium` | Class name applied to the Button if `variant="headlineMedium"`. |
| .NovaTypography-headlineSmall | `headlineSmall` | Class name applied to the Button if `variant="headlineSmall"`. |
| .NovaTypography-titleLarge | `titleLarge` | Class name applied to the Button if `variant="titleLarge"`. |
| .NovaTypography-titleMedium | `titleMedium` | Class name applied to the Button if `variant="titleMedium"`. |
| .NovaTypography-titleSmall | `titleSmall` | Class name applied to the Button if `variant="titleSmall"`. |
| .NovaTypography-bodyLarge | `bodyLarge` | Class name applied to the Button if `variant="bodyLarge"`. |
| .NovaTypography-bodyMedium | `bodyMedium` | Class name applied to the Button if `variant="bodyMedium"`. |
| .NovaTypography-bodySmall | `bodySmall` | Class name applied to the Button if `variant="bodySmall"`. |
| .NovaTypography-labelLarge | `labelLarge` | Class name applied to the Button if `variant="labelLarge"`. |
| .NovaTypography-labelMedium | `labelMedium` | Class name applied to the Button if `variant="labelMedium"`. |
| .NovaTypography-labelSmall | `labelSmall` | Class name applied to the Button if `variant="labelSmall"`. |
| .NovaTypography-noWrap | `noWrap` | Class name applied if text wrapping is disabled |

