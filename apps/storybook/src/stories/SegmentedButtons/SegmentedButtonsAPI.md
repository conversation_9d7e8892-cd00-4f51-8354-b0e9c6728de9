# API Documentation

- [SegmentedButton](#segmentedbutton)
- [SegmentedButtonGroup](#segmentedbuttongroup)

# SegmentedButton

API reference docs for the React SegmentedButton component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `SegmentedButton` component, you can choose to import it directly or through the main entry point.

```jsx
import { SegmentedButton } from '@hxnova/react-components/SegmentedButton';
// or
import { SegmentedButton } from '@hxnova/react-components';
```

## Props

The properties available for the `SegmentedButton` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **action** | ``Ref<ButtonActions>`` | - | A ref for imperative actions. It currently only supports `focusVisible()` action. |
| **className** | `string` | - |  |
| **component** | `keyof HTMLElementTagNameMap` | `'button'` | The HTML element that is ultimately rendered, for example 'button' or 'a' |
| **disabled** | ``false ⏐ true`` | `false` | If `true`, the component is disabled. |
| **endIcon** | `ReactNode` | - | An optional icon to show at the end of the button |
| **focusableWhenDisabled** | ``false ⏐ true`` | `false` | If `true`, allows a disabled button to receive focus. |
| **fullWidth** | ``false ⏐ true`` | `false` | If `true`, the button will take up the full width of its container. |
| **href** | `string` | - |  |
| **onChange** | ``(event: MouseEvent<HTMLElement, MouseEvent>, value: any) => void`` | - | Callback fired when the state changes.<br>@param event The event source of the callback.<br>@param value of the selected button. |
| **onClick** | ``(event: MouseEvent<HTMLElement, MouseEvent>, value: any) => void`` | - | Callback fired when the button is clicked.<br>@param event The event source of the callback.<br>@param value of the selected button. |
| **onFocusVisible** | ``FocusEventHandler<Element>`` | - |  |
| **ref** | ``((instance: HTMLButtonElement ⏐ null) => void) ⏐ RefObject<HTMLButtonElement> ⏐ null`` | - |  |
| **selected** | ``false ⏐ true`` | `false` | If `true`, the button is rendered in an active state. |
| **size** | ``"small" ⏐ "medium" ⏐ "large"`` | `'medium'` | The overall size of the button. |
| **slotProps** | ``{ root?: SlotComponentProps<"button", ButtonRootSlotPropsOverrides, { action?: Ref<ButtonActions>; children?: ReactNode; ... 13 more ...; focusVisible: boolean; }> ⏐ undefined; } ⏐ undefined`` | `{}` | The props used for each slot inside the Button. |
| **slots** | `ButtonSlots` | `{}` | The components used for each slot inside the Button.<br>Either a string to use a HTML element or a component. |
| **startIcon** | `ReactNode` | - | An optional icon to show at the start of the button |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **tabIndex** | `number` | - |  |
| **to** | `string` | - |  |
| **type** | ``"button" ⏐ "submit" ⏐ "reset"`` | `'button'` | Type attribute applied when the `component` is `button`. |
| **value** | `any` | - | The value to associate with the button when selected in a SegmentedButtonGroup. |

## CSS classes

CSS classes for different states and variations of the `SegmentedButton` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaSegmentedButton-root | `root` | Class name applied to the root element. |
| .Nova-selected | `selected` | Class name applied to the SegmentedButton if `selected={true}`. |

<br><br>

# SegmentedButtonGroup

API reference docs for the React SegmentedButtonGroup component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `SegmentedButtonGroup` component, you can choose to import it directly or through the main entry point.

```jsx
import { SegmentedButtonGroup } from '@hxnova/react-components/SegmentedButtonGroup';
// or
import { SegmentedButtonGroup } from '@hxnova/react-components';
```

## Props

The properties available for the `SegmentedButtonGroup` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **disabled** | ``false ⏐ true`` | `false` | If `true`, the component is disabled. This implies that all SegmentedButton children will be disabled. |
| **exclusive** | ``false ⏐ true`` | `false` | If `true`, only allow one of the child SegmentedButton values to be selected. |
| **onChange** | ``(event: MouseEvent<HTMLElement, MouseEvent>, value: any) => void`` | - | Callback fired when the value changes.<br>@param event The event source of the callback.<br>@param value of the selected buttons. When `exclusive` is true<br>this is a single value; when false an array of selected values. If no value<br>is selected and `exclusive` is true the value is null; when false an empty array. |
| **orientation** | ``"horizontal" ⏐ "vertical"`` | `'horizontal'` | The component orientation. |
| **size** | ``"small" ⏐ "medium" ⏐ "large"`` | `'medium'` | How large the SegmentedButtonGroup contents should be. |
| **slotProps** | ``{ root?: SlotProps<"div", SegmentedButtonGroupRootSlotPropsOverrides, SegmentedButtonGroupOwnerState> ⏐ undefined; }`` | `{}` | The props used for each slot inside. |
| **slots** | `SegmentedButtonGroupSlots` | `{}` | The components used for each slot inside. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **value** | `any` | - | The currently selected value within the group or an array of selected<br>values when `exclusive` is false.<br>The value must have reference equality with the option in order to be selected. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `SegmentedButtonGroup` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaSegmentedButtonGroup-root | `'div'` | The component that renders the root. |

## CSS classes

CSS classes for different states and variations of the `SegmentedButtonGroup` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaSegmentedButtonGroup-sizeMedium | `sizeMedium` | Class name applied to the root element if `size="medium"`. |
| .NovaSegmentedButtonGroup-sizeSmall | `sizeSmall` | Class name applied to the root element if `size="small"`. |
| .NovaSegmentedButtonGroup-sizeLarge | `sizeLarge` | Class name applied to the root element if `size="large"`. |
| .NovaSegmentedButtonGroup-horizontal | `horizontal` | Class name applied to the root element if `orientation="horizontal"`. |
| .NovaSegmentedButtonGroup-vertical | `vertical` | Class name applied to the root element if `orientation="vertical"`. |
| .NovaSegmentedButtonGroup-grouped | `grouped` | Class name applied to the children. |
| .NovaSegmentedButtonGroup-firstButton | `firstButton` | Class name applied to the first button in the segmented button group. |
| .NovaSegmentedButtonGroup-lastButton | `lastButton` | Class name applied to the last button in the segmented button group. |
| .NovaSegmentedButtonGroup-middleButton | `middleButton` | Class name applied to buttons in the middle of the segmented button group. |

