# API Documentation

- [Grow](#grow)

# Grow

API reference docs for the React Grow component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Grow` component, you can choose to import it directly or through the main entry point.

```jsx
import { Grow } from '@hxnova/react-components/Grow';
// or
import { Grow } from '@hxnova/react-components';
```

## Props

The properties available for the `Grow` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **children*** | ``ReactElement<unknown, any>`` | - | A single child content element. |
| **appear** | ``false ⏐ true`` | `true` | Perform the enter transition when it first mounts if `in` is also `true`.<br>Set this to `false` to disable this behavior. |
| **easing** | ``string ⏐ { enter?: string ⏐ undefined; exit?: string ⏐ undefined; }`` | - | The transition timing function.<br>You may specify a single easing or a object containing enter and exit values. |
| **in** | ``false ⏐ true`` | - | If `true`, the component will transition in. |
| **timeout** | ``number ⏐ { appear?: number ⏐ undefined; enter?: number ⏐ undefined; exit?: number ⏐ undefined; } ⏐ { appear?: number ⏐ undefined; enter?: number ⏐ undefined; exit?: number ⏐ undefined; } ⏐ "auto"`` | `'auto'` | The duration for the transition, in milliseconds.<br>You may specify a single timeout for all transitions, or individually with an object.<br>Set to 'auto' to automatically calculate transition time based on height. |
| **TransitionComponent** | ``ComponentClass<any, any> ⏐ FunctionComponent<any>`` | `Transition` | The component used for the transition. |

