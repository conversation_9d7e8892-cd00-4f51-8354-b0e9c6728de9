import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { TextField, TextFieldProps } from '@hxnova/react-components/TextField';
import { Typography } from '@hxnova/react-components/Typography';
import { Icon } from '@hxnova/icons';
import { IconButton } from '@hxnova/react-components/IconButton';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
  title: '@hxnova/react-components/TextField',
  component: TextField,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'centered',
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/tZgUauJZLgk0pX7rHCfxKt/NOVA-Core-Components?node-id=772-5886&p=f&t=GRJah5VGBaMTTcZz-0',
    },
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['!autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  // argTypes: {
  //   backgroundColor: { control: 'color' },
  // },
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
  // args: { onClick: fn() },
} satisfies Meta<typeof TextField>;

export default meta;
type Story = StoryObj<typeof meta>;

const TemplateWithDecorator = (props: TextFieldProps) => {
  const StartDecorator = <Icon family="material" name="search" />;
  const EndDecorator = (
    <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
      <Typography>Kg</Typography>
      <IconButton variant="neutral" size={props.size} disabled={props.disabled}>
        <Icon family="material" name="close" />
      </IconButton>
    </div>
  );
  return <TextField {...props} startDecorator={StartDecorator} endDecorator={EndDecorator} />;
};

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Basic: Story = {
  args: {
    label: 'Label',
    helperText: '',
    required: false,
    size: 'medium',
    fullWidth: false,
    disabled: false,
    error: false,
    showErrorIcon: true,
    readOnly: false,
    placeholder: 'Placeholder',
    autoFocus: false,
    defaultValue: '',
    style: { minWidth: '256px' },
  },
  argTypes: {
    required: {
      control: { type: 'boolean' },
    },
    fullWidth: {
      control: { type: 'boolean' },
    },
    disabled: {
      control: { type: 'boolean' },
    },
    error: {
      control: { type: 'boolean' },
    },
    showErrorIcon: {
      control: { type: 'boolean' },
    },
    readOnly: {
      control: { type: 'boolean' },
    },
    autoFocus: {
      control: { type: 'boolean' },
    },
    size: {
      control: { type: 'radio' },
      options: ['small', 'medium', 'large'],
    },
  },
  parameters: {
    controls: {
      include: [
        'label',
        'helperText',
        'required',
        'size',
        'fullWidth',
        'hideBackdrop',
        'disabled',
        'error',
        'showErrorIcon',
        'readOnly',
        'placeholder',
        'autoFocus',
        'defaultValue',
        'style',
        'maxLength',
        'multiple',
        'minRows',
        'maxRows',
      ],
    },
  },
};

export const WithHelperText: Story = {
  args: {
    ...Basic.args,
    label: 'Label',
    helperText: 'Helper text',
    required: false,
    size: 'medium',
    fullWidth: false,
    disabled: false,
    error: false,
    readOnly: false,
    placeholder: 'Placeholder',
    autoFocus: false,
    defaultValue: '',
  },
  argTypes: {
    ...Basic.argTypes,
  },
};

export const WithCounterText: Story = {
  args: {
    ...WithHelperText.args,
    maxLength: 20,
  },
  argTypes: {
    ...Basic.argTypes,
    maxLength: {
      control: { type: 'number' },
    },
  },
  parameters: {
    ...Basic.parameters,
  },
};

export const WithDecorator: Story = {
  render: TemplateWithDecorator,
  args: {
    ...WithCounterText.args,
  },
  argTypes: {
    ...Basic.argTypes,
  },
  parameters: {
    ...Basic.parameters,
  },
};

export const WithTextArea: Story = {
  args: {
    ...Basic.args,
    multiline: true,
    minRows: 3,
    maxRows: 10,
    maxLength: 100,
  },
  argTypes: {
    ...Basic.argTypes,
    multiline: {
      control: { type: 'boolean' },
    },
    minRows: {
      control: { type: 'number' },
    },
    maxRows: {
      control: { type: 'number' },
    },
    maxLength: {
      control: { type: 'number' },
    },
  },
  parameters: {
    ...Basic.parameters,
  },
};
