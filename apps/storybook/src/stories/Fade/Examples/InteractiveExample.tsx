import { useState, useMemo, useCallback } from 'react';
import { Fade } from '@hxnova/react-components/Fade';
import { Button } from '@hxnova/react-components/Button';
import { Box } from '@hxnova/react-components/Box';
import { Typography } from '@hxnova/react-components/Typography';

interface CardData {
  id: number;
  title: string;
  content: string;
  backgroundColor: string;
  borderColor: string;
  textColor: string;
}

interface FadeCardProps {
  card: CardData;
  isActive: boolean;
}

function FadeCard({ card, isActive }: FadeCardProps) {
  return (
    <Fade in={isActive} timeout={300} easing="ease-in-out">
      <Box
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          padding: '8px',
          borderRadius: 'var(--radius-xs)',
          textAlign: 'center',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          gap: '8px',
          backgroundColor: card.backgroundColor,
          border: `1px solid ${card.borderColor}`,
        }}
        role="region"
        aria-label={`${card.title} content`}
      >
        <Typography variant="titleSmall" style={{ color: card.textColor }}>
          {card.title}
        </Typography>
        <Typography variant="bodyMedium" style={{ color: card.textColor }}>
          {card.content}
        </Typography>
      </Box>
    </Fade>
  );
}

export default function InteractiveExample() {
  const [activeCard, setActiveCard] = useState<number | null>(null);

  const cardData = useMemo<CardData[]>(
    () => [
      {
        id: 1,
        title: 'Card 1',
        content: 'This is the first card with important information.',
        backgroundColor: 'var(--palette-primaryContainer)',
        borderColor: 'var(--palette-outline)',
        textColor: 'var(--palette-onPrimaryContainer)',
      },
      {
        id: 2,
        title: 'Card 2',
        content: 'This is the second card with different content.',
        backgroundColor: 'var(--palette-secondaryContainer)',
        borderColor: 'var(--palette-outline)',
        textColor: 'var(--palette-onSecondaryContainer)',
      },
      {
        id: 3,
        title: 'Card 3',
        content: 'This is the third card with unique details.',
        backgroundColor: 'var(--palette-surfaceContainer)',
        borderColor: 'var(--palette-outline)',
        textColor: 'var(--palette-onSurface)',
      },
    ],
    [],
  );

  const handleCardToggle = useCallback((cardId: number) => {
    setActiveCard((current) => (current === cardId ? null : cardId));
  }, []);

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        gap: '8px',
      }}
    >
      <Typography variant="titleMedium">Interactive Card Transitions</Typography>

      <Box
        sx={{
          display: 'flex',
          gap: '8px',
          flexWrap: 'wrap',
          justifyContent: 'center',
        }}
        role="group"
        aria-label="Card toggle buttons"
      >
        {cardData.map((card) => (
          <Button
            key={card.id}
            variant={activeCard === card.id ? 'filled' : 'outlined'}
            onClick={() => handleCardToggle(card.id)}
            aria-pressed={activeCard === card.id}
            aria-describedby={`card-${card.id}-content`}
          >
            {activeCard === card.id ? `Hide ${card.title}` : `Show ${card.title}`}
          </Button>
        ))}
      </Box>

      <Box
        sx={{
          position: 'relative',
          width: '350px',
          height: '150px',
        }}
        aria-live="polite"
        aria-label="Card display area"
      >
        {cardData.map((card) => (
          <FadeCard key={card.id} card={card} isActive={activeCard === card.id} />
        ))}
      </Box>

      {activeCard === null && (
        <Typography
          variant="bodySmall"
          sx={{
            color: 'var(--palette-onSurfaceVariant)',
            textAlign: 'center',
          }}
        >
          Click any button above to see the card fade in
        </Typography>
      )}
    </Box>
  );
}
