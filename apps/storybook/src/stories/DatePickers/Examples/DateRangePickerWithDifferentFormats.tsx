import { useState } from 'react';
import { Dayjs } from 'dayjs/esm';
import { DockedDateRangePicker, PickerProvider } from '@hxnova/react-components/DatePickers';

export default function DateRangePickerWithDifferentFormats() {
  const [value1, setValue1] = useState<[Dayjs | null, Dayjs | null]>([null, null]);
  const [value2, setValue2] = useState<[Dayjs | null, Dayjs | null]>([null, null]);
  const [value3, setValue3] = useState<[Dayjs | null, Dayjs | null]>([null, null]);

  return (
    <PickerProvider>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
        <div>
          <h4>DD/MM/YYYY Format</h4>
          <DockedDateRangePicker
            format="DD/MM/YYYY"
            value={value1}
            onChange={(newValue: [Dayjs | null, Dayjs | null]) => {
              setValue1(newValue);
              console.log('DD/MM/YYYY range changed:', newValue);
            }}
            calendars={1}
          />
        </div>

        <div>
          <h4>MM/DD/YYYY Format</h4>
          <DockedDateRangePicker
            format="MM/DD/YYYY"
            value={value2}
            onChange={(newValue: [Dayjs | null, Dayjs | null]) => {
              setValue2(newValue);
              console.log('MM/DD/YYYY range changed:', newValue);
            }}
            calendars={1}
          />
        </div>

        <div>
          <h4>YYYY-MM-DD Format</h4>
          <DockedDateRangePicker
            format="YYYY-MM-DD"
            value={value3}
            onChange={(newValue: [Dayjs | null, Dayjs | null]) => {
              setValue3(newValue);
              console.log('YYYY-MM-DD range changed:', newValue);
            }}
            calendars={1}
          />
        </div>
      </div>
    </PickerProvider>
  );
}
