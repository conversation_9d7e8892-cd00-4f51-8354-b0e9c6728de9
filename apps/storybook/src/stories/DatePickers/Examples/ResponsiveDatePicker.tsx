import { useState } from 'react';
import { Dayjs } from 'dayjs/esm';
import { DateRangePicker, PickerProvider } from '@hxnova/react-components/DatePickers';

export default function ResponsiveDatePicker() {
  const [value, setValue] = useState<[Dayjs | null, Dayjs | null]>([null, null]);

  return (
    <PickerProvider>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
        <h4>Responsive Date Range Picker</h4>
        <p>Adapts to screen size: modal on mobile, popover on desktop.</p>

        <DateRangePicker
          format="DD/MM/YYYY"
          value={value}
          onChange={(newValue: [Dayjs | null, Dayjs | null]) => {
            setValue(newValue);
            console.log('Date range changed:', newValue);
          }}
          calendars={2}
          desktopModeMediaQuery="@media (min-width: 720px)"
        />
      </div>
    </PickerProvider>
  );
}
