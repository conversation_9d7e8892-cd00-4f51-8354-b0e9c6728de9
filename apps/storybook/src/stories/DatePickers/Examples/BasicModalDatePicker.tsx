import { useState } from 'react';
import { Dayjs } from 'dayjs/esm';
import { ModalDatePicker, PickerProvider } from '@hxnova/react-components/DatePickers';

export default function BasicModalDatePicker() {
  const [value, setValue] = useState<Dayjs | null>(null);

  return (
    <PickerProvider>
      <ModalDatePicker
        label="Select Date"
        format="DD/MM/YYYY"
        value={value}
        onChange={(newValue) => {
          setValue(newValue);
          console.log('Date changed:', newValue);
        }}
      />
    </PickerProvider>
  );
}
