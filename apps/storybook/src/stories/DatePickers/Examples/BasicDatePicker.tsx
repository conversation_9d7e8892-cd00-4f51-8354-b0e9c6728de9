import { useState } from 'react';
import { DatePicker, PickerProvider } from '@hxnova/react-components/DatePickers';
import { Dayjs } from 'dayjs/esm';

export default function BasicDatePicker() {
  const [value, setValue] = useState<Dayjs | null>(null);

  return (
    <PickerProvider>
      <DatePicker
        format="DD/MM/YYYY"
        value={value}
        onChange={(newValue) => {
          setValue(newValue);
          console.log('Date changed:', newValue);
        }}
        disableFuture={false}
        disablePast={false}
        placeholder="DD/MM/YYYY"
      />
    </PickerProvider>
  );
}
