import { useState } from 'react';
import dayjs, { Dayjs } from 'dayjs/esm';
import { DockedDateRangePicker, PickerProvider } from '@hxnova/react-components/DatePickers';

export default function DateRangePickerWithValidation() {
  const [value1, setValue1] = useState<[Dayjs | null, Dayjs | null]>([null, null]);
  const [value2, setValue2] = useState<[Dayjs | null, Dayjs | null]>([null, null]);
  const [value3, setValue3] = useState<[Dayjs | null, Dayjs | null]>([null, null]);

  // Custom validation function to disable weekends - using Dayjs for better type safety
  const shouldDisableDate = (date: Dayjs) => {
    const day = date.day();
    return day === 0 || day === 6; // Sunday = 0, Saturday = 6
  };

  return (
    <PickerProvider>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '24px', maxWidth: '600px' }}>
        <div>
          <h4>Disable Past Dates</h4>
          <p>Only allows selection of current and future dates.</p>
          <DockedDateRangePicker
            format="DD/MM/YYYY"
            value={value1}
            onChange={(newValue: [Dayjs | null, Dayjs | null]) => {
              setValue1(newValue);
              console.log('Range 1 changed:', newValue);
            }}
            calendars={1}
            disablePast={true}
          />
        </div>

        <div>
          <h4>Disable Future Dates</h4>
          <p>Only allows selection of past and current dates.</p>
          <DockedDateRangePicker
            format="DD/MM/YYYY"
            value={value2}
            onChange={(newValue: [Dayjs | null, Dayjs | null]) => {
              setValue2(newValue);
              console.log('Range 2 changed:', newValue);
            }}
            calendars={1}
            disableFuture={true}
          />
        </div>

        <div>
          <h4>Custom Validation (No Weekends)</h4>
          <p>Disables weekend dates (Saturday and Sunday) using custom validation.</p>
          <DockedDateRangePicker
            format="DD/MM/YYYY"
            value={value3}
            onChange={(newValue: [Dayjs | null, Dayjs | null]) => {
              setValue3(newValue);
              console.log('Range 3 changed:', newValue);
            }}
            calendars={1}
            shouldDisableDate={shouldDisableDate}
            minDate={dayjs().subtract(1, 'month')}
            maxDate={dayjs().add(1, 'month')}
          />
          <p style={{ fontSize: '12px', color: '#666', marginTop: '8px' }}>
            Also demonstrates min/max date restrictions (±1 month from today)
          </p>
        </div>
      </div>
    </PickerProvider>
  );
}
