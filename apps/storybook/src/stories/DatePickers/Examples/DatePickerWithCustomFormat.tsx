import { useState } from 'react';
import { Dayjs } from 'dayjs/esm';
import { DockedDatePicker, PickerProvider } from '@hxnova/react-components/DatePickers';

export default function DatePickerWithCustomFormat() {
  const [value1, setValue1] = useState<Dayjs | null>(null);
  const [value2, setValue2] = useState<Dayjs | null>(null);
  const [value3, setValue3] = useState<Dayjs | null>(null);

  return (
    <PickerProvider>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
        <DockedDatePicker
          label="MM/DD/YYYY Format"
          format="MM/DD/YYYY"
          value={value1}
          onChange={(newValue) => {
            setValue1(newValue);
            console.log('Date changed:', newValue);
          }}
        />

        <DockedDatePicker
          label="DD-MM-YYYY Format"
          format="DD-MM-YYYY"
          value={value2}
          onChange={(newValue) => {
            setValue2(newValue);
            console.log('Date changed:', newValue);
          }}
        />

        <DockedDatePicker
          label="YYYY/MM/DD Format"
          format="YYYY/MM/DD"
          value={value3}
          onChange={(newValue) => {
            setValue3(newValue);
            console.log('Date changed:', newValue);
          }}
        />
      </div>
    </PickerProvider>
  );
}
