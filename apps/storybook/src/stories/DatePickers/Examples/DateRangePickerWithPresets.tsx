import { useState } from 'react';
import dayjs, { Dayjs } from 'dayjs/esm';
import { DockedDateRangePicker, PickerProvider } from '@hxnova/react-components/DatePickers';
import { Button } from '@hxnova/react-components/Button';

export default function DateRangePickerWithPresets() {
  const [value, setValue] = useState<[Dayjs | null, Dayjs | null]>([null, null]);

  const presets = [
    {
      label: 'Today',
      getValue: () => [dayjs(), dayjs()] as [Dayjs, Dayjs],
    },
    {
      label: 'Yesterday',
      getValue: () => [dayjs().subtract(1, 'day'), dayjs().subtract(1, 'day')] as [Dayjs, Dayjs],
    },
    {
      label: 'Last 7 days',
      getValue: () => [dayjs().subtract(6, 'day'), dayjs()] as [Dayjs, Dayjs],
    },
    {
      label: 'Last 14 days',
      getValue: () => [dayjs().subtract(13, 'day'), dayjs()] as [Dayjs, Dayjs],
    },
    {
      label: 'Last 30 days',
      getValue: () => [dayjs().subtract(29, 'day'), dayjs()] as [Dayjs, Dayjs],
    },
    {
      label: 'This month',
      getValue: () => [dayjs().startOf('month'), dayjs().endOf('month')] as [Dayjs, Dayjs],
    },
    {
      label: 'Last month',
      getValue: () =>
        [dayjs().subtract(1, 'month').startOf('month'), dayjs().subtract(1, 'month').endOf('month')] as [Dayjs, Dayjs],
    },
    {
      label: 'This year',
      getValue: () => [dayjs().startOf('year'), dayjs().endOf('year')] as [Dayjs, Dayjs],
    },
  ];

  return (
    <PickerProvider>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '24px', maxWidth: '800px' }}>
        <div>
          <h4>Date Range Picker with Presets</h4>
          <p>Click the preset buttons to quickly select common date ranges.</p>
        </div>

        <DockedDateRangePicker
          format="DD/MM/YYYY"
          value={value}
          onChange={(newValue: [Dayjs | null, Dayjs | null]) => {
            setValue(newValue);
            console.log('Date range changed:', newValue);
          }}
          calendars={2}
        />

        <div>
          <h5>Quick Presets:</h5>
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px', marginTop: '8px' }}>
            {presets.map((preset, index) => (
              <Button
                key={index}
                variant="outlined"
                size="small"
                onClick={() => {
                  const newValue = preset.getValue();
                  setValue(newValue);
                  console.log(`Preset "${preset.label}" selected:`, newValue);
                }}
              >
                {preset.label}
              </Button>
            ))}
          </div>
        </div>

        {value[0] && value[1] && (
          <div
            style={{
              padding: '16px',
              backgroundColor: '#f5f5f5',
              borderRadius: '8px',
              marginTop: '16px',
            }}
          >
            <h5 style={{ margin: '0 0 8px 0' }}>Selected Range:</h5>
            <div style={{ fontSize: '14px' }}>
              <strong>From:</strong> {value[0].format('dddd, MMMM DD, YYYY')}
              <br />
              <strong>To:</strong> {value[1].format('dddd, MMMM DD, YYYY')}
              <br />
              <strong>Duration:</strong> {value[1].diff(value[0], 'day') + 1} days
            </div>
          </div>
        )}
      </div>
    </PickerProvider>
  );
}
