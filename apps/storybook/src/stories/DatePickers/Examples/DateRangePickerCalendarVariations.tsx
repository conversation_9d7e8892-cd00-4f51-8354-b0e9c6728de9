import { useState } from 'react';
import { Dayjs } from 'dayjs/esm';
import { DockedDateRangePicker, PickerProvider } from '@hxnova/react-components/DatePickers';

export default function DateRangePickerCalendarVariations() {
  const [value1, setValue1] = useState<[Dayjs | null, Dayjs | null]>([null, null]);
  const [value2, setValue2] = useState<[Dayjs | null, Dayjs | null]>([null, null]);
  const [value3, setValue3] = useState<[Dayjs | null, Dayjs | null]>([null, null]);

  return (
    <PickerProvider>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
        <div>
          <h4>Single Calendar (calendars=1)</h4>
          <DockedDateRangePicker
            format="DD/MM/YYYY"
            value={value1}
            onChange={(newValue: [Dayjs | null, Dayjs | null]) => {
              setValue1(newValue);
              console.log('Single calendar range changed:', newValue);
            }}
            calendars={1}
          />
        </div>

        <div>
          <h4>Dual Calendar (calendars=2)</h4>
          <DockedDateRangePicker
            format="DD/MM/YYYY"
            value={value2}
            onChange={(newValue: [Dayjs | null, Dayjs | null]) => {
              setValue2(newValue);
              console.log('Dual calendar range changed:', newValue);
            }}
            calendars={2}
          />
        </div>

        <div>
          <h4>Triple Calendar (calendars=3)</h4>
          <DockedDateRangePicker
            format="DD/MM/YYYY"
            value={value3}
            onChange={(newValue: [Dayjs | null, Dayjs | null]) => {
              setValue3(newValue);
              console.log('Triple calendar range changed:', newValue);
            }}
            calendars={3}
          />
        </div>
      </div>
    </PickerProvider>
  );
}
