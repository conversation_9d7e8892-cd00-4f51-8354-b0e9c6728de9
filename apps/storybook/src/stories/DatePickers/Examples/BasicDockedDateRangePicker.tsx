import { useState } from 'react';
import { Dayjs } from 'dayjs/esm';
import { DockedDateRangePicker, PickerProvider } from '@hxnova/react-components/DatePickers';

export default function BasicDockedDateRangePicker() {
  const [value, setValue] = useState<[Dayjs | null, Dayjs | null]>([null, null]);

  return (
    <PickerProvider>
      <DockedDateRangePicker
        format="DD/MM/YYYY"
        value={value}
        onChange={(newValue: [Dayjs | null, Dayjs | null]) => {
          setValue(newValue);
          console.log('Date range changed:', newValue);
        }}
        calendars={2}
        disableFuture={false}
        disablePast={false}
        readOnly={false}
        disabled={false}
      />
    </PickerProvider>
  );
}
