import { useState } from 'react';
import { Dayjs } from 'dayjs/esm';
import { DateRangePicker, PickerProvider } from '@hxnova/react-components/DatePickers';

export default function BasicDateRangePicker() {
  const [value, setValue] = useState<[Dayjs | null, Dayjs | null]>([null, null]);

  return (
    <PickerProvider>
      <DateRangePicker
        format="DD/MM/YYYY"
        value={value}
        onChange={(newValue: [Dayjs | null, Dayjs | null]) => {
          setValue(newValue);
          console.log('Date range changed:', newValue);
        }}
        disableFuture={false}
        disablePast={false}
        placeholder="DD/MM/YYYY"
        calendars={2}
      />
    </PickerProvider>
  );
}
