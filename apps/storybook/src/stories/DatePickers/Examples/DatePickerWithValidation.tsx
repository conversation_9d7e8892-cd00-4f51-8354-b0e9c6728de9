import { useState } from 'react';
import dayjs, { Dayjs } from 'dayjs/esm';
import { DockedDatePicker, PickerProvider } from '@hxnova/react-components/DatePickers';

export default function DatePickerWithValidation() {
  const [value1, setValue1] = useState<Dayjs | null>(null);
  const [value2, setValue2] = useState<Dayjs | null>(null);
  const [value3, setValue3] = useState<Dayjs | null>(null);

  // Error state management
  const [error1, setError1] = useState<string>('');
  const [error2, setError2] = useState<string>('');
  const [error3, setError3] = useState<string>('');

  // Custom validation function for value1 (past dates only)
  const validatePastDate = (date: Dayjs | null): string => {
    if (!date) return '';
    if (dayjs().isBefore(date, 'day')) {
      return 'Future dates are not allowed';
    }
    return '';
  };

  // Custom validation function for value2 (future dates only)
  const validateFutureDate = (date: Dayjs | null): string => {
    if (!date) return '';
    if (dayjs().isAfter(date, 'day')) {
      return 'Past dates are not allowed';
    }
    return '';
  };

  // Custom validation function for value3 (business days only)
  const validateBusinessDay = (date: Dayjs | null): string => {
    if (!date) return '';
    const day = date.day();
    if (day === 0 || day === 6) {
      return 'Weekends are not allowed';
    }
    return '';
  };

  // Handler for past date picker
  const handleValue1Change = (newValue: Dayjs | null) => {
    setValue1(newValue);
    const errorMsg = validatePastDate(newValue);
    setError1(errorMsg);
  };

  // Handler for future date picker
  const handleValue2Change = (newValue: Dayjs | null) => {
    setValue2(newValue);
    const errorMsg = validateFutureDate(newValue);
    setError2(errorMsg);
  };

  // Handler for business day picker
  const handleValue3Change = (newValue: Dayjs | null) => {
    setValue3(newValue);
    const errorMsg = validateBusinessDay(newValue);
    setError3(errorMsg);
  };

  // Custom shouldDisableDate for business days
  const shouldDisableWeekends = (date: Dayjs) => {
    const day = date.day();
    return day === 0 || day === 6; // Sunday = 0, Saturday = 6
  };

  return (
    <PickerProvider>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '24px', maxWidth: '500px' }}>
        <div>
          <h4>Past Dates Only</h4>
          <p>Allows selection of past and current dates only, with custom error handling.</p>
          <DockedDatePicker
            label="Select Past or Present Date"
            format="DD/MM/YYYY"
            value={value1}
            onChange={handleValue1Change}
            disableFuture={true}
            aria-label="Date picker that disables future dates"
            aria-describedby={error1 ? 'past-date-error' : undefined}
          />
          {error1 && (
            <div
              id="past-date-error"
              style={{
                color: '#d32f2f',
                fontSize: '12px',
                marginTop: '4px',
                display: 'flex',
                alignItems: 'center',
                gap: '4px',
              }}
              role="alert"
              aria-live="polite"
            >
              ⚠️ {error1}
            </div>
          )}
          {!error1 && value1 && (
            <div style={{ color: '#2e7d32', fontSize: '12px', marginTop: '4px' }}>
              ✓ Valid date selected: {value1.format('DD/MM/YYYY')}
            </div>
          )}
        </div>

        <div>
          <h4>Future Dates Only</h4>
          <p>Allows selection of future and current dates only, with validation feedback.</p>
          <DockedDatePicker
            label="Select Future or Present Date"
            format="DD/MM/YYYY"
            value={value2}
            onChange={handleValue2Change}
            disablePast={true}
            aria-label="Date picker that disables past dates"
            aria-describedby={error2 ? 'future-date-error' : undefined}
          />
          {error2 && (
            <div
              id="future-date-error"
              style={{
                color: '#d32f2f',
                fontSize: '12px',
                marginTop: '4px',
                display: 'flex',
                alignItems: 'center',
                gap: '4px',
              }}
              role="alert"
              aria-live="polite"
            >
              ⚠️ {error2}
            </div>
          )}
          {!error2 && value2 && (
            <div style={{ color: '#2e7d32', fontSize: '12px', marginTop: '4px' }}>
              ✓ Valid date selected: {value2.format('DD/MM/YYYY')}
            </div>
          )}
        </div>

        <div>
          <h4>Business Days Only</h4>
          <p>Disables weekends and provides clear feedback about date restrictions.</p>
          <DockedDatePicker
            label="Select Business Day"
            format="DD/MM/YYYY"
            value={value3}
            onChange={handleValue3Change}
            shouldDisableDate={shouldDisableWeekends}
            aria-label="Date picker for business days only"
            aria-describedby={error3 ? 'business-day-error' : 'business-day-help'}
          />
          {error3 && (
            <div
              id="business-day-error"
              style={{
                color: '#d32f2f',
                fontSize: '12px',
                marginTop: '4px',
                display: 'flex',
                alignItems: 'center',
                gap: '4px',
              }}
              role="alert"
              aria-live="polite"
            >
              ⚠️ {error3}
            </div>
          )}
          {!error3 && !value3 && (
            <div id="business-day-help" style={{ color: '#666', fontSize: '12px', marginTop: '4px' }}>
              💡 Only weekdays (Monday-Friday) are selectable
            </div>
          )}
          {!error3 && value3 && (
            <div style={{ color: '#2e7d32', fontSize: '12px', marginTop: '4px' }}>
              ✓ Valid business day selected: {value3.format('DD/MM/YYYY')}
            </div>
          )}
        </div>
      </div>
    </PickerProvider>
  );
}
