import { useState } from 'react';
import { Dayjs } from 'dayjs/esm';
import { DockedDatePicker, PickerProvider } from '@hxnova/react-components/DatePickers';

export default function BasicDockedDatePicker() {
  const [value, setValue] = useState<Dayjs | null>(null);

  return (
    <PickerProvider>
      <DockedDatePicker
        format="DD/MM/YYYY"
        value={value}
        onChange={(newValue) => {
          setValue(newValue);
          console.log('Date changed:', newValue);
        }}
      />
    </PickerProvider>
  );
}
