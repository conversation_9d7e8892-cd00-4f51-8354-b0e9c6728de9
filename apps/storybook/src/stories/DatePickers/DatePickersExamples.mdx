import { <PERSON>vas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';                                                                  
import BasicDatePicker from './Examples/BasicDatePicker';
import BasicDatePickerSource from './Examples/BasicDatePicker.tsx?raw';
import BasicDateRangePicker from './Examples/BasicDateRangePicker';
import BasicDateRangePickerSource from './Examples/BasicDateRangePicker.tsx?raw';
import BasicDockedDatePicker from './Examples/BasicDockedDatePicker';
import BasicDockedDatePickerSource from './Examples/BasicDockedDatePicker.tsx?raw';
import BasicModalDatePicker from './Examples/BasicModalDatePicker';
import BasicModalDatePickerSource from './Examples/BasicModalDatePicker.tsx?raw';
import BasicDockedDateRangePicker from './Examples/BasicDockedDateRangePicker';
import BasicDockedDateRangePickerSource from './Examples/BasicDockedDateRangePicker.tsx?raw';
import BasicModalDateRangePicker from './Examples/BasicModalDateRangePicker';
import BasicModalDateRangePickerSource from './Examples/BasicModalDateRangePicker.tsx?raw';
import DatePickerWithCustomFormat from './Examples/DatePickerWithCustomFormat';
import DatePickerWithCustomFormatSource from './Examples/DatePickerWithCustomFormat.tsx?raw';
import DatePickerWithValidation from './Examples/DatePickerWithValidation';
import DatePickerWithValidationSource from './Examples/DatePickerWithValidation.tsx?raw';
import ResponsiveDatePicker from './Examples/ResponsiveDatePicker';
import ResponsiveDatePickerSource from './Examples/ResponsiveDatePicker.tsx?raw';
import DateRangePickerWithPresets from './Examples/DateRangePickerWithPresets';
import DateRangePickerWithPresetsSource from './Examples/DateRangePickerWithPresets.tsx?raw';
import DateRangePickerWithValidation from './Examples/DateRangePickerWithValidation';
import DateRangePickerWithValidationSource from './Examples/DateRangePickerWithValidation.tsx?raw';
import DateRangePickerCalendarVariations from './Examples/DateRangePickerCalendarVariations';
import DateRangePickerCalendarVariationsSource from './Examples/DateRangePickerCalendarVariations.tsx?raw';
import DateRangePickerWithDifferentFormats from './Examples/DateRangePickerWithDifferentFormats';
import DateRangePickerWithDifferentFormatsSource from './Examples/DateRangePickerWithDifferentFormats.tsx?raw';

<Meta title="@hxnova/react-components/DatePickers/Examples" />


## Basic Date Pickers

The DatePicker component provides flexible date selection capabilities with both docked (desktop) and modal (mobile) variants.

### Responsive DatePicker

The basic `DatePicker` component automatically adapts between desktop (docked) and mobile (modal) modes based on screen size.

**Features:**
* **Automatic Adaptation**: Switches between desktop (popover) and mobile (modal) modes
* **Media Query Control**: Use `desktopModeMediaQuery` prop to customize breakpoint
* **Consistent API**: Same props work across both desktop and mobile modes

<div className="sb-unstyled">
  <BasicDatePicker />
</div>
<CodeExpand code={BasicDatePickerSource} style={{marginTop: 16}}/>

### Docked Date Picker

This example demonstrates a basic docked date picker, which opens in a popover attached to the input field. This variant is typically used on desktop devices with sufficient screen space.

* **Format Configuration**: Uses `DD/MM/YYYY` format for displaying dates
* **Validation**: Configured to disable future dates with `disableFuture={true}`
* **State Management**: Uses controlled state with `value` and `onChange` props, use uncontrolled state with `defaultValue` prop.

<div className="sb-unstyled">
  <BasicDockedDatePicker />
</div>
<CodeExpand code={BasicDockedDatePickerSource} style={{marginTop: 16}}/>

### Modal Date Picker

This example demonstrates a modal date picker, which opens in a full-screen dialog. This variant is typically used on mobile devices or when you need a more prominent date selection interface.

<div className="sb-unstyled">
  <BasicModalDatePicker />
</div>
<CodeExpand code={BasicModalDatePickerSource} style={{marginTop: 16}}/>


## Date Range Pickers

Date range pickers allow users to select a start and end date, perfect for booking systems, filtering, and period selection.

### Responsive DateRangePicker  

The basic `DateRangePicker` component provides responsive date range selection with automatic mode switching.

**Features:**
* **Dual Calendar Support**: Shows multiple calendars for easier range selection
* **Range Validation**: Ensures start date is before end date
* **Flexible Display**: Supports various date formats and separators

<div className="sb-unstyled">
  <BasicDateRangePicker />
</div>
<CodeExpand code={BasicDateRangePickerSource} style={{marginTop: 16}}/>


### Docked Date Range Picker

This example demonstrates a docked date range picker with dual calendar display for intuitive range selection.

* **Multiple Calendars**: Shows two calendars side by side with `calendars={2}`
* **Range Selection**: Users can select start and end dates with visual feedback
* **Format Support**: Supports various date formats for display

<div className="sb-unstyled">
  <BasicDockedDateRangePicker />
</div>
<CodeExpand code={BasicDockedDateRangePickerSource} style={{marginTop: 16}}/>

### Modal Date Range Picker

This example shows a modal date range picker, providing a full-screen experience for range selection.

<div className="sb-unstyled">
  <BasicModalDateRangePicker />
</div>
<CodeExpand code={BasicModalDateRangePickerSource} style={{marginTop: 16}}/>

## Custom Formatting

### Different Date Formats

This example showcases various date format options available for date pickers, demonstrating flexibility in display preferences.

* **MM/DD/YYYY Format**: American date format
* **DD-MM-YYYY Format**: European date format with dash separators
* **YYYY/MM/DD Format**: ISO-like format for technical applications

<div className="sb-unstyled">
  <DatePickerWithCustomFormat />
</div>
<CodeExpand code={DatePickerWithCustomFormatSource} style={{marginTop: 16}}/>

## Validation

### Date Validation Rules

This example demonstrates validation features that restrict date selection based on business rules.

* **Disable Past Dates**: Use `disablePast={true}` to prevent selection of dates before today
* **Disable Future Dates**: Use `disableFuture={true}` to prevent selection of dates after today
* **Combined Validation**: Can be combined with min/max date ranges and custom validation functions

<div className="sb-unstyled">
  <DatePickerWithValidation />
</div>
<CodeExpand code={DatePickerWithValidationSource} style={{marginTop: 16}}/>

## Responsive Design

### Advanced Responsive Date Range Picker

This example demonstrates an advanced responsive date range picker with enhanced features and custom media query breakpoints.

* **Custom Media Query**: Uses `desktopModeMediaQuery` to determine when to switch modes
* **Dual Calendar Layout**: Shows two calendars side by side on desktop
* **Automatic Adaptation**: Seamlessly transitions between docked and modal variants
* **Enhanced UX**: Provides visual feedback and explanatory text

<div className="sb-unstyled">
  <ResponsiveDatePicker />
</div>
<CodeExpand code={ResponsiveDatePickerSource} style={{marginTop: 16}}/>

## Advanced Date Range Picker Features

### Date Range Picker with Presets

This example demonstrates a date range picker enhanced with common preset options for quick selection.

* **Quick Selection**: Predefined ranges like "Today", "Last 7 days", "This month"
* **Custom Presets**: Easily configurable preset buttons for business-specific ranges
* **Visual Feedback**: Shows selected range values and updates in real-time

<div className="sb-unstyled">
  <DateRangePickerWithPresets />
</div>
<CodeExpand code={DateRangePickerWithPresetsSource} style={{marginTop: 16}}/>

### Date Range Picker Validation

This example showcases various validation rules that can be applied to date range selection.

* **Disable Past Dates**: Prevent selection of dates before today
* **Disable Future Dates**: Restrict selection to past and current dates only
* **Custom Validation**: Implement business rules like "no weekends" or specific blocked dates
* **Min/Max Ranges**: Set absolute boundaries for selectable date ranges

<div className="sb-unstyled">
  <DateRangePickerWithValidation />
</div>
<CodeExpand code={DateRangePickerWithValidationSource} style={{marginTop: 16}}/>

### Calendar Layout Variations

This example demonstrates different calendar layout options for date range selection.

* **Single Calendar**: Compact layout ideal for mobile or space-constrained interfaces
* **Dual Calendar**: Standard desktop layout showing two months side by side
* **Triple Calendar**: Extended view for selecting longer ranges or better month overview

<div className="sb-unstyled">
  <DateRangePickerCalendarVariations />
</div>
<CodeExpand code={DateRangePickerCalendarVariationsSource} style={{marginTop: 16}}/>

### Date Format and Separator Options

This example showcases different date formats and range separators available.

* **Multiple Date Formats**: Support for various regional date formats (DD/MM/YYYY, MM/DD/YYYY, ISO)
* **Custom Separators**: Configure range separators (" to ", " → ", " - ")
* **Consistent Formatting**: Format applies to both display and input parsing

<div className="sb-unstyled">
  <DateRangePickerWithDifferentFormats />
</div>
<CodeExpand code={DateRangePickerWithDifferentFormatsSource} style={{marginTop: 16}}/>

