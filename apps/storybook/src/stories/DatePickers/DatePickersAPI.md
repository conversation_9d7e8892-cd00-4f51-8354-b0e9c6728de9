# API Documentation

- [DatePicker](#datepicker)
- [DateRangePicker](#daterangepicker)
- [DockedDatePicker](#dockeddatepicker)
- [DockedDateRangePicker](#dockeddaterangepicker)
- [ModalDatePicker](#modaldatepicker)
- [ModalDateRangePicker](#modaldaterangepicker)

# DatePicker

API reference docs for the React DatePicker component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `DatePicker` component, you can choose to import it directly or through the main entry point.

```jsx
import { DatePicker } from '@hxnova/react-components/DatePicker';
// or
import { DatePicker } from '@hxnova/react-components';
```

## Props

The properties available for the `DatePicker` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **cancelText** | `string` | `'Cancel'` | Text for cancel button. |
| **clearText** | `string` | `'Clear'` | Text for clear button. |
| **closeOnSelect** | ``false ⏐ true`` | `false` | If `true`, the picker closes after a date is selected. |
| **component** | `"div"` | - |  |
| **defaultValue** | ``null ⏐ Dayjs`` | - | The default selected value.<br>Used when the component is not controlled. |
| **desktopModeMediaQuery** | `string` | `'@media (pointer: fine)'` | CSS media query when `Mobile` mode will be changed to `Desktop`.<br>@example '@media (min-width: 720px)' or theme.breakpoints.up("sm") |
| **disabled** | ``false ⏐ true`` | `false` | If `true`, the component is disabled.<br>When disabled, the value cannot be changed and no interaction is possible. |
| **disableFuture** | ``false ⏐ true`` | `false` | If `true`, disable values after the current date for date components, time for time components and both for date time components. |
| **disablePast** | ``false ⏐ true`` | `false` | If `true`, disable values before the current date for date components, time for time components and both for date time components. |
| **error** | ``false ⏐ true`` | `false` | If `true`, the picker and its field will display an error state. |
| **firstDayOfWeek** | ``0 ⏐ 1 ⏐ 2 ⏐ 3 ⏐ 4 ⏐ 5 ⏐ 6`` | `0` | First day of the week. 0 = Sunday, 1 = Monday, etc. |
| **format** | `string` | `'DD/MM/YYYY'` | Format string for displaying the date (uses dayjs format strings). |
| **fullWidth** | ``false ⏐ true`` | `false` | If `true`, the input will take up the full width of its container. |
| **helperText** | `ReactNode` | - | The helper text content. |
| **label** | `ReactNode` | - | The label text displayed in the date field. |
| **maxDate** | `Dayjs` | `2099-12-31` | Maximal selectable date. |
| **minDate** | `Dayjs` | `1900-01-01` | Minimal selectable date. |
| **okText** | `string` | `'OK'` | Text for apply button. |
| **onChange** | ``(value: Dayjs ⏐ null) => void`` | - | Callback fired when the value changes.<br>@param value The new value. |
| **onClose** | ``() => void`` | - | Callback when the picker is closed. |
| **onError** | ``(error: string ⏐ null, value: any) => void`` | - | Callback fired when validation error changes.<br>@param error The validation error message, or null if valid.<br>@param value The current value being validated. |
| **onMonthChange** | ``(month: Dayjs) => void`` | - | Callback fired on month change.<br>@param month The new month. |
| **onOpen** | ``() => void`` | - | Callback when the picker is opened. |
| **onYearChange** | ``(year: Dayjs) => void`` | - | Callback fired on year change.<br>@param year The new year. |
| **open** | ``false ⏐ true`` | `false` | Controls whether the popup is open. |
| **placeholder** | `string` | - | The placeholder text for the inputs. |
| **placement** | ``"bottom-start" ⏐ "bottom-end" ⏐ "top-start" ⏐ "top-end"`` | `'bottom-start'` | Placement of the popup. |
| **readOnly** | ``false ⏐ true`` | `false` | If `true`, the component is read-only.<br>When read-only, the value cannot be changed but the user can interact with the interface. |
| **required** | ``false ⏐ true`` | `false` | If `true`, the label is displayed as required and the input will be required. |
| **shouldDisableDate** | ``(day: Dayjs) => boolean`` | - | Disable specific date.<br>Warning: This function can be called multiple times (for example when rendering date calendar, checking if focus can be moved to a certain date, etc.). Expensive computations can impact performance.<br>@param day The date to test.<br>@returns If `true` the date will be disabled.<br>@example ```tsx<br>// Disable weekends<br>const shouldDisableDate = (date) => {<br>const day = date.day();<br>return day === 0 || day === 6;<br>};<br>// Disable specific dates<br>const blacklistedDates = ['2024-12-25', '2024-01-01'];<br>const shouldDisableDate = (date) => {<br>return blacklistedDates.includes(date.format('YYYY-MM-DD'));<br>};<br>``` |
| **shouldDisableMonth** | ``(month: Dayjs) => boolean`` | - | Disable specific month.<br>@param month The month to test.<br>@returns If `true`, the month will be disabled. |
| **shouldDisableYear** | ``(year: Dayjs) => boolean`` | - | Disable specific year.<br>@param year The year to test.<br>@returns If `true`, the year will be disabled. |
| **slotProps** | `DatePickerSlotProps` | `{}` | The props used for each component slot. |
| **slots** | `DatePickerSlots` | `{}` | Overridable component slots. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **value** | ``null ⏐ Dayjs`` | - | The selected value.<br>Used when the component is controlled. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `DatePicker` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| popper | .NovaDatePicker-popper | `Popper` | Component used for the popper. |
| root | .NovaDatePicker-root | `'div'` | Component used for the root element. |
| field | .NovaDatePicker-field | `DateField` | Component used for the field. |
| calendar | .NovaDatePicker-calendar | `DateCalendar` | Custom component for the calendar. |
| footer | .NovaDatePicker-footer | `PickerViewFooter` | Component used for the footer. |
| dialog | .NovaDatePicker-dialog | `Dialog` | Component used for the dialog. |
| header | .NovaDatePicker-header | `PickerViewHeader` | Component used for the header. |

<br><br>

# DateRangePicker

API reference docs for the React DateRangePicker component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `DateRangePicker` component, you can choose to import it directly or through the main entry point.

```jsx
import { DateRangePicker } from '@hxnova/react-components/DateRangePicker';
// or
import { DateRangePicker } from '@hxnova/react-components';
```

## Props

The properties available for the `DateRangePicker` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **calendars** | ``1 ⏐ 2 ⏐ 3`` | `2` | Number of calendars to display side by side. |
| **cancelText** | `string` | `'Cancel'` | Text for cancel button. |
| **clearText** | `string` | `'Clear'` | Text for clear button. |
| **closeOnSelect** | ``false ⏐ true`` | `false` | If `true`, the picker closes after a date is selected. |
| **component** | `"div"` | - |  |
| **currentMonthCalendarPosition** | ``1 ⏐ 2 ⏐ 3`` | `1` | Position of the current month in the calendar grid. |
| **defaultValue** | `PickerRangeValue` | - | The default selected value.<br>Used when the component is not controlled. |
| **desktopModeMediaQuery** | `string` | `'@media (pointer: fine)'` | CSS media query when `Mobile` mode will be changed to `Desktop`.<br>@example '@media (min-width: 720px)' or theme.breakpoints.up("sm") |
| **disabled** | ``false ⏐ true`` | `false` | If `true`, the component is disabled.<br>When disabled, the value cannot be changed and no interaction is possible. |
| **disableFuture** | ``false ⏐ true`` | `false` | If `true`, disable values after the current date for date components, time for time components and both for date time components. |
| **disablePast** | ``false ⏐ true`` | `false` | If `true`, disable values before the current date for date components, time for time components and both for date time components. |
| **error** | ``false ⏐ true`` | `false` | If `true`, the picker and its field will display an error state. |
| **firstDayOfWeek** | ``0 ⏐ 1 ⏐ 2 ⏐ 3 ⏐ 4 ⏐ 5 ⏐ 6`` | `0` | First day of the week. 0 = Sunday, 1 = Monday, etc. |
| **format** | `string` | `'DD/MM/YYYY'` | Format string for displaying the date (uses dayjs format strings). |
| **fullWidth** | ``false ⏐ true`` | `false` | If `true`, the input will take up the full width of its container. |
| **helperText** | `ReactNode` | - | The helper text content. |
| **label** | `ReactNode` | - | The label text displayed in the date field. |
| **maxDate** | `Dayjs` | `2099-12-31` | Maximal selectable date. |
| **minDate** | `Dayjs` | `1900-01-01` | Minimal selectable date. |
| **okText** | `string` | `'OK'` | Text for apply button. |
| **onChange** | ``(value: PickerRangeValue) => void`` | - | Callback fired when the value changes.<br>@param value The new value. |
| **onClose** | ``() => void`` | - | Callback when the picker is closed. |
| **onError** | ``(error: string ⏐ null, value: any) => void`` | - | Callback fired when validation error changes.<br>@param error The validation error message, or null if valid.<br>@param value The current value being validated. |
| **onMonthChange** | ``(month: Dayjs) => void`` | - | Callback fired on month change.<br>@param month The new month. |
| **onOpen** | ``() => void`` | - | Callback when the picker is opened. |
| **onYearChange** | ``(year: Dayjs) => void`` | - | Callback fired on year change.<br>@param year The new year. |
| **placeholder** | `string` | - | The placeholder text for the inputs. |
| **placement** | ``"bottom-start" ⏐ "bottom-end" ⏐ "top-start" ⏐ "top-end"`` | `'bottom-start'` | Placement of the popup. |
| **readOnly** | ``false ⏐ true`` | `false` | If `true`, the component is read-only.<br>When read-only, the value cannot be changed but the user can interact with the interface. |
| **required** | ``false ⏐ true`` | `false` | If `true`, the label is displayed as required and the input will be required. |
| **shouldDisableDate** | ``(day: Dayjs) => boolean`` | - | Disable specific date.<br>Warning: This function can be called multiple times (for example when rendering date calendar, checking if focus can be moved to a certain date, etc.). Expensive computations can impact performance.<br>@param day The date to test.<br>@returns If `true` the date will be disabled.<br>@example ```tsx<br>// Disable weekends<br>const shouldDisableDate = (date) => {<br>const day = date.day();<br>return day === 0 || day === 6;<br>};<br>// Disable specific dates<br>const blacklistedDates = ['2024-12-25', '2024-01-01'];<br>const shouldDisableDate = (date) => {<br>return blacklistedDates.includes(date.format('YYYY-MM-DD'));<br>};<br>``` |
| **shouldDisableMonth** | ``(month: Dayjs) => boolean`` | - | Disable specific month.<br>@param month The month to test.<br>@returns If `true`, the month will be disabled. |
| **shouldDisableYear** | ``(year: Dayjs) => boolean`` | - | Disable specific year.<br>@param year The year to test.<br>@returns If `true`, the year will be disabled. |
| **slotProps** | `DateRangePickerSlotProps` | `{}` | The props used for each component slot. |
| **slots** | `DateRangePickerSlots` | `{}` | Overridable component slots. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **value** | `PickerRangeValue` | - | The selected value.<br>Used when the component is controlled. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `DateRangePicker` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| popper | .NovaDateRangePicker-popper | `Popper` | Component used for the popper. |
| root | .NovaDateRangePicker-root | `'div'` | Component used for the root element. |
| field | .NovaDateRangePicker-field | `DateRangeField` | Component used for the field. |
| calendar | .NovaDateRangePicker-calendar | `DateRangeCalendar` | Custom component for the range calendar. |
| footer | .NovaDateRangePicker-footer | `PickerViewFooter` | Component used for the footer. |
| dialog | .NovaDateRangePicker-dialog | `Dialog` | Component used for the dialog. |
| header | .NovaDateRangePicker-header | `PickerViewHeader` | Component used for the header. |

<br><br>

# DockedDatePicker

API reference docs for the React DockedDatePicker component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `DockedDatePicker` component, you can choose to import it directly or through the main entry point.

```jsx
import { DockedDatePicker } from '@hxnova/react-components/DockedDatePicker';
// or
import { DockedDatePicker } from '@hxnova/react-components';
```

## Props

The properties available for the `DockedDatePicker` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The component used for the root node.<br>Either a string to use a HTML element or a component. |
| **cancelText** | `string` | `'Cancel'` | Text for cancel button. |
| **clearText** | `string` | `'Clear'` | Text for clear button. |
| **closeOnSelect** | ``false ⏐ true`` | `false` | If `true`, the picker closes after a date is selected. |
| **defaultValue** | ``null ⏐ Dayjs`` | - | The default selected value.<br>Used when the component is not controlled. |
| **disabled** | ``false ⏐ true`` | `false` | If `true`, the component is disabled.<br>When disabled, the value cannot be changed and no interaction is possible. |
| **disableFuture** | ``false ⏐ true`` | `false` | If `true`, disable values after the current date for date components, time for time components and both for date time components. |
| **disablePast** | ``false ⏐ true`` | `false` | If `true`, disable values before the current date for date components, time for time components and both for date time components. |
| **error** | ``false ⏐ true`` | `false` | If `true`, the picker and its field will display an error state. |
| **firstDayOfWeek** | ``0 ⏐ 1 ⏐ 2 ⏐ 3 ⏐ 4 ⏐ 5 ⏐ 6`` | `0` | First day of the week. 0 = Sunday, 1 = Monday, etc. |
| **format** | `string` | `'DD/MM/YYYY'` | Format string for displaying the date (uses dayjs format strings). |
| **fullWidth** | ``false ⏐ true`` | `false` | If `true`, the input will take up the full width of its container. |
| **helperText** | `ReactNode` | - | The helper text content. |
| **label** | `ReactNode` | - | The label text displayed in the date field. |
| **maxDate** | `Dayjs` | `2099-12-31` | Maximal selectable date. |
| **minDate** | `Dayjs` | `1900-01-01` | Minimal selectable date. |
| **okText** | `string` | `'OK'` | Text for apply button. |
| **onChange** | ``(value: Dayjs ⏐ null) => void`` | - | Callback fired when the value changes.<br>@param value The new value. |
| **onClose** | ``() => void`` | - | Callback when the picker is closed. |
| **onError** | ``(error: string ⏐ null, value: any) => void`` | - | Callback fired when validation error changes.<br>@param error The validation error message, or null if valid.<br>@param value The current value being validated. |
| **onMonthChange** | ``(month: Dayjs) => void`` | - | Callback fired on month change.<br>@param month The new month. |
| **onOpen** | ``() => void`` | - | Callback when the picker is opened. |
| **onYearChange** | ``(year: Dayjs) => void`` | - | Callback fired on year change.<br>@param year The new year. |
| **placeholder** | `string` | - | The placeholder text for the inputs. |
| **placement** | ``"bottom-start" ⏐ "bottom-end" ⏐ "top-start" ⏐ "top-end"`` | `'bottom-start'` | Placement of the popup. |
| **readOnly** | ``false ⏐ true`` | `false` | If `true`, the component is read-only.<br>When read-only, the value cannot be changed but the user can interact with the interface. |
| **required** | ``false ⏐ true`` | `false` | If `true`, the label is displayed as required and the input will be required. |
| **shouldDisableDate** | ``(day: Dayjs) => boolean`` | - | Disable specific date.<br>Warning: This function can be called multiple times (for example when rendering date calendar, checking if focus can be moved to a certain date, etc.). Expensive computations can impact performance.<br>@param day The date to test.<br>@returns If `true` the date will be disabled.<br>@example ```tsx<br>// Disable weekends<br>const shouldDisableDate = (date) => {<br>const day = date.day();<br>return day === 0 || day === 6;<br>};<br>// Disable specific dates<br>const blacklistedDates = ['2024-12-25', '2024-01-01'];<br>const shouldDisableDate = (date) => {<br>return blacklistedDates.includes(date.format('YYYY-MM-DD'));<br>};<br>``` |
| **shouldDisableMonth** | ``(month: Dayjs) => boolean`` | - | Disable specific month.<br>@param month The month to test.<br>@returns If `true`, the month will be disabled. |
| **shouldDisableYear** | ``(year: Dayjs) => boolean`` | - | Disable specific year.<br>@param year The year to test.<br>@returns If `true`, the year will be disabled. |
| **slotProps** | `DockedDatePickerSlotProps` | `{}` | The props used for each component slot. |
| **slots** | `DockedDatePickerSlots` | `{}` | Overridable component slots. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **value** | ``null ⏐ Dayjs`` | - | The selected value.<br>Used when the component is controlled. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `DockedDatePicker` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| popper | .NovaDockedDatePicker-popper | `Popper` | Component used for the popper. |
| root | .NovaDockedDatePicker-root | `'div'` | Component used for the root element. |
| field | .NovaDockedDatePicker-field | `DateField` | Component used for the field. |
| calendar | .NovaDockedDatePicker-calendar | `DateCalendar` | Custom component for the calendar. |
| footer | .NovaDockedDatePicker-footer | `PickerViewFooter` | Component used for the footer. |

## CSS classes

CSS classes for different states and variations of the `DockedDatePicker` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .Nova-disabled | `disabled` | Styles applied to the root element when disabled. |
| .Nova-selected | `selected` | Styles applied to the selected day/month/year button. |
| .NovaDockedDatePicker-monthView | `monthView` | Styles applied to the month view container. |
| .NovaDockedDatePicker-monthButton | `monthButton` | Styles applied to the month button elements. |
| .NovaDockedDatePicker-selectedMonth | `selectedMonth` | Styles applied to the selected month item. |
| .NovaDockedDatePicker-yearView | `yearView` | Styles applied to the year view container. |
| .NovaDockedDatePicker-yearButton | `yearButton` | Styles applied to the year button elements. |
| .NovaDockedDatePicker-selectedYear | `selectedYear` | Styles applied to the selected year item. |

<br><br>

# DockedDateRangePicker

API reference docs for the React DockedDateRangePicker component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `DockedDateRangePicker` component, you can choose to import it directly or through the main entry point.

```jsx
import { DockedDateRangePicker } from '@hxnova/react-components/DockedDateRangePicker';
// or
import { DockedDateRangePicker } from '@hxnova/react-components';
```

## Props

The properties available for the `DockedDateRangePicker` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The component used for the root node.<br>Either a string to use a HTML element or a component. |
| **calendars** | ``1 ⏐ 2 ⏐ 3`` | `2` | Number of calendars to display side by side. |
| **cancelText** | `string` | `'Cancel'` | Text for cancel button. |
| **clearText** | `string` | `'Clear'` | Text for clear button. |
| **closeOnSelect** | ``false ⏐ true`` | `false` | If `true`, the picker closes after a date is selected. |
| **currentMonthCalendarPosition** | ``1 ⏐ 2 ⏐ 3`` | `1` | Position of the current month in the calendar grid. |
| **defaultValue** | `PickerRangeValue` | - | The default selected value.<br>Used when the component is not controlled. |
| **disabled** | ``false ⏐ true`` | `false` | If `true`, the component is disabled.<br>When disabled, the value cannot be changed and no interaction is possible. |
| **disableFuture** | ``false ⏐ true`` | `false` | If `true`, disable values after the current date for date components, time for time components and both for date time components. |
| **disablePast** | ``false ⏐ true`` | `false` | If `true`, disable values before the current date for date components, time for time components and both for date time components. |
| **error** | ``false ⏐ true`` | `false` | If `true`, the picker and its field will display an error state. |
| **firstDayOfWeek** | ``0 ⏐ 1 ⏐ 2 ⏐ 3 ⏐ 4 ⏐ 5 ⏐ 6`` | `0` | First day of the week. 0 = Sunday, 1 = Monday, etc. |
| **format** | `string` | `'DD/MM/YYYY'` | Format string for displaying the date (uses dayjs format strings). |
| **fullWidth** | ``false ⏐ true`` | `false` | If `true`, the input will take up the full width of its container. |
| **helperText** | `ReactNode` | - | The helper text content. |
| **label** | `ReactNode` | - | The label text displayed in the date field. |
| **maxDate** | `Dayjs` | `2099-12-31` | Maximal selectable date. |
| **minDate** | `Dayjs` | `1900-01-01` | Minimal selectable date. |
| **okText** | `string` | `'OK'` | Text for apply button. |
| **onChange** | ``(value: PickerRangeValue) => void`` | - | Callback fired when the value changes.<br>@param value The new value. |
| **onClose** | ``() => void`` | - | Callback when the picker is closed. |
| **onError** | ``(error: string ⏐ null, value: any) => void`` | - | Callback fired when validation error changes.<br>@param error The validation error message, or null if valid.<br>@param value The current value being validated. |
| **onMonthChange** | ``(month: Dayjs) => void`` | - | Callback fired on month change.<br>@param month The new month. |
| **onOpen** | ``() => void`` | - | Callback when the picker is opened. |
| **onYearChange** | ``(year: Dayjs) => void`` | - | Callback fired on year change.<br>@param year The new year. |
| **placeholder** | `string` | - | The placeholder text for the inputs. |
| **placement** | ``"bottom-start" ⏐ "bottom-end" ⏐ "top-start" ⏐ "top-end"`` | `'bottom-start'` | Placement of the popup. |
| **readOnly** | ``false ⏐ true`` | `false` | If `true`, the component is read-only.<br>When read-only, the value cannot be changed but the user can interact with the interface. |
| **required** | ``false ⏐ true`` | `false` | If `true`, the label is displayed as required and the input will be required. |
| **shouldDisableDate** | ``(day: Dayjs) => boolean`` | - | Disable specific date.<br>Warning: This function can be called multiple times (for example when rendering date calendar, checking if focus can be moved to a certain date, etc.). Expensive computations can impact performance.<br>@param day The date to test.<br>@returns If `true` the date will be disabled.<br>@example ```tsx<br>// Disable weekends<br>const shouldDisableDate = (date) => {<br>const day = date.day();<br>return day === 0 || day === 6;<br>};<br>// Disable specific dates<br>const blacklistedDates = ['2024-12-25', '2024-01-01'];<br>const shouldDisableDate = (date) => {<br>return blacklistedDates.includes(date.format('YYYY-MM-DD'));<br>};<br>``` |
| **shouldDisableMonth** | ``(month: Dayjs) => boolean`` | - | Disable specific month.<br>@param month The month to test.<br>@returns If `true`, the month will be disabled. |
| **shouldDisableYear** | ``(year: Dayjs) => boolean`` | - | Disable specific year.<br>@param year The year to test.<br>@returns If `true`, the year will be disabled. |
| **slotProps** | `DockedDateRangePickerSlotProps` | `{}` | The props used for each component slot. |
| **slots** | `DockedDateRangePickerSlots` | `{}` | Overridable component slots. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **value** | `PickerRangeValue` | - | The selected value.<br>Used when the component is controlled. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `DockedDateRangePicker` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| popper | .NovaDockedDateRangePicker-popper | `Popper` | Component used for the popper. |
| root | .NovaDockedDateRangePicker-root | `'div'` | Component used for the root element. |
| field | .NovaDockedDateRangePicker-field | `DateRangeField` | Component used for the field. |
| calendar | .NovaDockedDateRangePicker-calendar | `DateRangeCalendar` | Custom component for the range calendar. |
| footer | .NovaDockedDateRangePicker-footer | `PickerViewFooter` | Component used for the footer. |

## CSS classes

CSS classes for different states and variations of the `DockedDateRangePicker` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .Nova-disabled | `disabled` | Styles applied to the root element when disabled. |

<br><br>

# ModalDatePicker

API reference docs for the React ModalDatePicker component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `ModalDatePicker` component, you can choose to import it directly or through the main entry point.

```jsx
import { ModalDatePicker } from '@hxnova/react-components/ModalDatePicker';
// or
import { ModalDatePicker } from '@hxnova/react-components';
```

## Props

The properties available for the `ModalDatePicker` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The component used for the root node.<br>Either a string to use a HTML element or a component. |
| **cancelText** | `string` | `'Cancel'` | Text for cancel button. |
| **clearText** | `string` | `'Clear'` | Text for clear button. |
| **closeOnSelect** | ``false ⏐ true`` | `false` | If `true`, the picker closes after a date is selected. |
| **defaultValue** | ``null ⏐ Dayjs`` | - | The default selected value.<br>Used when the component is not controlled. |
| **disabled** | ``false ⏐ true`` | `false` | If `true`, the component is disabled.<br>When disabled, the value cannot be changed and no interaction is possible. |
| **disableFuture** | ``false ⏐ true`` | `false` | If `true`, disable values after the current date for date components, time for time components and both for date time components. |
| **disablePast** | ``false ⏐ true`` | `false` | If `true`, disable values before the current date for date components, time for time components and both for date time components. |
| **error** | ``false ⏐ true`` | `false` | If `true`, the picker and its field will display an error state. |
| **firstDayOfWeek** | ``0 ⏐ 1 ⏐ 2 ⏐ 3 ⏐ 4 ⏐ 5 ⏐ 6`` | `0` | First day of the week. 0 = Sunday, 1 = Monday, etc. |
| **format** | `string` | `'DD/MM/YYYY'` | Format string for displaying the date (uses dayjs format strings). |
| **fullWidth** | ``false ⏐ true`` | `false` | If `true`, the input will take up the full width of its container. |
| **helperText** | `ReactNode` | - | The helper text content. |
| **label** | `ReactNode` | - | The label text displayed in the date field. |
| **maxDate** | `Dayjs` | `2099-12-31` | Maximal selectable date. |
| **minDate** | `Dayjs` | `1900-01-01` | Minimal selectable date. |
| **okText** | `string` | `'OK'` | Text for apply button. |
| **onChange** | ``(value: Dayjs ⏐ null) => void`` | - | Callback fired when the value changes.<br>@param value The new value. |
| **onClose** | ``() => void`` | - | Callback when the picker is closed. |
| **onError** | ``(error: string ⏐ null, value: any) => void`` | - | Callback fired when validation error changes.<br>@param error The validation error message, or null if valid.<br>@param value The current value being validated. |
| **onMonthChange** | ``(month: Dayjs) => void`` | - | Callback fired on month change.<br>@param month The new month. |
| **onOpen** | ``() => void`` | - | Callback when the picker is opened. |
| **onYearChange** | ``(year: Dayjs) => void`` | - | Callback fired on year change.<br>@param year The new year. |
| **open** | ``false ⏐ true`` | `false` | Controls whether the popup is open. |
| **placeholder** | `string` | - | The placeholder text for the inputs. |
| **readOnly** | ``false ⏐ true`` | `false` | If `true`, the component is read-only.<br>When read-only, the value cannot be changed but the user can interact with the interface. |
| **required** | ``false ⏐ true`` | `false` | If `true`, the label is displayed as required and the input will be required. |
| **shouldDisableDate** | ``(day: Dayjs) => boolean`` | - | Disable specific date.<br>Warning: This function can be called multiple times (for example when rendering date calendar, checking if focus can be moved to a certain date, etc.). Expensive computations can impact performance.<br>@param day The date to test.<br>@returns If `true` the date will be disabled.<br>@example ```tsx<br>// Disable weekends<br>const shouldDisableDate = (date) => {<br>const day = date.day();<br>return day === 0 || day === 6;<br>};<br>// Disable specific dates<br>const blacklistedDates = ['2024-12-25', '2024-01-01'];<br>const shouldDisableDate = (date) => {<br>return blacklistedDates.includes(date.format('YYYY-MM-DD'));<br>};<br>``` |
| **shouldDisableMonth** | ``(month: Dayjs) => boolean`` | - | Disable specific month.<br>@param month The month to test.<br>@returns If `true`, the month will be disabled. |
| **shouldDisableYear** | ``(year: Dayjs) => boolean`` | - | Disable specific year.<br>@param year The year to test.<br>@returns If `true`, the year will be disabled. |
| **slotProps** | `ModalDatePickerSlotProps` | `{}` | The props used for each component slot. |
| **slots** | `ModalDatePickerSlots` | `{}` | Overridable component slots. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **value** | ``null ⏐ Dayjs`` | - | The selected value.<br>Used when the component is controlled. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `ModalDatePicker` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| dialog | .NovaModalDatePicker-dialog | `Dialog` | Component used for the dialog. |
| header | .NovaModalDatePicker-header | `PickerViewHeader` | Component used for the header. |
| root | .NovaModalDatePicker-root | `'div'` | Component used for the root element. |
| field | .NovaModalDatePicker-field | `DateField` | Component used for the field. |
| calendar | .NovaModalDatePicker-calendar | `DateCalendar` | Custom component for the calendar. |
| footer | .NovaModalDatePicker-footer | `PickerViewFooter` | Component used for the footer. |

## CSS classes

CSS classes for different states and variations of the `ModalDatePicker` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .Nova-disabled | `disabled` | Styles applied to the root element when disabled. |
| .Nova-selected | `selected` | Styles applied to the selected day/month/year button. |
| .NovaModalDatePicker-monthView | `monthView` | Styles applied to the month view container. |
| .NovaModalDatePicker-selectedMonth | `selectedMonth` | Styles applied to the selected month item. |
| .NovaModalDatePicker-monthButton | `monthButton` | Styles applied to the month button elements. |
| .NovaModalDatePicker-yearView | `yearView` | Styles applied to the year view container. |
| .NovaModalDatePicker-yearButton | `yearButton` | Styles applied to the year button elements. |
| .NovaModalDatePicker-selectedYear | `selectedYear` | Styles applied to the selected year item. |

<br><br>

# ModalDateRangePicker

API reference docs for the React ModalDateRangePicker component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `ModalDateRangePicker` component, you can choose to import it directly or through the main entry point.

```jsx
import { ModalDateRangePicker } from '@hxnova/react-components/ModalDateRangePicker';
// or
import { ModalDateRangePicker } from '@hxnova/react-components';
```

## Props

The properties available for the `ModalDateRangePicker` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **component** | `ElementType` | - | The component used for the root node.<br>Either a string to use a HTML element or a component. |
| **calendars** | ``1 ⏐ 2 ⏐ 3`` | `2` | Number of calendars to display side by side. |
| **cancelText** | `string` | `'Cancel'` | Text for cancel button. |
| **clearText** | `string` | `'Clear'` | Text for clear button. |
| **closeOnSelect** | ``false ⏐ true`` | `false` | If `true`, the picker closes after a date is selected. |
| **currentMonthCalendarPosition** | ``1 ⏐ 2 ⏐ 3`` | `1` | Position of the current month in the calendar grid. |
| **defaultValue** | `PickerRangeValue` | - | The default selected value.<br>Used when the component is not controlled. |
| **disabled** | ``false ⏐ true`` | `false` | If `true`, the component is disabled.<br>When disabled, the value cannot be changed and no interaction is possible. |
| **disableFuture** | ``false ⏐ true`` | `false` | If `true`, disable values after the current date for date components, time for time components and both for date time components. |
| **disablePast** | ``false ⏐ true`` | `false` | If `true`, disable values before the current date for date components, time for time components and both for date time components. |
| **error** | ``false ⏐ true`` | `false` | If `true`, the picker and its field will display an error state. |
| **firstDayOfWeek** | ``0 ⏐ 1 ⏐ 2 ⏐ 3 ⏐ 4 ⏐ 5 ⏐ 6`` | `0` | First day of the week. 0 = Sunday, 1 = Monday, etc. |
| **format** | `string` | `'DD/MM/YYYY'` | Format string for displaying the date (uses dayjs format strings). |
| **fullWidth** | ``false ⏐ true`` | `false` | If `true`, the input will take up the full width of its container. |
| **helperText** | `ReactNode` | - | The helper text content. |
| **label** | `ReactNode` | - | The label text displayed in the date field. |
| **maxDate** | `Dayjs` | `2099-12-31` | Maximal selectable date. |
| **minDate** | `Dayjs` | `1900-01-01` | Minimal selectable date. |
| **okText** | `string` | `'OK'` | Text for apply button. |
| **onChange** | ``(value: PickerRangeValue) => void`` | - | Callback fired when the value changes.<br>@param value The new value. |
| **onClose** | ``() => void`` | - | Callback when the picker is closed. |
| **onError** | ``(error: string ⏐ null, value: any) => void`` | - | Callback fired when validation error changes.<br>@param error The validation error message, or null if valid.<br>@param value The current value being validated. |
| **onMonthChange** | ``(month: Dayjs) => void`` | - | Callback fired on month change.<br>@param month The new month. |
| **onOpen** | ``() => void`` | - | Callback when the picker is opened. |
| **onYearChange** | ``(year: Dayjs) => void`` | - | Callback fired on year change.<br>@param year The new year. |
| **placeholder** | `string` | - | The placeholder text for the inputs. |
| **readOnly** | ``false ⏐ true`` | `false` | If `true`, the component is read-only.<br>When read-only, the value cannot be changed but the user can interact with the interface. |
| **required** | ``false ⏐ true`` | `false` | If `true`, the label is displayed as required and the input will be required. |
| **shouldDisableDate** | ``(day: Dayjs) => boolean`` | - | Disable specific date.<br>Warning: This function can be called multiple times (for example when rendering date calendar, checking if focus can be moved to a certain date, etc.). Expensive computations can impact performance.<br>@param day The date to test.<br>@returns If `true` the date will be disabled.<br>@example ```tsx<br>// Disable weekends<br>const shouldDisableDate = (date) => {<br>const day = date.day();<br>return day === 0 || day === 6;<br>};<br>// Disable specific dates<br>const blacklistedDates = ['2024-12-25', '2024-01-01'];<br>const shouldDisableDate = (date) => {<br>return blacklistedDates.includes(date.format('YYYY-MM-DD'));<br>};<br>``` |
| **shouldDisableMonth** | ``(month: Dayjs) => boolean`` | - | Disable specific month.<br>@param month The month to test.<br>@returns If `true`, the month will be disabled. |
| **shouldDisableYear** | ``(year: Dayjs) => boolean`` | - | Disable specific year.<br>@param year The year to test.<br>@returns If `true`, the year will be disabled. |
| **slotProps** | `ModalDateRangePickerSlotProps` | `{}` | The props used for each component slot. |
| **slots** | `ModalDateRangePickerSlots` | `{}` | Overridable component slots. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |
| **value** | `PickerRangeValue` | - | The selected value.<br>Used when the component is controlled. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `ModalDateRangePicker` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| dialog | .NovaModalDateRangePicker-dialog | `Dialog` | Component used for the dialog. |
| header | .NovaModalDateRangePicker-header | `PickerViewHeader` | Component used for the header. |
| root | .NovaModalDateRangePicker-root | `'div'` | Component used for the root element. |
| field | .NovaModalDateRangePicker-field | `DateRangeField` | Component used for the field. |
| calendar | .NovaModalDateRangePicker-calendar | `DateRangeCalendar` | Custom component for the range calendar. |
| footer | .NovaModalDateRangePicker-footer | `PickerViewFooter` | Component used for the footer. |

## CSS classes

CSS classes for different states and variations of the `ModalDateRangePicker` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .Nova-disabled | `disabled` | Styles applied to the root element when disabled. |
| .NovaModalDateRangePicker-content | `content` | Styles applied to the content element. |

