import { useState } from 'react';
import type { Meta } from '@storybook/react';
import { Dayjs } from 'dayjs/esm';
import type {
  DatePickerProps,
  DateRangePickerProps,
  DockedDatePickerProps,
  ModalDatePickerProps,
} from '@hxnova/react-components/DatePickers';
import {
  DatePicker as NovaDatePicker,
  DockedDatePicker as NovaDockedDatePicker,
  ModalDatePicker as NovaModalDatePicker,
  DateRangePicker as NovaDateRangePicker,
  DockedDateRangePicker as NovaDockedDateRangePicker,
  ModalDateRangePicker as NovaModalDateRangePicker,
} from '@hxnova/react-components/DatePickers';

const meta = {
  title: '@hxnova/react-components/DatePickers',
  component: NovaDatePicker,
  parameters: {
    layout: 'centered',
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/tZgUauJZLgk0pX7rHCfxKt/NOVA-Components?node-id=4594-15410&t=YgCNlN2eT16cMyQ9-0',
    },
  },
  tags: ['!autodocs'],
} satisfies Meta<typeof NovaDatePicker>;

export default meta;

// Template for basic DatePicker
const DatePickerTemplate = (args: DatePickerProps) => {
  return <NovaDatePicker {...args} />;
};

export const DatePicker = {
  render: DatePickerTemplate,
  args: {
    format: 'DD/MM/YYYY',
    disableFuture: false,
    disablePast: false,
    readOnly: false,
    disabled: false,
    helperText: '',
  },
  argTypes: {
    disableFuture: {
      control: { type: 'boolean' },
      options: [true, false],
    },
    disablePast: {
      control: { type: 'boolean' },
      options: [true, false],
    },
  },
  parameters: {
    controls: {
      include: ['label', 'format', 'disableFuture', 'disablePast', 'readOnly', 'disabled', 'helperText'],
    },
  },
};

// Template for basic DockedDatePicker
const DockedDatePickerTemplate = (args: DockedDatePickerProps) => {
  return <NovaDockedDatePicker {...args} />;
};

// Template for basic ModalDatePicker
const ModalDatePickerTemplate = (args: ModalDatePickerProps) => {
  return <NovaModalDatePicker {...args} />;
};

// Basic stories
export const DockedDatePicker = {
  render: DockedDatePickerTemplate,
  args: {
    label: 'Date',
    format: 'DD/MM/YYYY',
    disableFuture: false,
    disablePast: false,
    readOnly: false,
    disabled: false,
    helperText: '',
  },
  argTypes: {
    disableFuture: {
      control: { type: 'boolean' },
      options: [true, false],
    },
    disablePast: {
      control: { type: 'boolean' },
      options: [true, false],
    },
  },
  parameters: {
    controls: {
      include: ['label', 'format', 'disableFuture', 'disablePast', 'readOnly', 'disabled', 'helperText'],
    },
  },
};

export const ModalDatePicker = {
  render: ModalDatePickerTemplate,
  args: {
    ...DockedDatePicker.args,
  },
  parameters: {
    ...DockedDatePicker.parameters,
  },
  argTypes: {
    ...DockedDatePicker.argTypes,
  },
};

// DateRangePicker examples
const DateRangePickerTemplate = (args: DateRangePickerProps) => {
  const [value, setValue] = useState<[Dayjs | null, Dayjs | null]>([null, null]);

  return (
    <NovaDateRangePicker
      {...args}
      value={value}
      onChange={(newValue: [Dayjs | null, Dayjs | null]) => {
        setValue(newValue);
        console.log('Date range changed:', newValue);
      }}
    />
  );
};

export const DateRangePicker = {
  render: DateRangePickerTemplate,
  args: {
    format: 'DD/MM/YYYY',
    calendars: 2,
    disableFuture: false,
    disablePast: false,
    readOnly: false,
    disabled: false,
    label: 'Depart - return dates',
  },
  argTypes: {
    disableFuture: {
      control: { type: 'boolean' },
      options: [true, false],
    },
    disablePast: {
      control: { type: 'boolean' },
      options: [true, false],
    },
  },
  parameters: {
    controls: {
      include: [
        'startLabel',
        'endLabel',
        'format',
        'calendars',
        'disableFuture',
        'disablePast',
        'readOnly',
        'disabled',
        'label',
      ],
    },
  },
};

// Template for basic DateRangePicker
const DockedDateRangePickerTemplate = (args: DateRangePickerProps) => {
  const [value, setValue] = useState<[Dayjs | null, Dayjs | null]>([null, null]);

  return (
    <NovaDockedDateRangePicker
      {...args}
      value={value}
      onChange={(newValue: [Dayjs | null, Dayjs | null]) => {
        setValue(newValue);
        console.log('Date range changed:', newValue);
      }}
    />
  );
};

export const DockedDateRangePicker = {
  render: DockedDateRangePickerTemplate,
  args: {
    startLabel: 'Start',
    endLabel: 'End',
    format: 'DD/MM/YYYY',
    calendars: 2,
    disableFuture: false,
    disablePast: false,
    readOnly: false,
    disabled: false,
    label: 'Depart - return dates',
  },
  argTypes: {
    disableFuture: {
      control: { type: 'boolean' },
      options: [true, false],
    },
    disablePast: {
      control: { type: 'boolean' },
      options: [true, false],
    },
  },
  parameters: {
    controls: {
      include: [
        'startLabel',
        'endLabel',
        'format',
        'calendars',
        'disableFuture',
        'disablePast',
        'readOnly',
        'disabled',
        'label',
      ],
    },
  },
};

const ModalDateRangePickerTemplate = (args: DateRangePickerProps) => {
  const [value, setValue] = useState<[Dayjs | null, Dayjs | null]>([null, null]);

  return (
    <NovaModalDateRangePicker
      {...args}
      value={value}
      onChange={(newValue: [Dayjs | null, Dayjs | null]) => {
        setValue(newValue);
        console.log('Date range changed:', newValue);
      }}
    />
  );
};

export const ModalDateRangePickerBasic = {
  render: ModalDateRangePickerTemplate,
  args: {
    ...DockedDateRangePicker.args,
  },
  parameters: {
    ...DockedDateRangePicker.parameters,
  },
  argTypes: {
    ...DockedDateRangePicker.argTypes,
  },
};
