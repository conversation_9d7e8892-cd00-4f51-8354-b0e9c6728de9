import { <PERSON><PERSON>, Meta } from '@storybook/blocks';

<Meta title="@hxnova/react-components/DatePickers/ReadMe" />


##  Installation

### 1. Day.js Installation

**DatePicker components require Day.js as a date manipulation library.** Install it first:

```bash
npm install dayjs
# or
yarn add dayjs
# or
pnpm add dayjs
```

**Why Day.js?**
- **Smallest bundle impact**: Only 6.77 kB gzipped
- **Lightweight**: Compared to alternatives (date-fns: 19.39 kB, Luxon: 23.26 kB, Moment: 20.78 kB)
- **Full compatibility**: Works seamlessly with Nova's DatePicker components

### 2. PickerProvider Requirement

**All DatePicker components must be wrapped under `<PERSON>er<PERSON>rovider`** to function correctly. The PickerProvider provides essential date utilities, localization context, and default configurations.

### Setup Example

```tsx
import { PickerProvider } from '@hxnova/react-components/DatePickers';
import { useState } from 'react';

function App() {

  return (
    <PickerProvider>
      <App />
    </PickerProvider>
  );
}
```

**Required imports:**
- `dayjs/esm` - For TypeScript types (Dayjs)
- `@hxnova/react-components/DatePickers` - For components and provider

**Without PickerProvider, the components will throw an error**: `"usePickerContext must be used within a PickerProvider"`

