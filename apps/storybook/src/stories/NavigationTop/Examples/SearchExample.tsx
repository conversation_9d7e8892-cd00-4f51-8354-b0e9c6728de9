import { NavigationTop } from '@hxnova/react-components/NavigationTop';
import { Avatar } from '@hxnova/react-components/Avatar';
import { Icon } from '@hxnova/icons';
import MetrologyReportingIcon from '@nexusui/branding/MetrologyReporting';

export default function VariantDemo() {
  return (
    <NavigationTop
      onSearchChange={(val) => console.log(`Searching: ${val}`)}
      productLogo={<MetrologyReportingIcon height={40} width={40} />}
      pageTitle={'Metrology Reporting'}
      iconActions={[
        {
          icon: <Icon family="material" name="invert_colors" size={24} />,
          label: 'Menu',
          onClick: () => console.log('icon action clicked'),
        },
      ]}
      primaryActions={[
        {
          children: 'Primary Action',
          onClick: () => console.log('primary action clicked'),
        },
      ]}
      userAvatar={<Avatar onClick={() => console.log('avatar clicked')}>AX</Avatar>}
    />
  );
}
