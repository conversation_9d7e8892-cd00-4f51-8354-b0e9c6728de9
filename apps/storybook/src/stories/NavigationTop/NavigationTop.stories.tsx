import type { Meta, StoryFn } from '@storybook/react';
import { NavigationTop as NovaNavigationTop } from '@hxnova/react-components/NavigationTop';
import MetrologyReportingIcon from '@nexusui/branding/MetrologyReporting';
import { Icon } from '@hxnova/icons';
import { Avatar } from '@hxnova/react-components/Avatar';

const meta = {
  title: '@hxnova/react-components/NavigationTop',
  component: NovaNavigationTop,
  parameters: {
    layout: 'fullscreen',
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/tZgUauJZLgk0pX7rHCfxKt/NOVA-Core-Components?node-id=1392-5285&t=3xVD9OlszyDAvKLM-0',
    },
  },
  tags: ['!autodocs'],
} satisfies Meta<typeof NovaNavigationTop>;

export default meta;

const NavigationTopTemplate: StoryFn<
  (props: {
    showChildren: boolean;
    showSearch: boolean;
    productLogo: '<MetrologyReporting />' | 'none';
    pageTitle: string;
    divider: boolean;
    showIconActions: boolean;
    showPrimaryActions: boolean;
    showUserAvatar: boolean;
  }) => JSX.Element
> = ({
  showChildren,
  showSearch,
  productLogo,
  pageTitle,
  divider,
  showIconActions,
  showPrimaryActions,
  showUserAvatar,
}) => {
  return (
    <NovaNavigationTop
      onSearchChange={showSearch ? (val) => console.log(`Searching: ${val}`) : undefined}
      productLogo={productLogo !== 'none' ? <MetrologyReportingIcon height={40} width={40} /> : undefined}
      pageTitle={pageTitle}
      divider={divider}
      iconActions={
        showIconActions
          ? [
              {
                icon: <Icon family="material" name="invert_colors" size={24} />,
                label: 'Menu',
                onClick: () => console.log('icon action clicked'),
              },
              {
                icon: <Icon family="material" name="translate" size={24} />,
                label: 'Menu2',
                onClick: () => console.log('icon action clicked'),
              },
              {
                icon: <Icon family="material" name="more_vert" size={24} />,
                label: 'Menu3',
                onClick: () => console.log('icon action clicked'),
              },
            ]
          : undefined
      }
      primaryActions={
        showPrimaryActions
          ? [
              {
                children: 'Other Action',
                variant: 'outlined',
                onClick: () => console.log('other action clicked'),
              },
              {
                children: 'Primary Action',
                onClick: () => console.log('primary action clicked'),
              },
            ]
          : undefined
      }
      userAvatar={showUserAvatar ? <Avatar onClick={() => console.log('avatar clicked')}>AX</Avatar> : undefined}
    >
      {showChildren && `<CHILDREN CONTENT HERE>`}
    </NovaNavigationTop>
  );
};

export const UniversalStory = {
  render: NavigationTopTemplate,
  args: {
    showChildren: false,
    showSearch: false,
    productLogo: '<MetrologyReporting />',
    pageTitle: 'Metrology Reporting',
    divider: true,
    showIconActions: false,
    showPrimaryActions: false,
    showUserAvatar: true,
  },

  argTypes: {
    productLogo: {
      control: { type: 'select' },
      options: ['<MetrologyReporting />', 'none'],
    },
  },
  parameters: {
    controls: {
      include: [
        'showChildren',
        'showSearch',
        'productLogo',
        'pageTitle',
        'divider',
        'showIconActions',
        'showPrimaryActions',
        'showUserAvatar',
      ],
    },
  },
};
