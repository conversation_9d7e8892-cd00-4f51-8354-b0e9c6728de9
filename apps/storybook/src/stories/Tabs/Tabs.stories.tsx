import type { Meta, StoryFn } from '@storybook/react';
import { Tabs as NovaTabs } from '@hxnova/react-components/Tabs';
import { Icon } from '@hxnova/icons';
import { ComponentProps } from 'react';

const meta = {
  title: '@hxnova/react-components/Tabs',
  component: NovaTabs.Tab,
  parameters: {
    layout: 'centered',
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/tZgUauJZLgk0pX7rHCfxKt/NOVA-Core-Components?node-id=3533-14758&p=f&t=Hb57YfuAVPTWV7cR-0',
    },
  },
  tags: ['!autodocs'],
} satisfies Meta<typeof NovaTabs.Tab>;

export default meta;

const TabsTemplate: StoryFn<
  (props: {
    orientation: ComponentProps<typeof NovaTabs.Root>['orientation'];
    divider: boolean;
    contents: 'icon' | 'text' | 'both';
    iconPosition: 'top' | 'side';
  }) => JSX.Element
> = ({ orientation, divider, contents, iconPosition }) => {
  const showIcon = contents === 'icon' || contents === 'both';
  const showText = contents === 'text' || contents === 'both';
  return (
    <NovaTabs.Root defaultValue={0} orientation={orientation}>
      <NovaTabs.List divider={divider}>
        <NovaTabs.Tab
          iconPosition={iconPosition}
          value={0}
          icon={showIcon ? <Icon family="material" name="looks_one" size={24} /> : undefined}
        >
          {showText ? 'Tab 1' : undefined}
        </NovaTabs.Tab>
        <NovaTabs.Tab
          iconPosition={iconPosition}
          disabled
          value={1}
          icon={showIcon ? <Icon family="material" name="looks_two" size={24} /> : undefined}
        >
          {showText ? 'Tab 2' : undefined}
        </NovaTabs.Tab>
        <NovaTabs.Tab
          iconPosition={iconPosition}
          value={2}
          icon={showIcon ? <Icon family="material" name="looks_3" size={24} /> : undefined}
        >
          {showText ? 'Tab 3' : undefined}
        </NovaTabs.Tab>
        <NovaTabs.Tab
          iconPosition={iconPosition}
          value={3}
          icon={showIcon ? <Icon family="material" name="looks_4" size={24} /> : undefined}
        >
          {showText ? 'Tab 4' : undefined}
        </NovaTabs.Tab>
        <NovaTabs.Indicator />
      </NovaTabs.List>
      <NovaTabs.Panel value={0} style={{ padding: 16 }}>
        Tab 1 Content
      </NovaTabs.Panel>
      <NovaTabs.Panel value={1} style={{ padding: 16 }}>
        Tab 2 Content
      </NovaTabs.Panel>
      <NovaTabs.Panel value={2} style={{ padding: 16 }}>
        Tab 3 Content
      </NovaTabs.Panel>
      <NovaTabs.Panel value={3} style={{ padding: 16 }}>
        Tab 4 Content
      </NovaTabs.Panel>
    </NovaTabs.Root>
  );
};

export const UniversalStory = {
  render: TabsTemplate,
  args: {
    orientation: 'horizontal',
    divider: true,
    contents: 'text',
    iconPosition: 'side',
  },

  argTypes: {
    orientation: {
      control: { type: 'select' },
      options: ['horizontal', 'vertical'],
    },
    iconPosition: {
      control: { type: 'select' },
      options: ['top', 'side'],
    },
    contents: {
      control: { type: 'select' },
      options: ['text', 'icon', 'both'],
    },
  },
  parameters: {
    controls: { include: ['orientation', 'iconPosition', 'divider', 'contents'] },
  },
};
