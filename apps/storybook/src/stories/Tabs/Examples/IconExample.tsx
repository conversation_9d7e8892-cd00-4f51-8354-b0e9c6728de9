import { Tabs } from '@hxnova/react-components/Tabs';
import { Icon } from '@hxnova/icons';

export default function Demo() {
  return (
    <Tabs.Root defaultValue={1}>
      <Tabs.List>
        <Tabs.Tab value={1} icon={<Icon family="material" name="looks_one" size={24} />}>
          Tab 1
        </Tabs.Tab>
        <Tabs.Tab value={2} icon={<Icon family="material" name="looks_two" size={24} />}>
          Tab 2
        </Tabs.Tab>
        <Tabs.Tab value={3} icon={<Icon family="material" name="looks_3" size={24} />}>
          Tab 3
        </Tabs.Tab>
        <Tabs.Tab value={4} icon={<Icon family="material" name="looks_4" size={24} />}></Tabs.Tab>
        <Tabs.Indicator />
      </Tabs.List>
      <Tabs.Panel value={1} sx={{ padding: 16 }}>
        Tab 1 Content
      </Tabs.Panel>
      <Tabs.Panel value={2} sx={{ padding: 16 }}>
        Tab 2 Content
      </Tabs.Panel>
      <Tabs.Panel value={3} sx={{ padding: 16 }}>
        Tab 3 Content
      </Tabs.Panel>
      <Tabs.Panel value={4} sx={{ padding: 16 }}>
        Tab 4 Content
      </Tabs.Panel>
    </Tabs.Root>
  );
}
