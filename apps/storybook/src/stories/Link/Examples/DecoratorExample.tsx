import { Link } from '@hxnova/react-components/Link';
import { Avatar } from '@hxnova/react-components/Avatar';
import { Icon } from '@hxnova/icons';

export default function Demo() {
  return (
    <div sx={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
      <Link href="#sample" startDecorator={<Icon family="material" name="home" />}>
        Home
      </Link>
      <Link href="#sample" endDecorator={<Icon family="material" name="edit" />}>
        Edit
      </Link>
      <Link
        href="#sample"
        startDecorator={
          <Avatar sx={{ fontSize: 18 }}>
            <Icon family="material" name="person" />
          </Avatar>
        }
      >
        Profile
      </Link>
    </div>
  );
}
