import React from 'react';
import { StoryFn, Meta } from '@storybook/react';
import { Link as NovaLink } from '@hxnova/react-components/Link';
import { Icon } from '@hxnova/icons';

export default {
  title: '@hxnova/react-components/Link',
  component: NovaLink,
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/i3Hb8tWFEsucfJcWZSz20H/NOVA-Core-Components-(Copy)?node-id=4594-11316&t=lrVprY5UjCitgqgv-0',
    },
  },
  tags: ['!autodocs'],
} as Meta<typeof NovaLink>;

const Template: StoryFn<typeof NovaLink> = (args) => (
  <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: 20 }}>
    <NovaLink href="#common-examples" {...args} disabled startDecorator={<Icon family="material" name="edit" />}>
      Link
    </NovaLink>
    <NovaLink href="#common-examples" {...args} disabled endDecorator={<Icon family="material" name="arrow_forward" />}>
      Link
    </NovaLink>
    <NovaLink href="#common-examples" {...args} startDecorator={<Icon family="material" name="edit" />}>
      Link
    </NovaLink>
    <NovaLink href="#common-examples" {...args} endDecorator={<Icon family="material" name="arrow_forward" />}>
      Link
    </NovaLink>
  </div>
);

export const Link = {
  render: Template,
  args: {
    variant: 'labelMedium',
    underline: 'always',
  },
  argTypes: {
    underline: {
      control: { type: 'select' },
      options: ['always', 'hover', 'none'],
    },
    variant: {
      control: { type: 'select' },
      options: [
        'inherit',
        'displayLarge',
        'displayMedium',
        'displaySmall',
        'headlineLarge',
        'headlineMedium',
        'headlineSmall',
        'titleLarge',
        'titleMedium',
        'titleSmall',
        'bodyLarge',
        'bodyMedium',
        'bodySmall',
        'labelLarge',
        'labelMedium',
        'labelSmall',
      ],
    },
  },
  parameters: {
    controls: {
      include: ['underline', 'variant'],
    },
  },
};
