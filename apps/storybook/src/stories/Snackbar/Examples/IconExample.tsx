import { Alert } from '@hxnova/react-components/Alert';
import { Icon } from '@hxnova/icons';
export default function Demo() {
  return (
    <div sx={{ display: 'flex', width: '50%', flexDirection: 'column', flexWrap: 'wrap', gap: '1rem' }}>
      <Alert color="error" startDecorator={<Icon family="material" name="info" size={24} />}>
        This is a message
      </Alert>
      <Alert color="warning" startDecorator={<Icon family="material" name="report_problem" size={24} />}>
        This is a message
      </Alert>
      <Alert color="info" startDecorator={<Icon family="material" name="info" size={24} />}>
        This is a message
      </Alert>
      <Alert color="success" startDecorator={<Icon family="material" name="check_circle" size={24} />}>
        This is a message
      </Alert>
    </div>
  );
}
