import React from 'react';
import { StoryFn, Meta } from '@storybook/react';
import { Snackbar as NovaSnackbar, SnackbarCloseReason, SnackbarProps } from '@hxnova/react-components/Snackbar';
import { AlertProps } from '@hxnova/react-components/Alert';
import { Button } from '@hxnova/react-components/Button';
import { Alert } from '@hxnova/react-components/Alert';
import { Icon } from '@hxnova/icons';
export default {
  title: '@hxnova/react-components/Snackbar',
  component: NovaSnackbar,
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/tZgUauJZLgk0pX7rHCfxKt/NOVA-Core-Components?node-id=1239-20977&p=f&t=GVU6CYhRjiUMUAx0-0',
    },
    layout: 'centered',
  },
  tags: ['!autodocs'],
} as Meta<typeof NovaSnackbar>;

const Template: StoryFn<
  (
    props: SnackbarProps &
      AlertProps & {
        showStartIcon?: boolean;
        showAction?: boolean;
        showCloseButton?: boolean;
      },
  ) => JSX.Element
> = ({ showStartIcon, showAction, showCloseButton, color, intensity, orientation, ...args }) => {
  const [open, setOpen] = React.useState(false);

  const handleClick = () => {
    setOpen(true);
  };

  const handleClose = (_event?: React.SyntheticEvent | Event, reason?: SnackbarCloseReason) => {
    if (reason === 'clickaway') {
      return;
    }
    setOpen(false);
  };
  return (
    <div>
      <Button onClick={handleClick}>Open Snackbar</Button>
      <NovaSnackbar {...args} open={open} onClose={handleClose}>
        <Alert
          onClose={showCloseButton ? handleClose : undefined}
          intensity={intensity}
          color={color}
          orientation={orientation}
          startDecorator={showStartIcon ? <Icon family="material" name="info" size={24} /> : undefined}
          action={showAction ? { label: 'Button', onClick: () => {} } : undefined}
        >
          This is an Alert inside a Snackbar!
        </Alert>
      </NovaSnackbar>
    </div>
  );
};

export const Snackbar = {
  render: Template,
  args: {
    anchorOrigin: {
      vertical: 'bottom',
      horizontal: 'center',
    },
    autoHideDuration: 4000,
    disableWindowBlurListener: false,
    resumeHideDuration: 2000,
    animationDuration: 300,
    color: 'primary',
    intensity: 'bold',
    orientation: 'horizontal',
    showStartIcon: true,
    showAction: true,
    showCloseButton: true,
  },
  argTypes: {
    color: {
      control: { type: 'radio' },
      options: ['primary', 'error', 'warning', 'info', 'success'],
    },
    intensity: {
      control: { type: 'radio' },
      options: ['bold', 'subtle'],
    },
    orientation: {
      control: { type: 'radio' },
      options: ['horizontal', 'vertical'],
    },
  },
};
