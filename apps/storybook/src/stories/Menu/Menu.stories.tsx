import React from 'react';
import { StoryFn, Meta } from '@storybook/react';
import { MenuItem as NovaMenuItem } from '@hxnova/react-components/MenuItem';
import { Button } from '@hxnova/react-components/Button';
import { Typography } from '@hxnova/react-components/Typography';
import { Menu as NovaMenu, MenuProps } from '@hxnova/react-components/Menu';
import { ListItemContent } from '@hxnova/react-components/ListItemContent';
import { ListItemDecorator } from '@hxnova/react-components/ListItemDecorator';
import { Icon } from '@hxnova/icons';

export default {
  title: '@hxnova/react-components/Menu',
  component: NovaMenu,
  parameters: {
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/tZgUauJZLgk0pX7rHCfxKt/NOVA-Core-Components?node-id=4594-10074&t=GLVtMVxVPH63zvEu-0',
    },
  },
  tags: ['!autodocs'],
} as Meta<typeof NovaMenu>;

const MenuItemTemplate: StoryFn<(props: MenuProps) => JSX.Element> = (args) => {
  return (
    <NovaMenu open density={args.density}>
      <NovaMenuItem {...args}>
        <ListItemDecorator>
          <Icon family="material" name="person" size={24} />
        </ListItemDecorator>
        <ListItemContent>My account</ListItemContent>
        <ListItemDecorator>
          <Typography variant="bodySmall">⌘C</Typography>
          <Icon family="material" name="keyboard_arrow_right" size={24} />
        </ListItemDecorator>
      </NovaMenuItem>
    </NovaMenu>
  );
};

export const MenuItem = {
  render: MenuItemTemplate,
  args: {
    density: 'standard',
    selected: false,
    disabled: false,
  },
  argTypes: {
    density: {
      control: { type: 'select' },
      options: ['compact', 'standard', 'comfortable'],
    },
  },
};

const MenuTemplate: StoryFn<(props: MenuProps) => JSX.Element> = (args) => {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <>
      <Button
        id="basic-button"
        style={{ margin: '300px' }}
        aria-controls={open ? 'basic-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        onClick={handleClick}
      >
        Open menu
      </Button>
      <NovaMenu {...args} anchorEl={anchorEl} open={open} onClose={handleClose} disablePortal>
        <NovaMenuItem>
          <ListItemDecorator>
            <Icon family="material" name="person" size={24} />
          </ListItemDecorator>
          <ListItemContent>My account</ListItemContent>
          <ListItemDecorator>
            <Typography variant="bodySmall">⌘C</Typography>
            <Icon family="material" name="keyboard_arrow_right" size={24} />
          </ListItemDecorator>
        </NovaMenuItem>
        <NovaMenuItem>
          <ListItemDecorator>
            <Icon family="material" name="person" size={24} />
          </ListItemDecorator>
          <ListItemContent>My account</ListItemContent>
        </NovaMenuItem>
        <NovaMenuItem>Logout</NovaMenuItem>
      </NovaMenu>
    </>
  );
};

export const Menu = {
  render: MenuTemplate,
  args: {
    density: 'standard',
    placement: 'auto',
  },
  argTypes: {
    density: {
      control: { type: 'select' },
      options: ['compact', 'standard', 'comfortable'],
    },
    placement: {
      control: { type: 'select' },
      options: [
        'top',
        'bottom',
        'right',
        'left',
        'auto',
        'top-start',
        'top-end',
        'bottom-start',
        'bottom-end',
        'right-start',
        'right-end',
        'left-start',
        'left-end',
        'auto',
        'auto-start',
        'auto-end',
      ],
    },
  },
  parameters: {
    controls: {
      include: ['density', 'placement'],
    },
  },
};
