import React from 'react';
import { But<PERSON> } from '@hxnova/react-components/Button';
import { ListItemDecorator } from '@hxnova/react-components/ListItemDecorator';
import { Menu } from '@hxnova/react-components/Menu';
import { MenuItem } from '@hxnova/react-components/MenuItem';
import { Divider } from '@hxnova/react-components/Divider';
import { Icon } from '@hxnova/icons';

export default function BasicMenu() {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <div>
      <Button
        id="basic-button"
        aria-controls={open ? 'basic-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        onClick={handleClick}
      >
        Dashboard
        <Icon family="material" name="more_vert" size={24} />
      </Button>
      <Menu placement="bottom-end" anchorEl={anchorEl} open={open} onClose={handleClose}>
        <MenuItem onClick={handleClose}>
          <ListItemDecorator>
            <Icon family="material" name="edit" size={24} />
          </ListItemDecorator>
          Edit post
        </MenuItem>
        <MenuItem disabled>
          <ListItemDecorator />
          Draft post
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleClose}>
          <ListItemDecorator>
            <Icon family="material" name="delete_forever" size={24} />
          </ListItemDecorator>
          Delete
        </MenuItem>
      </Menu>
    </div>
  );
}
