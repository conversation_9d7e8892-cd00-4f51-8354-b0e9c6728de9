import { Pagination } from '@hxnova/react-components/Pagination';
import { PaginationItem } from '@hxnova/react-components/PaginationItem';
import { Icon } from '@hxnova/icons';
const ArrowBackIcon = () => <Icon family="material" name="arrow_back" size={24} />;
const ArrowForwardIcon = () => <Icon family="material" name="arrow_forward" size={24} />;

export default function CustomIconsExample() {
  return (
    <div sx={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
      <div>
        <h4>Custom navigation icons</h4>
        <Pagination
          count={10}
          renderItem={(item) => (
            <PaginationItem
              slots={{
                previous: ArrowBackIcon,
                next: ArrowForwardIcon,
              }}
              {...item}
            />
          )}
        />
      </div>
    </div>
  );
}
