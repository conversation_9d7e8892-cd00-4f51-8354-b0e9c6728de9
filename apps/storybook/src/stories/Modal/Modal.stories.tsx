import { useState } from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import { Modal, ModalProps } from '@hxnova/react-components/Modal';
import { Button } from '@hxnova/react-components/Button';
import { Box } from '@hxnova/react-components/Box';
import { Typography } from '@hxnova/react-components/Typography';
import { Card } from '@hxnova/react-components/Card';

const meta = {
  title: '@hxnova/react-components/Utils/Modal',
  component: Modal,
  parameters: {
    layout: 'centered',
  },
  tags: ['!autodocs'],
} satisfies Meta<typeof Modal>;

export default meta;

export const Default: StoryObj<Omit<ModalProps, 'children' | 'open'>> = {
  render: function ModalStory(args) {
    const [open, setOpen] = useState(false);

    return (
      <Box sx={{ position: 'relative', minWidth: '400px', minHeight: '200px' }}>
        <Button onClick={() => setOpen(true)}>Open Modal</Button>
        <Modal {...args} open={open} onClose={() => setOpen(false)}>
          <Card.Root
            sx={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              width: 400,
              maxWidth: '90vw',
              padding: '24px',
              outline: 'none',
            }}
          >
            <Typography variant="headlineSmall" sx={{ marginBottom: '16px' }}>
              Modal Title
            </Typography>
            <Typography variant="bodyMedium" sx={{ marginBottom: '24px' }}>
              This is a modal dialog. You can include any content here. The modal handles focus management, keyboard
              navigation, and click-away behavior automatically.
            </Typography>
            <Box sx={{ display: 'flex', gap: '8px', justifyContent: 'flex-end' }}>
              <Button variant="outlined" onClick={() => setOpen(false)}>
                Cancel
              </Button>
              <Button onClick={() => setOpen(false)}>Confirm</Button>
            </Box>
          </Card.Root>
        </Modal>
      </Box>
    );
  },
  args: {
    closeAfterTransition: false,
    disableAutoFocus: false,
    disableEnforceFocus: false,
    disableEscapeKeyDown: false,
    disablePortal: false,
    disableRestoreFocus: false,
    disableScrollLock: false,
    hideBackdrop: false,
    keepMounted: false,
  },
  argTypes: {
    disableEscapeKeyDown: {
      control: 'boolean',
      description: 'Disable closing on escape key press',
    },
    hideBackdrop: {
      control: 'boolean',
      description: 'Hide the backdrop',
    },
  },
  parameters: {
    controls: {
      include: ['disableEscapeKeyDown', 'hideBackdrop'],
    },
  },
};
