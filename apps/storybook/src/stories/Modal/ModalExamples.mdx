import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';
import ModalBasicExample from './Examples/ModalBasicExample';
import ModalBasicExampleSource from './Examples/ModalBasicExample.tsx?raw';
import ModalNestedExample from './Examples/ModalNestedExample';
import ModalNestedExampleSource from './Examples/ModalNestedExample.tsx?raw';
import ModalServerSideExample from './Examples/ModalServerSideExample';
import ModalServerSideExampleSource from './Examples/ModalServerSideExample.tsx?raw';
import ModalOptionsExample from './Examples/ModalOptionsExample';
import ModalOptionsExampleSource from './Examples/ModalOptionsExample.tsx?raw';

<Meta title="@hxnova/react-components/Utils/Modal/Examples" />

## Basic Modal

If you are creating a modal dialog, you probably want to use the Dialog component rather than directly using Modal. Modal is a lower-level construct that is leveraged by Dialog, Drawer, Menu and <PERSON>over.

The Modal component provides a foundation for creating dialogs, popups, and overlays. The **`open` prop** controls the modal's visibility, and the **`onClose` prop** handles close events from backdrop clicks or escape key presses. The modal automatically manages focus and provides accessibility features like focus trapping and keyboard navigation.

For accessibility, be sure to add **`aria-labelledby`** and **`aria-describedby`** props to reference the modal title and description. The modal automatically adds appropriate ARIA roles and manages focus properly.

<div className="sb-unstyled">
  <ModalBasicExample />
</div>
<CodeExpand code={ModalBasicExampleSource} showBorderTop style={{marginTop: 16}}/>

## Nested Modal

Modals can be nested, for example a select within a dialog, but stacking of more than two modals, or any two modals with a backdrop is discouraged. The Modal component properly manages the stacking order and focus management for nested scenarios.

<div className="sb-unstyled">
  <ModalNestedExample />
</div>
<CodeExpand code={ModalNestedExampleSource} showBorderTop style={{marginTop: 16}}/>

## Server-side Modal

React doesn't support the `createPortal()` API on the server. In order to display the modal during server-side rendering, you need to disable the portal feature with the **`disablePortal`** prop. This renders the modal as a child of its parent component instead of portaling it to the document body.

<div className="sb-unstyled">
  <ModalServerSideExample />
</div>
<CodeExpand code={ModalServerSideExampleSource} showBorderTop style={{marginTop: 16}}/>

## Modal Options

The Modal component offers various configuration options to customize its behavior. You can hide the backdrop with **`hideBackdrop`** and disable escape key handling with **`disableEscapeKeyDown`**. These options provide flexibility for different use cases while maintaining accessibility standards.

<div className="sb-unstyled">
  <ModalOptionsExample />
</div>
<CodeExpand code={ModalOptionsExampleSource} showBorderTop style={{marginTop: 16}}/> 