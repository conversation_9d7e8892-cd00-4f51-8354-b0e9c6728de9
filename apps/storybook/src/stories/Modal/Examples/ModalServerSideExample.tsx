import { useState } from 'react';
import { Modal } from '@hxnova/react-components/Modal';
import { Button } from '@hxnova/react-components/Button';
import { Box } from '@hxnova/react-components/Box';
import { Typography } from '@hxnova/react-components/Typography';
import { Card } from '@hxnova/react-components/Card';

export default function ModalServerSideExample() {
  const [open, setOpen] = useState(false);

  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <div sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '16px' }}>
      <Button onClick={handleOpen}>Open Server-side Modal</Button>
      <Modal
        open={open}
        onClose={handleClose}
        disablePortal
        aria-labelledby="server-modal-title"
        aria-describedby="server-modal-description"
      >
        <Card.Root
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: 400,
            maxWidth: '90vw',
            padding: '24px',
            outline: 'none',
            border: '1px solid var(--palette-divider)',
          }}
        >
          <Typography id="server-modal-title" variant="headlineSmall" sx={{ marginBottom: '16px' }}>
            Server-side Modal
          </Typography>
          <Typography id="server-modal-description" variant="bodyMedium" sx={{ marginBottom: '16px' }}>
            This modal uses the disablePortal prop, making it compatible with server-side rendering. The modal content
            is rendered as a child of its parent component instead of being portaled to the document body.
          </Typography>
          <Typography variant="bodySmall" sx={{ marginBottom: '24px', color: 'text.secondary' }}>
            If you disable JavaScript, you will still see this modal content in the DOM.
          </Typography>
          <Box sx={{ display: 'flex', gap: '8px', justifyContent: 'flex-end' }}>
            <Button onClick={handleClose}>Close</Button>
          </Box>
        </Card.Root>
      </Modal>
    </div>
  );
}
