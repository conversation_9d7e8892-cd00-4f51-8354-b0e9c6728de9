import { useState } from 'react';
import { Modal } from '@hxnova/react-components/Modal';
import { Button } from '@hxnova/react-components/Button';
import { Box } from '@hxnova/react-components/Box';
import { Typography } from '@hxnova/react-components/Typography';
import { Card } from '@hxnova/react-components/Card';

export default function ModalBasicExample() {
  const [basicOpen, setBasicOpen] = useState(false);

  return (
    <div sx={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <Typography variant="titleSmall" sx={{ marginBottom: '16px' }}>
        Basic Modal
      </Typography>
      <div sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '16px' }}>
        <Button onClick={() => setBasicOpen(true)}>Open Basic Modal</Button>
        <Modal
          open={basicOpen}
          onClose={() => setBasicOpen(false)}
          aria-labelledby="basic-modal-title"
          aria-describedby="basic-modal-description"
        >
          <Card.Root
            sx={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              width: 400,
              maxWidth: '90vw',
              padding: '24px',
              outline: 'none',
            }}
          >
            <Typography id="basic-modal-title" variant="headlineSmall" sx={{ marginBottom: '16px' }}>
              Text in a modal
            </Typography>
            <Typography id="basic-modal-description" variant="bodyMedium" sx={{ marginBottom: '24px' }}>
              This modal demonstrates proper ARIA labeling, focus management, and keyboard navigation (try pressing
              ESC).
            </Typography>
            <Box sx={{ display: 'flex', gap: '8px', justifyContent: 'flex-end' }}>
              <Button variant="outlined" onClick={() => setBasicOpen(false)}>
                Close
              </Button>
            </Box>
          </Card.Root>
        </Modal>
      </div>
    </div>
  );
}
