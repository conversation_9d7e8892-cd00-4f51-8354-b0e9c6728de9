import { useState } from 'react';
import { Modal } from '@hxnova/react-components/Modal';
import { Button } from '@hxnova/react-components/Button';
import { Box } from '@hxnova/react-components/Box';
import { Typography } from '@hxnova/react-components/Typography';
import { Card } from '@hxnova/react-components/Card';

// Child modal component
function ChildModal() {
  const [open, setOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState('');

  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleSelect = (value: string) => {
    setSelectedOption(value);
    setOpen(false);
  };

  return (
    <>
      <Button onClick={handleOpen} variant="outlined" sx={{ marginTop: '16px' }}>
        Open Child Modal
      </Button>
      <Modal
        open={open}
        onClose={handleClose}
        aria-labelledby="child-modal-title"
        aria-describedby="child-modal-description"
      >
        <Card.Root
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: 300,
            maxWidth: '90vw',
            padding: '24px',
            outline: 'none',
          }}
        >
          <Typography id="child-modal-title" variant="headlineSmall" sx={{ marginBottom: '16px' }}>
            Select an Option
          </Typography>
          <Typography id="child-modal-description" variant="bodyMedium" sx={{ marginBottom: '16px' }}>
            Choose one of the options below:
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: '12px', marginBottom: '24px' }}>
            <Button onClick={() => handleSelect('Option 1')} variant="outlined" fullWidth>
              Option 1
            </Button>
            <Button onClick={() => handleSelect('Option 2')} variant="outlined" fullWidth>
              Option 2
            </Button>
            <Button onClick={() => handleSelect('Option 3')} variant="outlined" fullWidth>
              Option 3
            </Button>
          </Box>
          <Box sx={{ display: 'flex', gap: '8px', justifyContent: 'flex-end' }}>
            <Button onClick={handleClose}>Close</Button>
          </Box>
        </Card.Root>
      </Modal>
      {selectedOption && (
        <Typography variant="bodySmall" sx={{ marginTop: '8px', color: 'text.secondary' }}>
          Selected: {selectedOption}
        </Typography>
      )}
    </>
  );
}

export default function ModalNestedExample() {
  const [open, setOpen] = useState(false);

  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <div sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '16px' }}>
      <Button onClick={handleOpen}>Open Nested Modal</Button>
      <Modal
        open={open}
        onClose={handleClose}
        aria-labelledby="parent-modal-title"
        aria-describedby="parent-modal-description"
      >
        <Card.Root
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: 400,
            maxWidth: '90vw',
            padding: '24px',
            outline: 'none',
          }}
        >
          <Typography id="parent-modal-title" variant="headlineSmall" sx={{ marginBottom: '16px' }}>
            Parent Modal
          </Typography>
          <Typography id="parent-modal-description" variant="bodyMedium" sx={{ marginBottom: '16px' }}>
            This is the parent modal that contains a child modal for additional interactions.
          </Typography>
          <ChildModal />
          <Box sx={{ display: 'flex', gap: '8px', justifyContent: 'flex-end', marginTop: '24px' }}>
            <Button variant="outlined" onClick={handleClose}>
              Close
            </Button>
          </Box>
        </Card.Root>
      </Modal>
    </div>
  );
}
