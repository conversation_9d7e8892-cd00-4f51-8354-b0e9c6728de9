import { useState } from 'react';
import { Modal } from '@hxnova/react-components/Modal';
import { Button } from '@hxnova/react-components/Button';
import { Box } from '@hxnova/react-components/Box';
import { Typography } from '@hxnova/react-components/Typography';
import { Card } from '@hxnova/react-components/Card';

export default function ModalOptionsExample() {
  const [noBackdropOpen, setNoBackdropOpen] = useState(false);
  const [noEscapeOpen, setNoEscapeOpen] = useState(false);

  return (
    <div sx={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <div>
        <Typography variant="titleSmall" sx={{ marginBottom: '16px' }}>
          Backdrop Options
        </Typography>
        <div sx={{ display: 'flex', gap: '16px', justifyContent: 'center' }}>
          <Button onClick={() => setNoBackdropOpen(true)}>No Backdrop</Button>
        </div>

        <Modal open={noBackdropOpen} onClose={() => setNoBackdropOpen(false)} hideBackdrop>
          <Card.Root
            sx={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              width: 400,
              maxWidth: '90vw',
              padding: '24px',
              outline: 'none',
              border: '2px solid var(--palette-primary)',
            }}
          >
            <Typography variant="headlineSmall" sx={{ marginBottom: '16px' }}>
              No Backdrop Modal
            </Typography>
            <Typography variant="bodyMedium" sx={{ marginBottom: '16px' }}>
              This modal has no backdrop. The background content remains visible and interactive.
            </Typography>
            <Box sx={{ display: 'flex', gap: '8px', justifyContent: 'flex-end' }}>
              <Button onClick={() => setNoBackdropOpen(false)}>Close</Button>
            </Box>
          </Card.Root>
        </Modal>
      </div>

      <div>
        <Typography variant="titleSmall" sx={{ marginBottom: '16px' }}>
          Keyboard Options
        </Typography>
        <div sx={{ display: 'flex', gap: '16px', justifyContent: 'center' }}>
          <Button onClick={() => setNoEscapeOpen(true)}>Disable ESC Key</Button>
        </div>

        <Modal open={noEscapeOpen} onClose={() => setNoEscapeOpen(false)} disableEscapeKeyDown>
          <Card.Root
            sx={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              width: 400,
              maxWidth: '90vw',
              padding: '24px',
              outline: 'none',
            }}
          >
            <Typography variant="headlineSmall" sx={{ marginBottom: '16px' }}>
              ESC Disabled Modal
            </Typography>
            <Typography variant="bodyMedium" sx={{ marginBottom: '16px' }}>
              This modal cannot be closed with the ESC key. Try pressing ESC - it will not work. You must use the Close
              button or click the backdrop.
            </Typography>
            <Box sx={{ display: 'flex', gap: '8px', justifyContent: 'flex-end' }}>
              <Button onClick={() => setNoEscapeOpen(false)}>Close</Button>
            </Box>
          </Card.Root>
        </Modal>
      </div>
    </div>
  );
}
