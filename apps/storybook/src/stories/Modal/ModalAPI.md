# API Documentation

- [Modal](#modal)

# Modal

API reference docs for the React Modal component. Learn about the props, CSS, and other APIs of this exported module.

## Import

To use the `Modal` component, you can choose to import it directly or through the main entry point.

```jsx
import { Modal } from '@hxnova/react-components/Modal';
// or
import { Modal } from '@hxnova/react-components';
```

## Props

The properties available for the `Modal` component. Props of the native component are also available.

| Name | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| **children*** | ``ReactElement<unknown, string ⏐ JSXElementConstructor<any>>`` | - | A single child content element. |
| **open*** | ``false ⏐ true`` | - | If `true`, the component is shown. |
| **classes** | ``Partial<ModalClasses>`` | - | Override or extend the styles applied to the component. |
| **className** | `string` | - | @ignore |
| **closeAfterTransition** | ``false ⏐ true`` | `false` | When set to true the Modal waits until a nested Transition is completed before closing. |
| **component** | `ElementType` | - | The root node component, which can be specified as either<br>a string representing an HTML tag or a React component. |
| **container** | ``null ⏐ Element ⏐ () => Element ⏐ null`` | - | An HTML element or function that returns one.<br>The `container` will have the portal children appended to it.<br>You can also provide a callback, which is called in a React layout effect.<br>This lets you set the container from a ref, and also makes server-side rendering possible.<br>By default, it uses the body of the top-level document object,<br>so it's simply `document.body` most of the time. |
| **disableAutoFocus** | ``false ⏐ true`` | `false` | If `true`, the modal will not automatically shift focus to itself when it opens, and<br>replace it to the last focused element when it closes.<br>This also works correctly with any modal children that have the `disableAutoFocus` prop.<br>Generally this should never be set to `true` as it makes the modal less<br>accessible to assistive technologies, like screen readers. |
| **disableEnforceFocus** | ``false ⏐ true`` | `false` | If `true`, the modal will not prevent focus from leaving the modal while open.<br>Generally this should never be set to `true` as it makes the modal less<br>accessible to assistive technologies, like screen readers. |
| **disableEscapeKeyDown** | ``false ⏐ true`` | `false` | If `true`, hitting escape will not fire the `onClose` callback. |
| **disablePortal** | ``false ⏐ true`` | `false` | The `children` will be under the DOM hierarchy of the parent component. |
| **disableRestoreFocus** | ``false ⏐ true`` | `false` | If `true`, the modal will not restore focus to previously focused element once<br>modal is hidden or unmounted. |
| **disableScrollLock** | ``false ⏐ true`` | `false` | Disable the scroll lock behavior. |
| **hideBackdrop** | ``false ⏐ true`` | `false` | If `true`, the backdrop is not rendered. |
| **keepMounted** | ``false ⏐ true`` | `false` | Always keep the children in the DOM.<br>This prop can be useful in SEO situation or<br>when you want to maximize the responsiveness of the Modal. |
| **onClose** | ``(event: object, reason: "backdropClick" ⏐ "escapeKeyDown") => void`` | - | Callback fired when the component requests to be closed.<br>The `reason` parameter can optionally be used to control the response to `onClose`.<br>@param event The event source of the callback.<br>@param reason Can be: `"escapeKeyDown"`, `"backdropClick"`. |
| **onTransitionEnter** | ``() => void`` | - | A function called when a transition enters. |
| **onTransitionExited** | ``() => void`` | - | A function called when a transition has exited. |
| **slotProps** | ``{ root?: SlotComponentProps<"div", ModalComponentsPropsOverrides, ModalOwnerState> ⏐ undefined; backdrop?: SlotComponentProps<...> ⏐ undefined; }`` | `{}` | The props used for each slot inside the Modal. |
| **slots** | `ModalSlots` | `{}` | The components used for each slot inside the Modal.<br>Either a string to use a HTML element or a component. |
| **sx** | `SxProps` | - | The system prop that allows defining system overrides as well as additional CSS styles. |

## Slots

Slots allow for more granular control over the rendering of specific parts of the `Modal` component.

| Slot name | Class name | Default component | Description |
| --------- | ---------- | ----------------- | ----------- |
| root | .NovaModal-root | `'div'` | The component that renders the root. |
| backdrop | .NovaModal-backdrop | `'div'` | The component that renders the backdrop. |

## CSS classes

CSS classes for different states and variations of the `Modal` component.

| Class name | Rule name | Description |
| ---------- | --------- | ----------- |
| .NovaModal-hidden | `hidden` | Class name applied to the root element if the `Modal` has exited. |

