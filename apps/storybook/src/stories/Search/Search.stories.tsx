import { <PERSON><PERSON>n, <PERSON>a, StoryObj } from '@storybook/react';
import { IconButton } from '@hxnova/react-components/IconButton';
import { Search, SearchProps } from '@hxnova/react-components/Search';
import { Icon } from '@hxnova/icons';

const meta = {
  title: '@hxnova/react-components/Search',
  component: Search,
  parameters: {
    layout: 'centered',
    design: {
      type: 'figma',
      url: 'https://www.figma.com/design/tZgUauJZLgk0pX7rHCfxKt/NOVA-Core-Components?node-id=1071-34055&t=8gsCBxylYIAHAwKy-4',
    },
  },
  tags: ['!autodocs'],
} satisfies Meta<typeof Search>;

export default meta;

type Story = StoryObj<typeof meta>;

const Template: StoryFn<(props: SearchProps) => JSX.Element> = (args) => {
  const StartDecorator = <Icon family="material" name="search" size={24} />;
  const EndDecorator = (
    <IconButton variant="neutral" size={args.size}>
      <Icon family="material" name="mic_none" size={24} />
    </IconButton>
  );

  return <Search {...args} startDecorator={StartDecorator} endDecorator={EndDecorator} />;
};

export const Basic: Story = {
  render: Template,
  args: {
    size: 'medium',
    fullWidth: false,
    placeholder: 'Hinted search text',
    autoFocus: false,
    defaultValue: '',
    style: { minWidth: '360px' },
  },
  argTypes: {
    size: {
      control: { type: 'radio' },
      options: ['small', 'medium', 'large'],
    },
  },
};
