import React, { useCallback, useMemo } from 'react';
import { ColumnType, DataGrid } from '@hxnova/react-components/DataGrid';
import { Icon } from '@hxnova/icons';
import { IconButton } from '@hxnova/react-components';

type DataType = {
  id: number;
  firstName: string;
  lastName: string;
};

const columns: ColumnType<DataType>[] = [
  { field: 'id', header: 'ID', width: 150 },
  { field: 'firstName', header: 'First Name', width: 200 },
  { field: 'lastName', header: 'Last Name', width: 200 },
  {
    field: 'fullName',
    header: 'Full Name',
    width: 250,
    cell: (row) => `${row.firstName} ${row.lastName}`,
    sortable: false,
  },
  {
    field: 'actions',
    minWidth: 100,
    flex: 1,
    align: 'end',
    cell: (row: DataType) => (
      <IconButton
        variant="neutral"
        onClick={() => {
          console.log(row.id);
        }}
      >
        <Icon family="material" name="more_vert" size={24} />
      </IconButton>
    ),
    sortable: false,
  },
];
const data: DataType[] = [
  { id: 1, firstName: 'Tony', lastName: 'Smith' },
  { id: 2, firstName: 'Isla', lastName: 'Fletcher' },
  { id: 3, firstName: 'Evie', lastName: 'Easton' },
  { id: 4, firstName: 'Liam', lastName: 'Johnson' },
  { id: 5, firstName: 'Ava', lastName: 'Brown' },
  { id: 6, firstName: 'Noah', lastName: 'Williams' },
];
export default function DataGridDemo() {
  const initialState = useMemo(
    () => ({
      selectedRows: [2, 4],
    }),
    [],
  );
  const isRowSelectable = useCallback((row: DataType) => {
    return row.id !== 5;
  }, []);
  return (
    <DataGrid
      columns={columns}
      data={data}
      rowSelectionMode="checkboxSelection"
      initialState={initialState}
      isRowSelectable={isRowSelectable}
    />
  );
}
