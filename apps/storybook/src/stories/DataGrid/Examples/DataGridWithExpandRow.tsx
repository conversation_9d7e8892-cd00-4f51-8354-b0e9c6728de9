import { useCallback } from 'react';
import { ColumnType, DataGrid } from '@hxnova/react-components/DataGrid';
import { Typography } from '@hxnova/react-components/Typography';
import { Icon } from '@hxnova/icons';
import { Tag } from '@hxnova/react-components/Tag';
import { Link } from '@hxnova/react-components/Link';

type DataType = {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  address: string;
  status: 'active' | 'pending' | 'deactivated';
};

const columns: ColumnType<DataType>[] = [
  { field: 'id', header: 'ID', width: 150 },
  { field: 'firstName', header: 'First Name', width: 200 },
  { field: 'lastName', header: 'Last Name', width: 200 },
  {
    field: 'status',
    header: 'Status',
    width: 200,
    cell: (paras: DataType) => (
      <Tag
        intensity="subtle"
        variant={paras.status === 'active' ? 'success' : paras.status === 'pending' ? 'warning' : 'error'}
        label={`${paras.status.charAt(0).toUpperCase()}${paras.status.substring(1, paras.status.length)}`}
      />
    ),
  },
];

const data: DataType[] = [
  {
    id: 1,
    firstName: 'Tony',
    lastName: 'Smith',
    email: '<EMAIL>',
    address: '123 Main St',
    status: 'active',
  },
  {
    id: 2,
    firstName: 'Isla',
    lastName: 'Fletcher',
    email: '<EMAIL>',
    address: '456 Elm St',
    status: 'deactivated',
  },
  {
    id: 3,
    firstName: 'Evie',
    lastName: 'Easton',
    email: '<EMAIL>',
    address: '789 Oak St',
    status: 'active',
  },
  {
    id: 4,
    firstName: 'Liam',
    lastName: 'Johnson',
    email: '<EMAIL>',
    address: '101 Pine St',
    status: 'active',
  },
  {
    id: 5,
    firstName: 'Ava',
    lastName: 'Brown',
    email: '<EMAIL>',
    address: '102 Maple St',
    status: 'active',
  },
  {
    id: 6,
    firstName: 'Noah',
    lastName: 'Williams',
    email: '<EMAIL>',
    address: '103 Birch St',
    status: 'active',
  },
];

export default function DataGridDemo() {
  const isRowExpandable = useCallback((row: DataType) => {
    return row.status === 'active';
  }, []);

  const expandedRowPanelRender = useCallback((row: DataType) => {
    return (
      <div
        sx={{
          height: '100%',
          padding: '24px',
          backgroundColor: 'var(--palette-background-paper)',
          display: 'grid',
          gridTemplateColumns: '1fr 1fr',
          gap: '32px',
          borderTop: '1px solid var(--palette-divider)',
        }}
      >
        <div sx={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
          <Typography variant="titleMedium" sx={{ marginBottom: '8px' }}>
            Contact Information
          </Typography>

          <div sx={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <Icon family="material" name="email" size="24" color="primary" />
            <div>
              <Typography variant="bodyMedium" color="textSecondary">
                Email
              </Typography>
              <Typography variant="bodyMedium">{row.email}</Typography>
            </div>
          </div>

          <div sx={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <Icon family="material" name="location_on" size="24" color="primary" />
            <div>
              <Typography variant="bodyMedium" color="textSecondary">
                Address
              </Typography>
              <Typography variant="bodyMedium">{row.address}</Typography>
            </div>
          </div>
        </div>
        {row.id !== 1 && (
          <div
            sx={{
              gridColumn: '1 / -1',
              borderTop: '1px solid var(--palette-divider)',
            }}
          >
            <Typography variant="titleMedium" sx={{ marginBottom: '8px' }}>
              Recent Activity
            </Typography>
            <div sx={{ display: 'flex', gap: '24px' }}>
              <div sx={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <Icon family="material" name="shopping_cart" size="24" />
                <Typography variant="bodyMedium">3 recent orders</Typography>
              </div>
              <div sx={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <Icon family="material" name="support_agent" size="24" />
                <Typography variant="bodyMedium">2 support tickets</Typography>
              </div>
            </div>
            <DataGrid
              sx={{ marginTop: '8px' }}
              columns={[
                { field: 'id', header: 'Order ID', width: 300, cell: (row) => <Link href="#">{row.id}</Link> },
                { field: 'event', header: 'Event', flex: 1 },
              ]}
              data={[
                { id: 'order-001', event: 'Created a ticket for #122.' },
                { id: 'order-002', event: 'Added user James A to Group A.' },
                { id: 'order-003', event: 'Removed user James B from Group B.' },
              ]}
              pagination={false}
            />
          </div>
        )}
      </div>
    );
  }, []);

  const getExpandedRowHeight = useCallback((row: DataType) => {
    return row.id === 1 ? 200 : 400;
  }, []);

  return (
    <DataGrid
      columns={columns}
      data={data}
      isRowExpandable={isRowExpandable}
      expandedRowPanelRender={expandedRowPanelRender}
      getExpandedRowHeight={getExpandedRowHeight}
    />
  );
}
