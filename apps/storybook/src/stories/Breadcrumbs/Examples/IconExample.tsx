import { Breadcrumbs } from '@hxnova/react-components/Breadcrumbs';
import { Link } from '@hxnova/react-components/Link';
import { Typography } from '@hxnova/react-components/Typography';
import { Icon } from '@hxnova/icons';

const commonLinkStyles = {
  color: 'var(--palette-onSurfaceVariant)',
};

const primaryTextStyles = {
  color: 'var(--palette-primary)',
};

export default function IconExample() {
  return (
    <Breadcrumbs aria-label="breadcrumbs">
      <Link
        underline="hover"
        sx={commonLinkStyles}
        href="#"
        startDecorator={<Icon family="material" name="home" size={24} />}
      >
        Home
      </Link>
      <Link underline="hover" sx={commonLinkStyles} href="#">
        Category
      </Link>
      <Typography sx={primaryTextStyles}>Current Page</Typography>
    </Breadcrumbs>
  );
}
