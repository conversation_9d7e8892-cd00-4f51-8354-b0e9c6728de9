{"name": "storybook", "private": true, "version": "0.0.0", "type": "module", "scripts": {"build": "storybook build", "lint": "eslint .", "storybook": "storybook dev -p 6006"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.3.1"}, "devDependencies": {"@hxnova/react-components": "workspace:*", "@hxnova/themes": "workspace:*", "@hxnova/icons": "workspace:^", "@hxnova/templates": "workspace:*", "@nexusui/branding": "^2.8.1", "@chromatic-com/storybook": "^3.2.2", "@eslint/js": "^9.15.0", "codesandbox": "2.2.3", "@mui/system": "^6.2.0", "@storybook/addon-actions": "^8.4.7", "@storybook/addon-essentials": "^8.4.7", "@storybook/addon-interactions": "^8.4.7", "@storybook/addon-onboarding": "^8.4.7", "@storybook/blocks": "^8.4.7", "@storybook/manager-api": "^8.4.7", "@storybook/react": "^8.4.7", "@storybook/react-vite": "^8.4.7", "@storybook/test": "^8.4.7", "@storybook/theming": "^8.4.7", "@types/autosuggest-highlight": "^3.2.3", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "@pigment-css/react": "0.0.30", "@pigment-css/vite-plugin": "~0.0.30", "@fontsource/roboto": "^5.0.12", "@fontsource/roboto-mono": "^5.0.17", "autosuggest-highlight": "^3.3.4", "eslint": "^9.15.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "eslint-plugin-storybook": "^0.11.1", "globals": "^15.12.0", "raw-loader": "^4.0.2", "storybook": "^8.4.7", "typescript": "~5.6.2", "typescript-eslint": "^8.15.0", "vite": "^6.0.1", "@storybook/addon-a11y": "8.4.7", "@storybook/addon-designs": "^8.0.4", "@storybook/addon-docs": "^8.4.4", "remark-gfm": "^4.0.0", "dayjs": "^1.10.7", "react-is": "^18.2.0", "prop-types": "^15.8.1", "storybook-dark-mode": "4.0.1"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}}