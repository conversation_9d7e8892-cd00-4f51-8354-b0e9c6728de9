node_modules
dist
build
.vscode
.nx
apps/storybook/storybook-static
.eslintcache

**/**/test-results/
**/**/playwright-report/
**/**/blob-report/
**/**/playwright/.cache/
**/**/*.log
**/**/.DS_Store
**/**/dist
**/**/coverage
packages/nova-templates/**/package-lock.json
packages/nova-templates/**/yarn.lock
packages/nova-templates/**/pnpm-lock.yaml

# Added by Task Master AI
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
.env
# Editor directories and files
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
.DS_Store