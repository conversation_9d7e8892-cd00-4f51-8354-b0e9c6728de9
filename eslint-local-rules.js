/**
 * Custom ESLint rules for PigmentCSS project
 */

module.exports = {
  rules: {
    'no-ternary-in-sx': {
      meta: {
        type: 'problem',
        docs: {
          description: 'Disallow ternary operators in sx prop for PigmentCSS compatibility',
          category: 'Possible Errors',
          recommended: false,
        },
        fixable: null,
        schema: [],
        messages: {
          noTernaryInSx:
            'Ternary operators are not supported in sx prop when using PigmentCSS. Consider using style or css variable instead.',
        },
      },
      create(context) {
        function checkObjectExpression(node, isSxProp = false) {
          if (node.type === 'ObjectExpression') {
            node.properties.forEach((prop) => {
              if (prop.type === 'Property') {
                checkForTernary(prop.value, isSxProp);
              }
            });
          }
        }

        function checkForTernary(node, isSxProp = false) {
          if (!node) return;

          if (node.type === 'ConditionalExpression' && isSxProp) {
            context.report({
              node,
              messageId: 'noTernaryInSx',
            });

            return;
          }

          // Recursively check nested structures
          if (node.type === 'ObjectExpression') {
            checkObjectExpression(node, isSxProp);
          } else if (node.type === 'ArrayExpression') {
            node.elements.forEach((element) => {
              if (element) {
                checkForTernary(element, isSxProp);
              }
            });
          }
        }

        return {
          JSXAttribute(node) {
            // Check if this is an sx prop
            if (node.name && node.name.name === 'sx' && node.value) {
              if (node.value.type === 'JSXExpressionContainer') {
                checkForTernary(node.value.expression, true);
              }
            }
          },
        };
      },
    },
  },
};
